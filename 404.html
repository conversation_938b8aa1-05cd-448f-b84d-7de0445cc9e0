<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, follow">
    <title>Page Not Found | NP Labs Compounding Pharmacy</title>
    <meta name="description" content="Oops! The page you're looking for seems to have been compounded into thin air. Return to the NP Labs homepage to explore our compounding pharmacy services.">
    <meta name="keywords" content="page not found, 404 error, NP Labs, compounding pharmacy">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Page Not Found | NP Labs Compounding Pharmacy">
    <meta property="og:description" content="Oops! The page you're looking for seems to have been compounded into thin air. Return to the NP Labs homepage to explore our compounding pharmacy services.">
    <meta property="og:image" content="https://www.nplabs.com/images/nplabs-logo-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/404.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Page Not Found | NP Labs Compounding Pharmacy">
    <meta name="twitter:description" content="Oops! The page you're looking for seems to have been compounded into thin air. Return to the NP Labs homepage to explore our compounding pharmacy services.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/nplabs-logo-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="/core.css">
    <link rel="stylesheet" href="/components.css">
    <link rel="stylesheet" href="/sections.css">
    <link rel="stylesheet" href="/footer.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Custom styles for 404 page */
        .page-header.error-hero {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/images/quality-assurance.png') no-repeat center center/cover;
        }

        /* Critical base styles */
        :root {
            --primary-blue: #00509e;
            --primary-blue-dark: #003b75;
            --secondary-teal: #00a896;
            --dark-grey: #333333;
            --medium-grey: #666666;
            --light-grey: #f5f7fa;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Lato', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--dark-grey);
            line-height: 1.6;
            background-color: #fff;
        }

        .header {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            text-align: center;
        }

        .header img {
            max-width: 200px;
            height: auto;
        }

        .error-container {
            text-align: center;
            padding: 60px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .error-code {
            font-size: 120px;
            font-weight: 900;
            color: var(--primary-blue);
            margin-bottom: 0;
            line-height: 1;
            position: relative;
            display: inline-block;
        }

        .error-code::after {
            content: "Rx";
            position: absolute;
            top: 15px;
            right: -40px;
            font-size: 30px;
            color: var(--secondary-teal);
            transform: rotate(20deg);
        }

        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary-blue);
        }

        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: var(--dark-grey);
        }

        .error-prescription {
            background-color: #f5f7fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            border: 1px dashed var(--primary-blue);
        }

        .error-prescription::before {
            content: "Rx";
            position: absolute;
            top: -15px;
            left: 20px;
            background-color: var(--secondary-teal);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .error-prescription h3 {
            color: var(--primary-blue);
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .error-prescription ul {
            text-align: left;
            margin-bottom: 0;
            padding-left: 20px;
        }

        .error-prescription li {
            margin-bottom: 10px;
            position: relative;
            padding-left: 25px;
            list-style-type: none;
        }

        .error-prescription li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--secondary-teal);
            font-weight: bold;
        }

        .error-image {
            max-width: 300px;
            margin: 30px auto;
        }

        .search-container {
            max-width: 500px;
            margin: 0 auto 30px;
        }

        .search-form {
            display: flex;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid var(--medium-grey);
            border-radius: 4px;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .search-input:focus {
            border-color: var(--primary-blue);
            outline: none;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 700;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-speed);
            margin: 5px;
        }

        .btn-primary {
            background-color: var(--primary-blue);
            color: white;
            border: 2px solid var(--primary-blue);
        }

        .btn-primary:hover {
            background-color: var(--primary-blue-dark);
            border-color: var(--primary-blue-dark);
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
        }

        .btn-secondary:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        .footer {
            background-color: var(--primary-blue);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-top: 60px;
        }

        .footer p {
            margin: 5px 0;
        }

        .footer a {
            color: white;
            text-decoration: underline;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .error-code {
                font-size: 100px;
            }

            .error-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .error-code {
                font-size: 80px;
            }

            .error-title {
                font-size: 1.8rem;
            }

            .search-form {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>

<!-- Header - Directly embedded -->
<header class="site-header">
    <div class="container header-container">
        <div class="logo">
            <a href="/index.html" aria-label="NPLabs Homepage">
                <img src="/nplabslogo.svg" alt="NPLabs Logo" onerror="this.src='/images/logo.png'; this.onerror='';">
            </a>
        </div>

        <nav class="main-nav" id="main-nav">
            <button class="mobile-nav-close" id="mobile-nav-close" aria-label="Close navigation">×</button>
            <ul class="nav-items">
                <li class="nav-item has-mega-menu">
                    <div class="mega-menu-wrapper">
                        <span class="nav-link menu-trigger">Who We Are <i class="fas fa-chevron-down fa-xs"></i></span>
                        <div class="mega-menu">
                            <div class="menu-items-grid">
                                <a href="/about.html" class="menu-item">About Us</a>
                                <a href="/careers.html" class="menu-item">Careers</a>
                                <a href="/our-team.html" class="menu-item">Our Team</a>
                                <a href="/quality-assurance.html" class="menu-item">Quality Assurance</a>
                                <a href="/our-philosophy.html" class="menu-item">Our Philosophy</a>
                                <a href="/where-we-ship.html" class="menu-item">Where We Ship</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/our-services.html">Our Services</a>
                </li>
                <li class="nav-item"><a href="/blog/index.html" class="nav-link">Health & Wellness Blog</a></li>
                <li class="mobile-only-nav-item"><a href="/login.html" class="btn btn-outline">Login</a></li>
                <li class="mobile-only-nav-item"><a href="/register.html" class="btn btn-secondary">Register Portal</a></li>
            </ul>
        </nav>

        <div class="user-actions">
            <a href="/login.html" class="btn btn-outline">Login</a>
            <a href="/register.html" class="btn btn-secondary">Register Portal</a>
        </div>

        <button class="mobile-nav-toggle" id="mobile-nav-toggle" aria-label="Toggle navigation" aria-expanded="false" aria-controls="main-nav">
            <i class="fas fa-bars"></i>
        </button>
    </div>
</header>

<main id="main-content">
    <!-- 404 Error Section -->
    <section class="page-header about-hero error-hero">
        <div class="page-header-overlay"></div>
        <div class="container">
            <h1 data-aos="fade-up">404</h1>
            <p data-aos="fade-up" data-aos-delay="200">Page Not Found</p>
        </div>
    </section>

    <section class="content-section">
        <div class="container">
            <div class="error-container">
                <h2 class="error-title" data-aos="fade-up">This Page Has Been Compounded Into Thin Air!</h2>
                <p class="error-message" data-aos="fade-up" data-aos-delay="100">
                    Oops! It seems the page you're looking for has been dissolved, filtered, or simply doesn't exist in our formula.
                </p>

            <div class="error-image" data-aos="fade-up" data-aos-delay="200">
                <svg width="300px" height="300px" viewBox="0 0 300 300" version="1.1" xmlns="http://www.w3.org/2000/svg">
                    <title>404 Empty Beaker</title>
                    <defs>
                        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="beakerGradient">
                            <stop stop-color="#00A896" stop-opacity="0.2" offset="0%"></stop>
                            <stop stop-color="#00509E" stop-opacity="0.1" offset="100%"></stop>
                        </linearGradient>
                    </defs>
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <!-- Beaker Base -->
                        <path d="M80,60 L80,240 C80,260 100,280 120,280 L180,280 C200,280 220,260 220,240 L220,60" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>

                        <!-- Beaker Top -->
                        <path d="M70,60 L230,60" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>

                        <!-- Beaker Neck -->
                        <path d="M100,60 L100,40 L200,40 L200,60" stroke="#00509E" stroke-width="4"></path>

                        <!-- Beaker Rim -->
                        <path d="M90,40 L210,40" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>

                        <!-- Measurement Lines -->
                        <path d="M90,100 L110,100" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
                        <path d="M90,140 L110,140" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
                        <path d="M90,180 L110,180" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
                        <path d="M90,220 L110,220" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>

                        <!-- Measurement Numbers -->
                        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="104">100</text>
                        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="144">200</text>
                        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="184">300</text>
                        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="224">400</text>

                        <!-- Beaker Interior (Glass Effect) -->
                        <path d="M85,65 L85,240 C85,257 98,275 120,275 L180,275 C202,275 215,257 215,240 L215,65 Z" fill="url(#beakerGradient)"></path>

                        <!-- 404 Text (as if written on the beaker) -->
                        <text font-family="Arial" font-size="24" font-weight="bold" fill="#00509E" opacity="0.3" x="130" y="170" text-anchor="middle">404</text>
                    </g>
                </svg>
            </div>

            <div class="error-prescription" data-aos="fade-up" data-aos-delay="300">
                <h3>Your Prescription for Recovery:</h3>
                <ul>
                    <li><strong>Dosage:</strong> One click on the home button</li>
                    <li><strong>Frequency:</strong> As needed for navigation</li>
                    <li><strong>Side Effects:</strong> May cause sudden relief and improved user experience</li>
                    <li><strong>Special Instructions:</strong> If symptoms of lost pages persist, please contact our support team</li>
                </ul>
            </div>

            <div class="search-container" data-aos="fade-up" data-aos-delay="400">
                <form class="search-form" action="/" method="get">
                    <input type="text" class="search-input" placeholder="Try searching for what you need..." aria-label="Search">
                    <button type="submit" class="btn btn-primary">Search</button>
                </form>
            </div>

            <div class="cta-buttons" data-aos="fade-up" data-aos-delay="500">
                <a href="/" class="btn btn-primary">Return to Home</a>
                <a href="/contact.html" class="btn btn-secondary">Contact Us</a>
            </div>
        </div>
    </section>
</main>

<!-- Footer - Directly embedded -->
<footer class="site-footer">
    <div class="container">
        <div class="footer-grid">
            <div class="footer-column about-column">
                <span class="footer-brand-text">NPLabs Compounding Pharmacy</span>
                <p>Dedicated to pioneering functional medicine and delivering personalized care worldwide.</p>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div class="footer-column links-column">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="/index.html">Home</a></li>
                    <li><a href="/about.html">About Us</a></li>
                    <li><a href="/our-services.html">Our Services</a></li>
                    <li><a href="/contact.html">Contact</a></li>
                </ul>
            </div>

            <div class="footer-column links-column">
                <h4>Support</h4>
                <ul class="footer-links">
                    <li><a href="#">F.A.Q.</a></li>
                    <li><a href="/privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="/terms-of-use.html">Terms of Use</a></li>
                    <li><a href="#">Cookie Policy</a></li>
                </ul>
            </div>

            <div class="footer-column contact-column">
                <h4>Contact Us</h4>
                <p><i class="fas fa-map-marker-alt"></i> Athens, Greece</p>
                <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <!-- Add Phone if available -->
                <!-- <p><i class="fas fa-phone"></i> <a href="tel:+123456789">****** 567 89</a></p> -->
            </div>
        </div>

        <div class="footer-bottom">
          <p class="footer-copyright" style="font-size:11px !important; font-weight:600 !important;">
            &copy; 2025 NPLABS.ONLINE. All Rights Reserved
          </p>
        </div>
    </div>
</footer>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>

<!-- Navigation Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile navigation toggle
        const mobileNavToggle = document.getElementById('mobile-nav-toggle');
        const mobileNavClose = document.getElementById('mobile-nav-close');
        const mainNav = document.getElementById('main-nav');

        if (mobileNavToggle) {
            mobileNavToggle.addEventListener('click', function() {
                mainNav.classList.add('active');
                document.body.style.overflow = 'hidden';
                mobileNavToggle.setAttribute('aria-expanded', 'true');
            });
        }

        if (mobileNavClose) {
            mobileNavClose.addEventListener('click', function() {
                mainNav.classList.remove('active');
                document.body.style.overflow = '';
                mobileNavToggle.setAttribute('aria-expanded', 'false');
            });
        }

        // Mega menu functionality
        const menuTriggers = document.querySelectorAll('.menu-trigger');

        menuTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                const parent = this.closest('.has-mega-menu');

                // Close all other open menus
                document.querySelectorAll('.has-mega-menu').forEach(item => {
                    if (item !== parent) {
                        item.classList.remove('active');
                    }
                });

                // Toggle current menu
                parent.classList.toggle('active');
                e.stopPropagation();
            });
        });

        // Close mega menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.has-mega-menu')) {
                document.querySelectorAll('.has-mega-menu').forEach(item => {
                    item.classList.remove('active');
                });
            }
        });
    });
</script>

</body>
</html>

# NP Labs Meta Tags Implementation Guide

This guide provides step-by-step instructions for implementing proper meta tags across all pages of the NP Labs website to improve SEO performance.

## Implementation Process

### Step 1: Update Pages Missing Description Tags

First, add meta description tags to the following pages:

1. **blog-entry-perimenopause.html**
   ```html
   <meta name="description" content="Learn about the perimenopause transition, symptoms, and personalized treatment options from NP Labs' compounding pharmacy experts.">
   ```

2. **emailjs-template.html**
   ```html
   <meta name="description" content="NP Labs email template - not for public viewing.">
   ```

3. **hormone-tracker.html**
   ```html
   <meta name="description" content="Track your hormone levels and symptoms with NP Labs' interactive hormone tracking tool. Personalized insights for better hormone health.">
   ```

4. **perimenopause-transition.html**
   ```html
   <meta name="description" content="Understand the perimenopause transition and discover personalized compounded solutions from NP Labs to manage symptoms and support your health.">
   ```

### Step 2: Add Advanced Meta Tags to Main Pages

Start by updating the most important pages with all recommended meta tags:

#### Homepage (index.html)

```html
<!-- Basic Meta Tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>NP Labs | Personalized Compounded Medicine for Your Unique Needs</title>
<meta name="description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Securely manage prescriptions online. Learn more & register today.">
<meta name="keywords" content="compounding pharmacy, personalized medicine, custom medications, prescription compounding, NP Labs, Athens Greece">

<!-- Canonical URL -->
<link rel="canonical" href="https://www.nplabs.com/">

<!-- Open Graph Tags for Social Media -->
<meta property="og:title" content="NP Labs | Personalized Compounded Medicine">
<meta property="og:description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Learn more & register today.">
<meta property="og:image" content="https://www.nplabs.com/images/og-image.jpg">
<meta property="og:url" content="https://www.nplabs.com/">
<meta property="og:type" content="website">
<meta property="og:site_name" content="NP Labs">

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="NP Labs | Personalized Compounded Medicine">
<meta name="twitter:description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Learn more & register today.">
<meta name="twitter:image" content="https://www.nplabs.com/images/twitter-image.jpg">
```

#### About Us (about.html)

```html
<!-- Basic Meta Tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>About NP Labs: Expert Compounding Pharmacy in Athens, Greece</title>
<meta name="description" content="Discover NP Labs' mission to provide personalized compounded medications. Our expert team in Athens, Greece is committed to creating custom pharmaceutical solutions for your unique health needs.">
<meta name="keywords" content="compounding pharmacy, about NP Labs, personalized medicine team, custom medication experts, Athens Greece">

<!-- Canonical URL -->
<link rel="canonical" href="https://www.nplabs.com/about.html">

<!-- Open Graph Tags for Social Media -->
<meta property="og:title" content="About NP Labs: Expert Compounding Pharmacy">
<meta property="og:description" content="Discover NP Labs' mission to provide personalized compounded medications. Our expert team is committed to creating custom pharmaceutical solutions.">
<meta property="og:image" content="https://www.nplabs.com/images/og-image.jpg">
<meta property="og:url" content="https://www.nplabs.com/about.html">
<meta property="og:type" content="website">
<meta property="og:site_name" content="NP Labs">

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="About NP Labs: Expert Compounding Pharmacy">
<meta name="twitter:description" content="Discover NP Labs' mission to provide personalized compounded medications. Our expert team is committed to creating custom pharmaceutical solutions.">
<meta name="twitter:image" content="https://www.nplabs.com/images/twitter-image.jpg">
```

### Step 3: Update Service Pages

For each service page, use specific keywords and descriptions related to that service:

#### Women's Health (womens-health.html)

```html
<!-- Basic Meta Tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Women's Health Compounding Services | NP Labs</title>
<meta name="description" content="Specialized compounded medications for women's health issues including hormone therapy, menopause management, and reproductive health from NP Labs.">
<meta name="keywords" content="women's health, hormone therapy, bioidentical hormones, menopause treatment, compounded medications for women, BHRT">

<!-- Canonical URL -->
<link rel="canonical" href="https://www.nplabs.com/womens-health.html">

<!-- Open Graph Tags for Social Media -->
<meta property="og:title" content="Women's Health Compounding Services | NP Labs">
<meta property="og:description" content="Specialized compounded medications for women's health issues including hormone therapy, menopause management, and reproductive health.">
<meta property="og:image" content="https://www.nplabs.com/images/womens-health-og.jpg">
<meta property="og:url" content="https://www.nplabs.com/womens-health.html">
<meta property="og:type" content="website">
<meta property="og:site_name" content="NP Labs">

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Women's Health Compounding Services | NP Labs">
<meta name="twitter:description" content="Specialized compounded medications for women's health issues including hormone therapy, menopause management, and reproductive health.">
<meta name="twitter:image" content="https://www.nplabs.com/images/womens-health-twitter.jpg">
```

### Step 4: Update Blog Pages

For blog pages, include specific keywords related to the blog post topic:

#### Blog Index (blog/index.html)

```html
<!-- Basic Meta Tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Health & Wellness Blog | Compounding Pharmacy Insights | NP Labs</title>
<meta name="description" content="Explore expert insights on health, wellness, and personalized medicine from NP Labs' compounding pharmacy specialists. Evidence-based articles on hormone health, pediatric care, and more.">
<meta name="keywords" content="compounding pharmacy blog, health and wellness, personalized medicine, hormone health, NP Labs blog">

<!-- Canonical URL -->
<link rel="canonical" href="https://www.nplabs.com/blog/">

<!-- Open Graph Tags for Social Media -->
<meta property="og:title" content="Health & Wellness Blog | NP Labs">
<meta property="og:description" content="Explore expert insights on health, wellness, and personalized medicine from NP Labs' compounding pharmacy specialists.">
<meta property="og:image" content="https://www.nplabs.com/images/blog-og.jpg">
<meta property="og:url" content="https://www.nplabs.com/blog/">
<meta property="og:type" content="website">
<meta property="og:site_name" content="NP Labs">

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Health & Wellness Blog | NP Labs">
<meta name="twitter:description" content="Explore expert insights on health, wellness, and personalized medicine from NP Labs' compounding pharmacy specialists.">
<meta name="twitter:image" content="https://www.nplabs.com/images/blog-twitter.jpg">
```

#### Blog Post (blog/posts/pediatric-compounding.html)

```html
<!-- Basic Meta Tags -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Pediatric Compounding: Making Medicine Child-Friendly | NP Labs Blog</title>
<meta name="description" content="Discover how compounding pharmacies are addressing the unique medication needs of children, making medicine more effective, safer, and easier to administer.">
<meta name="keywords" content="pediatric compounding, children's medications, flavored medicine, custom dosages for children, medication for kids">

<!-- Canonical URL -->
<link rel="canonical" href="https://www.nplabs.com/blog/posts/pediatric-compounding.html">

<!-- Open Graph Tags for Social Media -->
<meta property="og:title" content="Pediatric Compounding: Making Medicine Child-Friendly">
<meta property="og:description" content="Discover how compounding pharmacies are addressing the unique medication needs of children, making medicine more effective, safer, and easier to administer.">
<meta property="og:image" content="https://www.nplabs.com/blog/assets/images/Pediatric-Compounding-1.png">
<meta property="og:url" content="https://www.nplabs.com/blog/posts/pediatric-compounding.html">
<meta property="og:type" content="article">
<meta property="og:site_name" content="NP Labs">
<meta property="article:published_time" content="2023-03-15T08:00:00+00:00">
<meta property="article:author" content="NP Labs Team">

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Pediatric Compounding: Making Medicine Child-Friendly">
<meta name="twitter:description" content="Discover how compounding pharmacies are addressing the unique medication needs of children, making medicine more effective, safer, and easier to administer.">
<meta name="twitter:image" content="https://www.nplabs.com/blog/assets/images/Pediatric-Compounding-1.png">
```

### Step 5: Create Social Media Images

Create standardized Open Graph and Twitter Card images:

1. **Main OG Image**: Create a 1200×630 pixel image featuring the NP Labs logo and tagline
2. **Twitter Card Image**: Create a 1200×600 pixel image featuring the NP Labs logo and tagline
3. **Service-Specific Images**: Create custom images for each service category
4. **Blog Post Images**: Use the main blog post image for OG and Twitter tags

### Step 6: Test Implementation

After implementing the meta tags:

1. Use the [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/) to test Open Graph tags
2. Use the [Twitter Card Validator](https://cards-dev.twitter.com/validator) to test Twitter Card tags
3. Use [Google's Rich Results Test](https://search.google.com/test/rich-results) to test structured data
4. Use [SEO Meta in 1 Click](https://chrome.google.com/webstore/detail/seo-meta-in-1-click/bjogjfinolnhfhkbipphpdlldadpnmhc) Chrome extension to quickly check meta tags

## Page-Specific Meta Tag Recommendations

The following table provides recommended meta tags for each main page:

| Page | Title | Description | Keywords |
|------|-------|-------------|----------|
| index.html | NP Labs \| Personalized Compounded Medicine for Your Unique Needs | NP Labs offers custom-compounded medications tailored to your specific health requirements. Securely manage prescriptions online. Learn more & register today. | compounding pharmacy, personalized medicine, custom medications, prescription compounding, NP Labs, Athens Greece |
| about.html | About NP Labs: Expert Compounding Pharmacy in Athens, Greece | Discover NP Labs' mission to provide personalized compounded medications. Our expert team in Athens, Greece is committed to creating custom pharmaceutical solutions for your unique health needs. | compounding pharmacy, about NP Labs, personalized medicine team, custom medication experts, Athens Greece |
| our-services.html | Custom Compounding Services & Specialized Medications - NP Labs | NP Labs offers specialized compounding services including custom medications, hormone therapy, pain management solutions, and more. Get personalized pharmaceutical care in Athens, Greece. | compounding services, custom medications, hormone therapy, pain management, specialized pharmacy services |
| contact.html | Contact NP Labs: Expert Compounding Pharmacy Services | Get in touch with NP Labs' compounding pharmacy team in Athens, Greece. Questions about custom medications? Contact us for personalized pharmaceutical solutions. | contact compounding pharmacy, NP Labs contact, custom medication questions, pharmacy consultation |
| womens-health.html | Women's Health Compounding Services \| NP Labs | Specialized compounded medications for women's health issues including hormone therapy, menopause management, and reproductive health from NP Labs. | women's health, hormone therapy, bioidentical hormones, menopause treatment, compounded medications for women, BHRT |
| mens-health.html | Men's Health Compounding Services \| NP Labs | Customized medications for men's health including hormone optimization, sexual health, and prostate support from NP Labs compounding pharmacy. | men's health, testosterone therapy, hormone optimization, sexual health, prostate support, custom medications for men |

## Conclusion

Implementing these meta tag recommendations across all pages will significantly improve the SEO performance of the NP Labs website. Remember to:

1. Use unique, descriptive titles for each page
2. Keep descriptions under 160 characters
3. Include relevant keywords naturally
4. Create high-quality images for social sharing
5. Test implementation using the recommended tools

For any questions or assistance with implementation, please contact the web development team.

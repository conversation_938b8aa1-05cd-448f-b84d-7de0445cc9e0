# Product Requirements Document: NP Labs Website

## 1. Introduction

### 1.1 Product Definition

The NP Labs website is a platform for NP Labs, a company specializing in personalized compounded medicines. It serves as the primary interface for potential and existing customers (patients and prescribers) to learn about the company's offerings and manage their healthcare needs.

### 1.2 Purpose

The website's primary purpose is to:

*   **Showcase NP Labs:** Effectively communicate the company's mission, services, and the benefits of personalized compounded medicine.
*   **Build Trust:** Establish credibility through clear communication of expertise, experience, and commitment to quality.
*   **Provide Secure Access:** Offer a secure online portal for patients and prescribers to manage prescriptions and orders.
* **Streamline Processes:** Provide a user friendly portal for ordering and re-ordering prescriptions.

### 1.3 Goals

*   **Enhance Brand Recognition:** Increase awareness of NP Labs as a leader in personalized compounded medicine.
*   **Drive Registrations:** Encourage both patients and prescribers to register for the online portal.
*   **Provide a User-Friendly Platform:** Deliver a seamless and efficient experience for managing healthcare online.
*   **Increase Global Reach:** By offering a user friendly website, grow the global reach of the company.
* **Educate Users:** Inform users on the benefits of personalized medicine.

## 2. Target Audience

### 2.1 Patients

*   **Needs:**
    *   Customized medication solutions tailored to their unique biological needs.
    *   Convenient online access to manage prescriptions and orders.
    *   Clear information about the benefits of personalized medicine.
    * Easy registration process.
    * Safe place to upload prescriptions.
*   **Characteristics:**
    *   May be dissatisfied with standard, one-size-fits-all treatments.
    *   Value convenience and personalized care.
    *   Comfortable with using online platforms for healthcare management.

### 2.2 Prescribers

*   **Needs:**
    *   Tools to efficiently manage patient prescriptions.
    *   Secure and reliable platform for handling sensitive information.
    *   Information about NP Labs' compounding process and quality standards.
    * Streamlined communication with NP labs.
*   **Characteristics:**
    *   Doctors, clinicians, or healthcare providers.
    *   Value efficiency and accuracy in managing patient data.
    *   Require a professional and trustworthy online environment.

## 3. Key Features

### 3.1 Public Website (`index.html`)

*   **Homepage:**
    *   Introduction to NP Labs and its mission.
    *   Highlighting the benefits of personalized compounded medicine.
    *   Explanation of the compounding process.
    *   Outline of the online portal's key features.
    *   Clear Calls-to-Action (CTAs) for registration and contact.
*   **Navigation:**
    *   Main menu with links to "Who We Are," "Our Services," "Health & Wellness," and "Blog" (future implementation).
    *   Footer with links to Terms of Use, Privacy Policy, Cookie Policy, and FAQ.
*   **Content Sections:**
    *   Hero section with compelling visuals and messaging.
    *   Detailed explanation of personalized medicine.
    *   Credibility/trust section (years of experience, customers served, countries reached).
    *   Showcase of the compounding facility and process.
    *   Benefits of using the online portal.
    *   CTA blocks for contact and registration.
    *   Newsletter signup section with:
        * User-friendly form with large input fields
        * Clear consent checkbox for GDPR compliance
        * Visually appealing design that works on all devices
        * Personalized success messaging
* **Branding:**
    * Uses `nplabslogo.svg`.
*   **Responsive Design:**
    *   Adaptive layout for different screen sizes, using `picture` elements and CSS media queries.
*   **Accessibility:**
    *   Skip links for screen reader navigation.
    *   ARIA labels for interactive elements.

### 3.2 User Authentication

*   **Login Page (`login.html`):**
    *   Secure access for registered patients and prescribers.
    *   Email and password-based login.
    *   "Forgot Password?" functionality (future implementation).
    *   Link to the registration page.
*   **Registration Page (`register.html`):**
    *   **Account Type Selection:** Clear choice between "Patient" and "Prescriber" registration.
    *   **Patient Registration Form:**
        *   Fields: First Name, Last Name, Email, Date of Birth, Password, Phone Number, Address, City, Country, Post Code.
        *   Password confirmation and basic strength meter.
    *   **Prescriber Registration Form:**
        *   Fields: First Name, Last Name, Email, License Number, Phone Number, Password, Practice/Clinic Address, City, Country, Post Code.
        *   Password confirmation and basic strength meter.
    * Uses password visibility.

### 3.3 Online Portal (Future Implementation)

*   **User Dashboards:** Separate interfaces for patients and prescribers.
*   **Prescription Upload:** Securely upload prescription documents.
*   **Order Management:** Place new orders, view order history, reorder.
*   **Order Tracking:** Monitor order status and delivery progress.
*   **Prescriber Tools:** Specific features for managing patient prescriptions.
*   **Account Management:** Update personal information, addresses, passwords.

### 3.4 Interactivity & UX

*   **Scroll Animations:** Elements animate into view on scroll.
*   **Mobile Navigation:** Hamburger menu and overlay for mobile devices.
*   **Form Validation:** Client-side validation (required fields, email format, password length).
*   **Submission Feedback:** Loading state, success/error messages.
* **Password Visibility:** Ability to toggle password visibility.
* **Password Strength:** Displays the strength of a chosen password.

## 4. Functional Requirements

### 4.1 Public Site

*   **Navigation:** All links must direct to the correct pages or sections.
*   **Content Display:** Content must render correctly across all supported browsers and devices.
*   **Newsletter Signup:**
    * Functionality to collect email addresses and consent, integration with an email marketing service.
    * Larger email input field for better visibility and usability.
    * Mobile-responsive design with full-width inputs on smaller screens.
    * Personalized success messages that extract the user's name from their email address.
    * Light-colored success messages with professional styling and animations.
    * Privacy reassurance messaging to build trust.
    * Consistent implementation across main site and blog pages.
*   **Contact:** Contact forms and links should direct inquiries appropriately.

### 4.2 Authentication

*   **Registration:**
    *   Securely capture and store user data.
    *   Validate all form fields (client-side and server-side): email format, strong password, required fields, date format, phone number format.
    *   Password confirmation must match during registration.
*   **Login:**
    *   Authenticate users against stored credentials.
*   **Password Recovery:**
    *   "Forgot Password" functionality (email reset link).

### 4.3 Portal

*   **Backend Implementation:** Full backend development for all portal features.
*   **Data Security:** Secure handling of user data and PHI.
*   **Role-Based Access Control:**
    *   Patients should only access their own data.
    *   Prescribers should only access data relevant to their role and patients.

### 4.4 General

*   **Form Submission Feedback:**
    * Clear feedback on all form submissions (success, error, validation issues).
    * Loading indicators during form submission.
    * Animated success messages with personalized content.
    * Timed auto-dismissal of success messages with smooth fade-out effects.
    * Error messages with clear instructions for resolution.
*   **Interactivity:** Interactive elements (menus, toggles) must function reliably.

## 5. Non-Functional Requirements

### 5.1 Performance

*   **Page Load Times:** Fast loading times for all pages.
*   **Image Optimization:** Efficient image loading (lazy loading, `picture` element).

### 5.2 Security

*   **Data Handling:** Secure handling of PHI and user credentials (HTTPS, data encryption).
*   **Vulnerability Protection:** Protection against common web vulnerabilities (e.g., XSS, CSRF).
*   **Data Privacy:** Compliance with relevant data privacy regulations (e.g., GDPR, HIPAA if applicable).

### 5.3 Accessibility

*   **WCAG Compliance:** Adherence to WCAG 2.1+ standards.
*   **Semantic HTML:** Proper use of semantic HTML elements.
*   **Keyboard Navigation:** Full keyboard accessibility.
*   **Screen Reader Compatibility:** Support for screen readers.
*   **Color Contrast:** Sufficient color contrast for readability.

### 5.4 Responsiveness

*   **Cross-Device Functionality:** Fully functional and usable experience on desktop, tablet, and mobile devices.

### 5.5 Compatibility

*   **Browser Support:** Support for the latest versions of major browsers (Chrome, Firefox, Safari, Edge).

## 6. Design Considerations

### 6.1 Branding

*   **Color Palette:**
    *   Primary Blue: `#00509e`
    *   Secondary Teal: `#00a896`
    *   Accent Blue: `#0077b6`
    *   Light White: `#ffffff`
    *   Dark Grey: `#222222`
    *   Light Grey: `#f4f7f6`
    *   Medium Grey: `#e0e0e0`
    *   Dark Grey: `#555555`
    *   Error Red: `#d9534f`
    *   Warning Yellow: `#f0ad4e`
    *   Success Green: `#5cb85c`
*   **Logo:** `nplabslogo.svg`.
*   **Typography:** Lato font family.
*   **Icons:** Uses the Font Awesome icon library.

### 6.2 User Interface (UI)

*   **Clean and Professional:** Modern, uncluttered design.
*   **Intuitive Interface:** Easy to navigate, especially for forms and portal dashboards.

### 6.3 User Experience (UX)

*   **Navigation:** Simple and intuitive navigation.
*   **Calls-to-Action:** Clear and prominent CTAs.
*   **Forms:** Minimal friction in registration/login and portal tasks.
*   **Newsletter Experience:**
    * Non-intrusive newsletter signup that encourages participation without being pushy.
    * Visually appealing form with appropriate sizing for all devices.
    * Thoughtful success messaging that builds trust and confirms subscription.
    * Smooth animations and transitions for a polished experience.

## 7. Future Considerations / Out of Scope (Initial Version)

*   **Full Content Implementation:** "Who We Are," "Our Services," "Health & Wellness," and "Blog" pages.
*   **Backend Development:** User authentication, database, portal functionality, prescription processing, order fulfillment integration.
*   **Real Form Submission Logic:** Currently simulated in `script.js`.
*   **Advanced Portal Features:** Communication between patient/prescriber, dosage reminders.
*   **Payment Integration:** Integration with payment gateways.
*   **Multi-Language Support.**
*   **Admin Panel:** For managing users, content, and orders.

# NP Labs Website Technical Documentation

*Last Updated: July 2024*

## Project Overview

NP Labs is a professional website for a compounding pharmacy that specializes in personalized medicine solutions. The website serves as both an informational platform and a portal for patients and prescribers to register and manage their accounts. It also features a comprehensive Health & Wellness Blog that provides educational content about compounding pharmacy, personalized medicine, and health topics.

## Site Architecture

### Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **CSS Organization**: Modular CSS files (core.css, components.css, sections.css, footer.css, hero1.css, terms.css)
- **JavaScript Libraries**:
  - Font Awesome 6.4.0 (Icons)
  - AOS - Animate On Scroll (Animations)
- **Fonts**: Lato (300, 400, 700, 900 weights)
- **Responsive Design**: Mobile-first approach with responsive breakpoints

### File Structure

```
nplabs/
├── css/
│   ├── core.css           # Core styling and variables
│   ├── components.css     # Reusable UI components
│   ├── sections.css       # Section-specific styles
│   ├── footer.css         # Footer styles
│   ├── hero1.css          # Hero section styles
│   ├── terms.css          # Terms and legal page styles
│   └── blog.css           # Blog-specific styles
├── js/
│   ├── include-html.js    # Component inclusion system
│   ├── navigation.js      # Navigation functionality
│   ├── scripts.js         # General site scripts
│   └── blog-filter.js     # Blog category filtering system
├── images/
│   ├── team/              # Team member photos
│   └── various images     # Site imagery and icons
├── blog/
│   ├── assets/
│   │   └── images/        # Blog-specific images
│   ├── posts/             # Individual blog post pages
│   ├── index.html         # Blog home page
│   └── blog-post-template-guide.md  # Template guide for blog posts
├── _header.html           # Reusable header component
├── _footer.html           # Reusable footer component
└── Various HTML pages     # Site pages
```

## Key Design Elements

### Color Palette

- **Primary Blue**: #00509e
- **Secondary Teal**: #00a896
- **Gradient Overlay**: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%)
- **Text Colors**: Dark gray for body text, white for text on dark backgrounds
- **Background Colors**: White for main content, light gray (#f9f9f9) for alternate sections

### Typography

- **Primary Font**: Lato (sans-serif)
- **Heading Sizes**:
  - H1: 3rem, 700 weight
  - H2: 2.5rem, 700 weight
  - H3: 1.75rem, 700 weight
- **Body Text**: 1rem, 400 weight
- **Lead Text**: 1.25rem, 400 weight

### Component System

#### Navigation

- Responsive main navigation with dropdown mega-menus
- Mobile hamburger menu with slide-in animation
- Active state indicators for current page

#### Hero Sections

- Each page uses a standardized hero section with:
  - Background color or image
  - Gradient overlay
  - Centered title and subtitle
  - Consistent padding and spacing

#### Buttons

- Primary: Blue background with white text
- Secondary: White with blue border
- CTA: Larger size with hover effects
- All buttons have consistent padding, border-radius, and hover states

#### Cards

- Service cards, team member cards, and information cards
- Consistent shadow effects and hover interactions
- Standardized padding and margin

## Page Structure

### Common Elements

All pages include:
1. Header with navigation
2. Hero section with page title and description
3. Main content sections
4. Call-to-action section (on most pages)
5. Footer with site links and contact information

### Key Pages

#### Homepage (index.html)

- Hero section with main value proposition
- Services overview with cards
- About section with key facts
- Testimonials carousel
- Partners/clients section
- CTA for registration

#### About Us (about.html)

- Company history and mission
- Team highlights
- Capabilities section
- Values and philosophy summary

#### Our Services (our-services.html)

- Service categories with detailed descriptions
- Benefits of compounded medicine
- Process explanation
- FAQs section

#### Registration Pages

- Separate flows for patients and prescribers
- Form validation
- Privacy policy and terms acceptance

#### Health & Wellness Blog (blog/index.html)

- Featured article section with prominent display
- Category filtering system (All Posts, Health, Wellness, Personalized Medicine, Research)
- Latest articles grid with responsive card layout
- Newsletter subscription form
- Pagination for browsing multiple pages of content

#### Blog Post Pages (blog/posts/)

- Consistent template following blog-post-template-guide.md
- Author information and publication date
- Category tagging system
- Related articles section
- Social sharing options
- Call-to-action for compounding services

#### Legal Pages

- Privacy Policy
- Terms of Use
- Consistent styling with the main site

## Technical Implementation Details

### Component Inclusion System

The site uses a custom component inclusion system via `include-html.js` that:
- Loads header and footer components dynamically
- Maintains consistent navigation across the site
- Allows for centralized updates to common elements

```javascript
// Component inclusion example
document.addEventListener('DOMContentLoaded', function() {
    // Load header
    loadHTML('header-placeholder', '_header.html', initNavigation);
    // Load footer
    loadHTML('footer-placeholder', '_footer.html');
});
```

### Responsive Design Implementation

- Mobile-first CSS with progressive enhancement
- Breakpoints at 576px, 768px, 992px, and 1200px
- Flexible grid system for layout
- Responsive images with appropriate sizing
- Media queries for typography adjustments

```css
/* Example of responsive implementation */
.page-hero h1 {
    font-size: 2rem;
}

@media (min-width: 768px) {
    .page-hero h1 {
        font-size: 3rem;
    }
}
```

### Animation System

- AOS (Animate On Scroll) library integration
- Consistent fade and slide animations
- Performance optimizations for mobile
- Accessibility considerations

```html
<!-- Animation example -->
<div data-aos="fade-up" data-aos-delay="200">
    Content to animate
</div>
```

### Navigation Implementation

- Mega-menu system for desktop
- Slide-in panel for mobile
- JavaScript-based interaction handling
- Accessibility features (ARIA attributes, keyboard navigation)

## Special Features

### Blog Category Filtering System

The blog implements a dynamic category filtering system that allows users to filter posts without page reloads:

- JavaScript-based filtering with smooth animations
- URL parameter support for shareable filtered views
- Post count indicators for each category
- Accessibility features including ARIA attributes and screen reader announcements
- Fallback for users with JavaScript disabled
- Featured article integration with category filtering

```javascript
// Blog filtering initialization
document.addEventListener('DOMContentLoaded', function() {
    // Get all elements
    const categoryLinks = document.querySelectorAll('.category-link');
    const postCards = document.querySelectorAll('.post-card');
    const featuredArticle = document.querySelector('.featured-post-card');

    // Initialize category counts including featured article
    updateCategoryCounts();

    // Initialize from URL parameters
    initializeFromURL();

    // Add event listeners to category links
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.dataset.category;
            filterPosts(category);
            // Update active states and ARIA attributes
            updateActiveState(this);
        });
    });
});
```

### Blog Image Display System

The blog implements a specialized image display system that ensures images are fully visible without being cropped:

- Container-based approach with fixed dimensions
- Object-fit: contain to maintain aspect ratio
- Centered images with flex display
- Light background color for visual consistency
- Subtle padding to prevent images from touching container edges
- Hover animations for interactive feedback

```css
.post-image {
    height: 200px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    padding: 10px;
}

.post-image img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
}
```

### Hero Section Styling

The hero sections use a consistent pattern:
- `.page-hero` base class for structure
- Specific modifier class for each page (e.g., `.about-hero`, `.terms-hero`)
- Pseudo-element for gradient overlay
- Centered content container

```css
.page-hero {
    padding: 0;
    color: white;
    position: relative;
    overflow: hidden;
    background-position: center;
    background-size: cover;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}
```

### Form System

- Consistent form styling across registration and contact forms
- Validation feedback
- Responsive layout adjustments
- Accessibility features

## Accessibility Features

- Semantic HTML structure
- ARIA attributes for interactive elements
- Sufficient color contrast
- Keyboard navigation support
- Screen reader considerations
- Blog filter announcements for screen readers
- Focus management during category filtering
- Skip navigation links
- Fallback content for users with JavaScript disabled
- Respect for user motion preferences

```html
<!-- Example of accessibility features in blog filtering -->
<div class="blog-categories" role="toolbar" aria-label="Blog category filters">
    <a href="#" class="category-link active" data-category="all" role="button" aria-pressed="true" aria-controls="blog-posts-container">All Posts</a>
    <!-- Other category links -->
</div>

<!-- Screen reader announcement for filter changes -->
<div class="sr-only" aria-live="polite" id="filter-announcement">
    Showing all posts
</div>
```

## Performance Optimizations

- Optimized image assets with appropriate dimensions and formats
- CSS organization for efficient loading
- Minimal JavaScript dependencies
- Lazy loading for below-the-fold content
- Efficient blog filtering without page reloads
- Staggered animations to reduce rendering load
- Conditional loading of category-specific content
- Optimized image display system for blog posts
- Reduced motion options for users with performance concerns
- Efficient DOM manipulation in JavaScript

## Development Guidelines

### CSS Naming Conventions

- Component-based naming (e.g., `.card`, `.btn`)
- Modifier classes with descriptive names (e.g., `.btn-primary`)
- Page-specific prefixes for unique styles (e.g., `.about-hero`)

### Blog Post Template System

The blog uses a standardized template system to ensure consistency across all posts:

- Template guide in blog-post-template-guide.md provides detailed instructions
- Consistent HTML structure for all blog posts
- Standard metadata format for categories, dates, and author information
- Predefined content sections (intro, main content, conclusion, CTA)
- Image placement and formatting guidelines
- SEO best practices for blog content

```html
<!-- Example of blog post structure -->
<article class="blog-post">
    <header class="post-header">
        <h1>Post Title</h1>
        <div class="post-meta">
            <span class="post-category">Category</span>
            <span class="post-date">Publication Date</span>
        </div>
    </header>

    <div class="post-content">
        <!-- Structured content sections -->
    </div>

    <footer class="post-footer">
        <!-- Author info, related posts, CTA -->
    </footer>
</article>
```

### JavaScript Practices

- Event delegation for performance
- Modular function organization
- Error handling for component loading
- Progressive enhancement approach
- Asynchronous loading for non-critical resources

### Responsive Design Approach

- Mobile-first CSS
- Consistent breakpoints
- Flexible layouts with percentage-based widths
- Strategic use of min-width media queries

## Deployment Considerations

- All assets are relative paths for easy deployment
- No server-side dependencies
- Cross-browser compatibility
- Performance considerations for various network conditions

## Future Enhancement Opportunities

- Integration with a backend system for user authentication
- Dynamic content management system for blog posts
- Enhanced animation and interaction patterns
- Additional service pages and content expansion
- Blog comment system for user engagement
- Related posts algorithm based on categories and tags
- Email newsletter integration for blog subscribers
- Social media sharing optimization
- Analytics integration for content performance tracking
- Search functionality for blog content

---

This documentation provides a comprehensive overview of the NP Labs website architecture, design system, and implementation details. It should serve as a guide for any developer looking to replicate or extend the site's functionality.

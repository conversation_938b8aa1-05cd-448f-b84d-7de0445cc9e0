# NP Labs SEO Next Steps Action Plan

## Overview

This document outlines the specific next steps and action items for completing the SEO implementation for the NP Labs website. It provides a detailed roadmap for the remaining tasks in each phase of the project.

## Phase 2: Social Media Images Creation

### 1. Convert HTML Templates to Actual Images

**Priority**: High  
**Timeline**: 1-2 days

**Action Items**:
- Use browser screenshot tools to convert HTML templates to PNG images
- Alternative: Recreate designs in Canva or Adobe Express using the template specifications
- Save images in the appropriate directories:
  - Open Graph images: `images/social/og/`
  - Twitter Card images: `images/social/twitter/`

**Resources Needed**:
- Browser with developer tools
- Image editing software (Photoshop, GIMP, or online tools like Canva)
- NP Labs logo in high resolution
- Stock photos or custom images for service pages

### 2. Create Main Site Images

**Priority**: High  
**Timeline**: 1 day

**Action Items**:
- Create Open Graph images (1200×630 pixels) for:
  - Homepage (index.html)
  - About page (about.html)
  - Contact page (contact.html)
  - Services overview page (our-services.html)
  - Blog index page (blog/index.html)
- Create Twitter Card images (1200×600 pixels) for the same pages
- Use consistent branding elements across all images

**Naming Convention**:
- `nplabs-home-og.png`
- `nplabs-about-og.png`
- `nplabs-contact-og.png`
- `nplabs-services-og.png`
- `nplabs-blog-og.png`
- `nplabs-home-twitter.png`
- etc.

### 3. Create Service Page Images

**Priority**: Medium  
**Timeline**: 2-3 days

**Action Items**:
- Create Open Graph and Twitter Card images for all 12 service pages
- Use service-specific imagery and icons
- Include service name in the image
- Maintain consistent branding and layout

**Service Pages**:
- Women's Health
- Men's Health
- Pain Management
- Pediatric Care
- Thyroid Support
- Veterinary Compounding
- Quality Assurance
- Dermatology
- Ophthalmology
- LDN
- Dental Care
- Sports Medicine

### 4. Verify Blog Post Images

**Priority**: Medium  
**Timeline**: 1 day

**Action Items**:
- Check existing blog post featured images for size and quality
- Ensure images are at least 1200×630 pixels for Open Graph
- Optimize images for web (compress without losing quality)
- Create new images if existing ones don't meet requirements

### 5. Implement Images in Meta Tags

**Priority**: High  
**Timeline**: 1-2 days

**Action Items**:
- Update Open Graph image tags in all HTML files:
  ```html
  <meta property="og:image" content="https://www.nplabs.com/images/social/og/nplabs-home-og.png">
  ```
- Update Twitter Card image tags in all HTML files:
  ```html
  <meta name="twitter:image" content="https://www.nplabs.com/images/social/twitter/nplabs-home-twitter.png">
  ```
- Ensure absolute URLs are used for all image paths

## Phase 3: Implementation Testing

### 1. Test Open Graph Tags

**Priority**: High  
**Timeline**: 1 day

**Action Items**:
- Use Facebook Sharing Debugger to test all main pages
- Check for errors or warnings
- Verify images display correctly
- Fix any issues identified

**Testing Process**:
1. Go to [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
2. Enter the URL of each page
3. Click "Debug" to see how the page appears when shared
4. Check for any errors or warnings
5. Click "Scrape Again" to refresh if needed

### 2. Test Twitter Card Tags

**Priority**: High  
**Timeline**: 1 day

**Action Items**:
- Use Twitter Card Validator to test all main pages
- Check for errors or warnings
- Verify images display correctly
- Fix any issues identified

**Testing Process**:
1. Go to [Twitter Card Validator](https://cards-dev.twitter.com/validator)
2. Enter the URL of each page
3. Check for any errors or warnings
4. Verify the correct card type is being used

### 3. Test Structured Data

**Priority**: Medium  
**Timeline**: 1 day

**Action Items**:
- Use Google's Rich Results Test to validate schema markup
- Check for errors or warnings
- Verify structured data is correctly implemented
- Fix any issues identified

**Testing Process**:
1. Go to [Google's Rich Results Test](https://search.google.com/test/rich-results)
2. Enter the URL of each page with schema markup
3. Check for any errors or warnings
4. Verify the structured data is correctly detected

## Phase 4: Additional SEO Improvements

### 1. Implement Schema Markup

**Priority**: High  
**Timeline**: 2-3 days

**Action Items**:
- Add Organization schema to the homepage
- Add Local Business schema to the contact page
- Add Article schema to all blog posts
- Add Service schema to service pages

**Implementation Method**:
- Add schema as JSON-LD in the `<head>` section of each page:
  ```html
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "NP Labs",
    ...
  }
  </script>
  ```

### 2. Submit Sitemap to Google Search Console

**Priority**: Medium  
**Timeline**: 1 day

**Action Items**:
- Create Google Search Console account if not already done
- Verify ownership of the website
- Submit sitemap.xml
- Monitor indexing status

**Submission Process**:
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Select the property (website)
3. Navigate to "Sitemaps" section
4. Enter "sitemap.xml" and click "Submit"

### 3. Optimize Image Alt Text

**Priority**: Medium  
**Timeline**: 2-3 days

**Action Items**:
- Review all images on the website
- Add descriptive alt text to images that are missing it
- Improve existing alt text to be more descriptive and keyword-rich
- Focus on important images first (hero images, service images, etc.)

**Guidelines for Alt Text**:
- Be descriptive and specific
- Include relevant keywords naturally
- Keep it concise (125 characters or less)
- Don't start with "image of" or "picture of"
- Example: `alt="Pharmacist preparing custom medication in NP Labs compounding facility"`

### 4. Verify Heading Structure

**Priority**: Low  
**Timeline**: 1-2 days

**Action Items**:
- Review all pages for proper H1, H2, H3 usage
- Ensure each page has exactly one H1 tag
- Verify logical nesting of headings (H1 > H2 > H3)
- Fix any issues identified

**Heading Structure Guidelines**:
- H1: Main page title (include primary keyword)
- H2: Major section headings (include secondary keywords)
- H3: Sub-section headings
- H4-H6: Further subdivisions as needed
- Avoid skipping heading levels (e.g., H2 to H4)

## Timeline and Dependencies

### Week 1: Social Media Images and Schema Markup
- Day 1-2: Convert HTML templates to actual images
- Day 3-4: Create main site images
- Day 5: Implement images in meta tags for main pages
- Day 1-3: Implement schema markup on main pages

### Week 2: Testing and Remaining Implementations
- Day 1: Test Open Graph and Twitter Card tags
- Day 2: Test structured data
- Day 3-4: Create service page images
- Day 5: Verify blog post images
- Day 3-5: Implement schema markup on remaining pages

### Week 3: Final Optimizations
- Day 1: Submit sitemap to Google Search Console
- Day 2-4: Optimize image alt text
- Day 5: Verify heading structure

## Success Metrics

- **Social Media Sharing**: All pages display correctly when shared on social media
- **Structured Data**: All schema markup validates without errors
- **Sitemap**: Successfully submitted and indexed by Google
- **Image Optimization**: All important images have descriptive alt text
- **Heading Structure**: All pages have proper heading hierarchy

## Conclusion

This action plan provides a detailed roadmap for completing the remaining SEO tasks for the NP Labs website. By following this plan, we will ensure that all aspects of SEO are properly implemented, leading to improved search engine visibility, better social media sharing, and an overall enhanced user experience.

# NP Labs Social Media Images Plan

## Overview

This document outlines the plan for creating and implementing social media images for the NP Labs website. These images will be used in Open Graph and Twitter Card meta tags to enhance the appearance of NP Labs content when shared on social media platforms.

## Image Requirements

### Open Graph Images
- **Dimensions**: 1200×630 pixels
- **Format**: JPG or PNG (PNG preferred for images with text or logos)
- **File Size**: Less than 8MB
- **Aspect Ratio**: 1.91:1
- **Text**: Should occupy less than 20% of the image area
- **Quality**: High resolution, clear, and visually appealing

### Twitter Card Images
- **Dimensions**: 1200×600 pixels (summary_large_image)
- **Dimensions**: 800×800 pixels (summary)
- **Format**: JPG or PNG (PNG preferred for images with text or logos)
- **File Size**: Less than 5MB
- **Aspect Ratio**: 2:1 (summary_large_image), 1:1 (summary)
- **Quality**: High resolution, clear, and visually appealing

## Image Categories

### 1. Main Site Images
- **Home Page**: Showcase the NP Labs facility and compounding expertise
- **About Page**: Feature the team or facility with a professional appearance
- **Contact Page**: Include contact information visually with the NP Labs logo
- **Services Overview**: Visual representation of the range of services offered
- **Blog Index**: Engaging image representing health and wellness content

### 2. Service-Specific Images
- **Women's Health**: Visual representing women's health and hormone balance
- **Men's Health**: Visual representing men's health and vitality
- **Pain Management**: Visual representing pain relief and management
- **Pediatric Care**: Child-friendly image representing pediatric compounding
- **Thyroid Support**: Visual representing thyroid health and support
- **Veterinary Compounding**: Pet-friendly image representing animal care
- **Quality Assurance**: Laboratory or quality control visual
- **Dermatology**: Skin health and dermatological treatments visual
- **Ophthalmology**: Eye health and care visual
- **LDN**: Low Dose Naltrexone therapy visual
- **Dental Care**: Oral health and dental solutions visual
- **Sports Medicine**: Athletic performance and recovery visual

### 3. Blog Post Images
- Use existing blog post featured images
- Ensure they meet the size requirements for Open Graph and Twitter Cards
- Optimize if necessary for better social media appearance

### 4. Legal and Informational Pages
- **Privacy Policy**: Professional, trust-focused image with NP Labs branding
- **Terms of Use**: Professional, trust-focused image with NP Labs branding
- **Our Facility**: High-quality image of the NP Labs facility
- **Our Team**: Group photo or collage of the NP Labs team
- **Our Philosophy**: Visual representing the company's values and approach
- **Where We Ship**: Map or shipping-themed image with NP Labs branding
- **Careers**: Professional workplace image representing career opportunities

### 5. Authentication Pages
- **Login**: Secure, professional image with NP Labs branding
- **Register**: Welcoming, professional image with NP Labs branding
- **Patient Registration**: Patient-focused image with NP Labs branding
- **Prescriber Registration**: Healthcare professional-focused image with NP Labs branding

## Design Guidelines

### Branding Elements
- Include the NP Labs logo in all images
- Use the company's color scheme (blue, white, and complementary colors)
- Maintain consistent typography that matches the website
- Include a subtle branded watermark or footer in each image

### Content Guidelines
- Images should clearly represent the page's content
- Include minimal text (page title or key message)
- Ensure text is legible at smaller sizes
- Use high-quality, professional stock photos or original photography
- Avoid cluttered designs or excessive elements

### Technical Guidelines
- Create master templates for each image category
- Save in both Open Graph and Twitter Card dimensions
- Optimize file size without sacrificing quality
- Use descriptive filenames (e.g., nplabs-womens-health-og.jpg)
- Store in a dedicated images directory

## Implementation Plan

### Phase 1: Template Creation
1. Create master templates for Open Graph images (1200×630 pixels)
2. Create master templates for Twitter Card images (1200×600 pixels)
3. Establish consistent design elements (logo placement, text style, etc.)

### Phase 2: Main Site Images
1. Design and create images for the 5 main pages
2. Implement in the respective HTML files
3. Test sharing on Facebook and Twitter

### Phase 3: Service Pages Images
1. Design and create images for the 12 service pages
2. Implement in the respective HTML files
3. Test sharing on Facebook and Twitter

### Phase 4: Blog and Other Pages
1. Verify and optimize existing blog post images
2. Create images for legal and informational pages
3. Create images for authentication pages
4. Implement in the respective HTML files
5. Test sharing on Facebook and Twitter

## File Organization

```
images/
├── social/
│   ├── og/
│   │   ├── nplabs-home-og.jpg
│   │   ├── nplabs-about-og.jpg
│   │   ├── nplabs-contact-og.jpg
│   │   ├── nplabs-services-og.jpg
│   │   ├── nplabs-blog-og.jpg
│   │   ├── nplabs-womens-health-og.jpg
│   │   └── ...
│   └── twitter/
│       ├── nplabs-home-twitter.jpg
│       ├── nplabs-about-twitter.jpg
│       ├── nplabs-contact-twitter.jpg
│       ├── nplabs-services-twitter.jpg
│       ├── nplabs-blog-twitter.jpg
│       ├── nplabs-womens-health-twitter.jpg
│       └── ...
```

## Testing Process

1. **Facebook Testing**:
   - Use the [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
   - Enter the URL of each page
   - Verify the correct image appears
   - Check for any warnings or errors
   - Force a refresh if needed

2. **Twitter Testing**:
   - Use the [Twitter Card Validator](https://cards-dev.twitter.com/validator)
   - Enter the URL of each page
   - Verify the correct image appears
   - Check for any warnings or errors

3. **LinkedIn Testing**:
   - Create a test post with the URL
   - Verify the correct image appears
   - Delete the test post after verification

## Timeline

- **Week 1**: Template creation and main site images
- **Week 2**: Service pages images
- **Week 3**: Blog and other pages images
- **Week 4**: Testing and refinement

## Resources

- [Facebook Sharing Best Practices](https://developers.facebook.com/docs/sharing/best-practices/)
- [Twitter Cards Documentation](https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards)
- [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)
- [Canva](https://www.canva.com/) - For image creation
- [Adobe Express](https://www.adobe.com/express/) - For image creation
- [TinyPNG](https://tinypng.com/) - For image optimization

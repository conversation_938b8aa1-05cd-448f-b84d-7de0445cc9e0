/**
 * Automated Media Library Test Script
 * Run this in the browser console to test media library functionality
 */

(async function runAutomatedTest() {
    console.log('🚀 Starting automated media library test...');
    
    const results = {
        imageLoading: { passed: 0, failed: 0, errors: [] },
        mediaManager: { passed: 0, failed: 0, errors: [] },
        performance: { loadTimes: [], averageLoadTime: 0 }
    };
    
    // Test images
    const testImages = [
        '../images/blog/Pediatric-Compounding-1.png',
        '../images/blog/hormonebalance.png',
        '../images/blog/ldn-therapy.png',
        '../images/blog/personalized-medication.png',
        '../images/blog/perimenopause-transition.png',
        '../images/blog/bioidentical-hormones.png',
        '../images/blog/gutbrainconnection.png',
        '../images/blog/Low Dose Naltrexone Therapy.png',
        '../images/blog/author-elena.jpg',
        '../images/blog/author-thomas.jpg',
        '../images/blog/default-author.jpg',
        '../images/blog/default-post.svg'
    ];
    
    // Test 1: Image Loading Performance
    console.log('📸 Testing image loading...');
    for (const imagePath of testImages) {
        try {
            const startTime = performance.now();
            await testImageLoad(imagePath);
            const loadTime = performance.now() - startTime;
            
            results.imageLoading.passed++;
            results.performance.loadTimes.push(loadTime);
            console.log(`✅ ${imagePath.split('/').pop()} loaded in ${loadTime.toFixed(2)}ms`);
        } catch (error) {
            results.imageLoading.failed++;
            results.imageLoading.errors.push({ image: imagePath, error: error.message });
            console.error(`❌ ${imagePath.split('/').pop()} failed: ${error.message}`);
        }
    }
    
    // Calculate average load time
    if (results.performance.loadTimes.length > 0) {
        results.performance.averageLoadTime = results.performance.loadTimes.reduce((a, b) => a + b, 0) / results.performance.loadTimes.length;
    }
    
    // Test 2: MediaManager Functionality
    console.log('⚙️ Testing MediaManager functionality...');
    
    // Test MediaManager exists
    try {
        if (typeof MediaManager !== 'undefined') {
            results.mediaManager.passed++;
            console.log('✅ MediaManager object exists');
        } else {
            throw new Error('MediaManager not found');
        }
    } catch (error) {
        results.mediaManager.failed++;
        results.mediaManager.errors.push({ test: 'MediaManager existence', error: error.message });
        console.error(`❌ MediaManager test failed: ${error.message}`);
    }
    
    // Test getExistingMediaFiles
    try {
        const files = await MediaManager.getExistingMediaFiles();
        if (files && files.length > 0) {
            results.mediaManager.passed++;
            console.log(`✅ getExistingMediaFiles returned ${files.length} files`);
        } else {
            throw new Error('No files returned');
        }
    } catch (error) {
        results.mediaManager.failed++;
        results.mediaManager.errors.push({ test: 'getExistingMediaFiles', error: error.message });
        console.error(`❌ getExistingMediaFiles failed: ${error.message}`);
    }
    
    // Test validateImageLoading (if available)
    try {
        if (typeof MediaManager.validateImageLoading === 'function') {
            const validationResults = await MediaManager.validateImageLoading();
            results.mediaManager.passed++;
            console.log(`✅ validateImageLoading: ${validationResults.loaded}/${validationResults.total} images loaded`);
        } else {
            console.warn('⚠️ validateImageLoading function not available');
        }
    } catch (error) {
        results.mediaManager.failed++;
        results.mediaManager.errors.push({ test: 'validateImageLoading', error: error.message });
        console.error(`❌ validateImageLoading failed: ${error.message}`);
    }
    
    // Test utility functions
    try {
        const testSize = MediaManager.formatFileSize(1024);
        const testDate = MediaManager.formatDate(new Date());
        
        if (testSize && testDate) {
            results.mediaManager.passed++;
            console.log('✅ Utility functions (formatFileSize, formatDate) work correctly');
        } else {
            throw new Error('Utility functions returned invalid results');
        }
    } catch (error) {
        results.mediaManager.failed++;
        results.mediaManager.errors.push({ test: 'utility functions', error: error.message });
        console.error(`❌ Utility functions failed: ${error.message}`);
    }
    
    // Generate final report
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    
    const imageSuccessRate = Math.round((results.imageLoading.passed / (results.imageLoading.passed + results.imageLoading.failed)) * 100);
    const managerSuccessRate = Math.round((results.mediaManager.passed / (results.mediaManager.passed + results.mediaManager.failed)) * 100);
    const overallSuccessRate = Math.round(((results.imageLoading.passed + results.mediaManager.passed) / (results.imageLoading.passed + results.imageLoading.failed + results.mediaManager.passed + results.mediaManager.failed)) * 100);
    
    console.log(`🖼️ Image Loading: ${results.imageLoading.passed}/${results.imageLoading.passed + results.imageLoading.failed} (${imageSuccessRate}%)`);
    console.log(`⚙️ Media Manager: ${results.mediaManager.passed}/${results.mediaManager.passed + results.mediaManager.failed} (${managerSuccessRate}%)`);
    console.log(`🎯 Overall Success Rate: ${overallSuccessRate}%`);
    console.log(`⚡ Average Image Load Time: ${results.performance.averageLoadTime.toFixed(2)}ms`);
    
    if (results.performance.averageLoadTime < 3000) {
        console.log('✅ Performance: EXCELLENT (< 3 seconds)');
    } else if (results.performance.averageLoadTime < 5000) {
        console.log('⚠️ Performance: GOOD (< 5 seconds)');
    } else {
        console.log('❌ Performance: NEEDS IMPROVEMENT (> 5 seconds)');
    }
    
    // Report errors
    if (results.imageLoading.errors.length > 0) {
        console.log('\n❌ Image Loading Errors:');
        results.imageLoading.errors.forEach(error => {
            console.log(`  - ${error.image}: ${error.error}`);
        });
    }
    
    if (results.mediaManager.errors.length > 0) {
        console.log('\n❌ Media Manager Errors:');
        results.mediaManager.errors.forEach(error => {
            console.log(`  - ${error.test}: ${error.error}`);
        });
    }
    
    // Final verdict
    if (overallSuccessRate >= 95) {
        console.log('\n🎉 VERDICT: EXCELLENT - Media library is working perfectly!');
    } else if (overallSuccessRate >= 80) {
        console.log('\n✅ VERDICT: GOOD - Media library is working well with minor issues');
    } else if (overallSuccessRate >= 60) {
        console.log('\n⚠️ VERDICT: NEEDS IMPROVEMENT - Some issues need to be addressed');
    } else {
        console.log('\n❌ VERDICT: CRITICAL ISSUES - Major problems need immediate attention');
    }
    
    return results;
    
    // Helper function to test image loading
    function testImageLoad(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load: ${url}`));
            img.src = url;
            
            // Timeout after 10 seconds
            setTimeout(() => {
                reject(new Error(`Timeout loading: ${url}`));
            }, 10000);
        });
    }
})();

// Instructions for manual testing
console.log(`
🔧 MANUAL TESTING INSTRUCTIONS:
1. Copy and paste this entire script into the browser console
2. Press Enter to run the automated test
3. Check the results in the console output
4. For additional testing, visit: http://localhost:8000/admin/media.html?debug=true
5. Click the "Validate Images" button that appears in debug mode
`);

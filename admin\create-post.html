<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Post - NP Labs Blog Admin</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/post-editor.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    
    <!-- Quill Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-brand">
                <img src="../nplabslogo.svg" alt="NP Labs">
                <h1>Blog Admin</h1>
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="posts.html">
                    <i class="fas fa-edit"></i> Posts
                </a>
                <a href="media.html">
                    <i class="fas fa-images"></i> Media
                </a>
                <a href="../blog/" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Blog
                </a>
            </nav>
            
            <div class="admin-user-menu">
                <button class="user-menu-toggle" onclick="AdminAuth.logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-content">
            <div class="post-editor-header">
                <div class="editor-title">
                    <h2>Create New Post</h2>
                    <p>Write and publish a new blog article</p>
                </div>
                
                <div class="editor-actions">
                    <button type="button" class="btn btn-secondary" id="previewBtn">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="button" class="btn btn-outline" id="saveDraftBtn">
                        <i class="fas fa-save"></i> Save Draft
                    </button>
                    <button type="button" class="btn btn-primary" id="publishBtn">
                        <i class="fas fa-paper-plane"></i> Publish
                    </button>
                </div>
            </div>

            <form id="postForm" class="post-editor-form">
                <!-- Basic Information -->
                <div class="editor-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="postTitle">Post Title *</label>
                            <input type="text" id="postTitle" name="title" required 
                                   placeholder="Enter your post title" maxlength="100">
                            <div class="field-help">
                                <span class="char-count">0/100</span>
                                <span class="field-note">This will be used for SEO and social sharing</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="postSlug">URL Slug</label>
                            <input type="text" id="postSlug" name="slug" 
                                   placeholder="auto-generated-from-title">
                            <div class="field-help">
                                <span class="field-note">Leave empty to auto-generate from title</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="postCategory">Category *</label>
                            <select id="postCategory" name="category" required>
                                <option value="">Select a category</option>
                                <option value="health">Health</option>
                                <option value="wellness">Wellness</option>
                                <option value="medicine">Personalized Medicine</option>
                                <option value="research">Research</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="featuredToggle">Featured Post</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="featuredToggle" name="featured">
                                <label for="featuredToggle" class="toggle-label">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-text">Make this the featured post</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="editor-section">
                    <h3>Featured Image</h3>
                    
                    <div class="image-upload-area" id="imageUploadArea">
                        <div class="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h4>Upload Featured Image</h4>
                            <p>Drag and drop an image here, or click to select</p>
                            <p class="upload-specs">Recommended: 1200x630px, JPG/PNG/WebP, max 5MB</p>
                            <input type="file" id="featuredImage" name="featuredImage" 
                                   accept="image/jpeg,image/png,image/webp" hidden>
                        </div>
                        
                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" src="" alt="Featured image preview">
                            <div class="image-actions">
                                <button type="button" class="btn btn-secondary btn-sm" id="changeImageBtn">
                                    <i class="fas fa-edit"></i> Change
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="removeImageBtn">
                                    <i class="fas fa-trash"></i> Remove
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="imageAlt">Image Alt Text *</label>
                        <input type="text" id="imageAlt" name="imageAlt" 
                               placeholder="Describe the image for accessibility" maxlength="125">
                        <div class="field-help">
                            <span class="char-count">0/125</span>
                            <span class="field-note">Important for accessibility and SEO</span>
                        </div>
                    </div>
                </div>

                <!-- Content Editor -->
                <div class="editor-section">
                    <h3>Post Content</h3>
                    
                    <div class="content-editor">
                        <div id="contentEditor" class="quill-editor"></div>
                        <textarea id="contentHidden" name="content" hidden></textarea>
                    </div>
                    
                    <div class="editor-stats">
                        <span id="wordCount">0 words</span>
                        <span id="charCount">0 characters</span>
                        <span id="readingTime">0 min read</span>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="editor-section collapsible">
                    <h3>
                        <button type="button" class="section-toggle" data-target="seoSettings">
                            <i class="fas fa-chevron-down"></i>
                            SEO Settings
                        </button>
                    </h3>
                    
                    <div id="seoSettings" class="collapsible-content">
                        <div class="form-group">
                            <label for="metaDescription">Meta Description</label>
                            <textarea id="metaDescription" name="metaDescription" 
                                      placeholder="Brief description for search engines and social media"
                                      maxlength="160" rows="3"></textarea>
                            <div class="field-help">
                                <span class="char-count">0/160</span>
                                <span class="field-note">Optimal length: 120-160 characters</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="metaKeywords">Keywords</label>
                            <input type="text" id="metaKeywords" name="metaKeywords" 
                                   placeholder="keyword1, keyword2, keyword3">
                            <div class="field-help">
                                <span class="field-note">Separate keywords with commas</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags -->
                <div class="editor-section">
                    <h3>Tags</h3>
                    
                    <div class="form-group">
                        <label for="postTags">Post Tags</label>
                        <div class="tags-input-container">
                            <div class="tags-display" id="tagsDisplay"></div>
                            <input type="text" id="postTags" placeholder="Add tags..." 
                                   autocomplete="off">
                        </div>
                        <div class="field-help">
                            <span class="field-note">Press Enter or comma to add tags</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <!-- Preview Modal -->
    <div id="previewModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Post Preview</h3>
                <button type="button" class="modal-close" onclick="PostEditor.closePreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <iframe id="previewFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/post-editor.js"></script>
    
    <script>
        // Initialize post editor
        document.addEventListener('DOMContentLoaded', function() {
            // Require authentication
            if (!AdminAuth.requireAuth()) {
                return;
            }
            
            // Initialize post editor
            PostEditor.init();
        });
    </script>
</body>
</html>

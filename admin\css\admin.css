/* NP Labs Blog Admin Styles */

/* Admin Body Styles */
.admin-body {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-teal) 100%);
    min-height: 100vh;
    font-family: 'Lato', sans-serif;
    margin: 0;
    padding: 0;
}

/* Login Page Styles */
.admin-login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
}

.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: 2rem;
}

.admin-logo {
    height: 60px;
    margin-bottom: 1rem;
}

.login-header h1 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: var(--dark-grey);
    font-size: 1rem;
    margin-bottom: 0;
}

.login-form {
    text-align: left;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.login-form label {
    display: block;
    color: var(--dark-grey);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.login-form label i {
    margin-right: 0.5rem;
    color: var(--primary-blue);
}

.login-form input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.login-form input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}

.btn-block {
    width: 100%;
    margin-top: 1rem;
}

.login-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--light-grey);
}

.login-footer p {
    color: var(--dark-grey);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.login-footer p i {
    color: var(--secondary-teal);
    margin-right: 0.5rem;
}

.back-to-blog {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-to-blog:hover {
    color: var(--secondary-teal);
}

.back-to-blog i {
    margin-right: 0.5rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-content .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-grey);
    border-top: 4px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-content p {
    color: var(--dark-grey);
    font-weight: 600;
    margin: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Messages */
.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    margin-top: 1rem;
    text-align: center;
}

.success-message {
    background: #efe;
    border: 1px solid #cfc;
    color: #363;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    margin-top: 1rem;
    text-align: center;
}

/* Dashboard Layout */
.admin-dashboard {
    background: var(--light-grey);
    min-height: 100vh;
}

.admin-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.admin-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-brand img {
    height: 40px;
}

.admin-brand h1 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.admin-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.admin-nav a {
    color: var(--dark-grey);
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.admin-nav a:hover,
.admin-nav a.active {
    background: var(--primary-blue);
    color: white;
}

.admin-user-menu {
    position: relative;
}

.user-menu-toggle {
    background: none;
    border: none;
    color: var(--dark-grey);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.user-menu-toggle:hover {
    background: var(--light-grey);
}

/* Main Content Layout */
.admin-main {
    display: flex;
    min-height: calc(100vh - 80px);
}

.admin-sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
}

.admin-content {
    flex: 1;
    padding: 2rem;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--dark-grey);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background: var(--primary-blue);
    color: white;
}

.sidebar-nav i {
    width: 20px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .admin-header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .admin-nav {
        gap: 1rem;
    }
    
    .admin-main {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        order: 2;
    }
    
    .admin-content {
        order: 1;
        padding: 1rem;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 0 1rem;
    }
    
    .sidebar-nav li {
        margin-bottom: 0;
        margin-right: 0.5rem;
        white-space: nowrap;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem 1rem;
    }

    .admin-brand h1 {
        font-size: 1.2rem;
    }

    .admin-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 1200px;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--light-grey);
}

.modal-header h3 {
    color: var(--primary-blue);
    font-weight: 700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark-grey);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: var(--light-grey);
}

.modal-body {
    padding: 0;
    height: 70vh;
}

.modal-body iframe {
    width: 100%;
    height: 100%;
    border: none;
}

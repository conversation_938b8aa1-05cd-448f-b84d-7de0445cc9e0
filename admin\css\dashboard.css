/* Dashboard Specific Styles */

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: var(--dark-grey);
    font-size: 1.1rem;
    margin: 0;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    border: 2px solid transparent;
}

.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
}

.quick-action-card.primary {
    border-color: var(--primary-blue);
}

.quick-action-card.primary:hover {
    background: var(--primary-blue);
    color: white;
}

.quick-action-card.secondary {
    border-color: var(--secondary-teal);
}

.quick-action-card.secondary:hover {
    background: var(--secondary-teal);
    color: white;
}

.quick-action-card.tertiary {
    border-color: #6c757d;
}

.quick-action-card.tertiary:hover {
    background: #6c757d;
    color: white;
}

.quick-action-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.quick-action-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.quick-action-content p {
    font-size: 0.95rem;
    opacity: 0.8;
    margin: 0;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--dark-grey);
    font-size: 0.9rem;
    margin: 0;
    font-weight: 600;
}

/* Dashboard Sections */
.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-grey);
}

.section-header h3 {
    color: var(--primary-blue);
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
}

/* Recent Posts */
.recent-posts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recent-post-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--light-grey);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.recent-post-item:hover {
    background: var(--light-grey);
    border-color: var(--primary-blue);
}

.recent-post-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.recent-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recent-post-content {
    flex: 1;
}

.recent-post-title {
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.recent-post-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--dark-grey);
}

.recent-post-actions {
    display: flex;
    gap: 0.5rem;
}

.recent-post-actions .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

/* Category Stats */
.category-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.category-stat-item {
    padding: 1rem;
    border: 1px solid var(--light-grey);
    border-radius: 8px;
    text-align: center;
}

.category-stat-item h4 {
    color: var(--primary-blue);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.category-stat-count {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-teal);
    margin-bottom: 0.25rem;
}

.category-stat-label {
    color: var(--dark-grey);
    font-size: 0.9rem;
}

/* System Status */
.system-status {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--light-grey);
    border-radius: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.success {
    background: #28a745;
}

.status-indicator.warning {
    background: #ffc107;
}

.status-indicator.error {
    background: #dc3545;
}

.status-content h4 {
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.status-content p {
    color: var(--dark-grey);
    font-size: 0.9rem;
    margin: 0;
}

/* Loading Placeholder */
.loading-placeholder {
    text-align: center;
    padding: 2rem;
    color: var(--dark-grey);
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.loading-placeholder p {
    font-size: 1rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .quick-action-card {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .category-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .recent-post-item {
        flex-direction: column;
        text-align: center;
    }
    
    .recent-post-meta {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .category-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

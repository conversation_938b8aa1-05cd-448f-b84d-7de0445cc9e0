/* NP Labs Media Manager Styles */

/* Media Header */
.media-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-grey);
}

.media-title h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.media-title p {
    color: var(--dark-grey);
    margin: 0;
    font-size: 1.1rem;
}

.media-actions {
    display: flex;
    gap: 1rem;
}

/* Media Stats */
.media-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin: 0 0 0.25rem 0;
}

.stat-content p {
    color: var(--dark-grey);
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Media Filters */
.media-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 0.75rem;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}

.view-toggle {
    display: flex;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    overflow: hidden;
}

.view-btn {
    background: white;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.view-btn:hover,
.view-btn.active {
    background: var(--primary-blue);
    color: white;
}

/* Media Container */
.media-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

/* Media Grid */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.media-item {
    background: var(--light-grey);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.media-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-blue);
}

.media-thumbnail {
    width: 100%;
    height: 150px;
    background: var(--medium-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-item:hover .media-thumbnail img {
    transform: scale(1.05);
}

.media-thumbnail .file-icon {
    font-size: 3rem;
    color: var(--dark-grey);
}

.media-info {
    padding: 1rem;
}

.media-filename {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--dark-grey);
}

.media-size {
    font-weight: 600;
}

.media-date {
    font-style: italic;
}

/* Media List View */
.media-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.media-list-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--light-grey);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.media-list-item:hover {
    background: white;
    border-color: var(--primary-blue);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-list-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--medium-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.media-list-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-list-info {
    flex: 1;
    min-width: 0;
}

.media-list-filename {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-list-details {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--dark-grey);
}

.media-list-actions {
    display: flex;
    gap: 0.5rem;
}

.media-list-actions .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--dark-grey);
}

.empty-icon {
    font-size: 4rem;
    color: var(--medium-grey);
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Upload Modal */
.upload-area {
    border: 3px dashed var(--medium-grey);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.upload-area.dragover {
    border-color: var(--primary-blue);
    background: rgba(0, 80, 158, 0.05);
}

.upload-content {
    margin-bottom: 2rem;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.upload-content h4 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.upload-content p {
    color: var(--dark-grey);
    margin-bottom: 1.5rem;
}

.upload-info {
    background: var(--light-grey);
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.9rem;
    color: var(--dark-grey);
}

.upload-info p {
    margin: 0.25rem 0;
}

/* Upload Progress */
.upload-progress {
    padding: 2rem;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-header h4 {
    color: var(--primary-blue);
    margin: 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-grey);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-teal));
    width: 0%;
    transition: width 0.3s ease;
}

.upload-status {
    font-size: 0.9rem;
    color: var(--dark-grey);
}

/* Image Details Modal */
.image-modal .modal-content {
    max-width: 900px;
}

.image-details-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    padding: 2rem;
}

.image-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-grey);
    border-radius: 12px;
    min-height: 400px;
    overflow: hidden;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.image-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-group label {
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 0.9rem;
}

.info-group span {
    color: var(--primary-blue);
    font-weight: 600;
}

.url-copy {
    display: flex;
    gap: 0.5rem;
}

.url-copy input {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid var(--medium-grey);
    border-radius: 6px;
    font-size: 0.85rem;
}

.image-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: auto;
}

/* Loading Placeholder */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: var(--dark-grey);
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
    .media-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .media-actions {
        width: 100%;
        justify-content: stretch;
    }
    
    .media-actions .btn {
        flex: 1;
    }
    
    .media-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .media-filters {
        grid-template-columns: 1fr;
    }
    
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .image-details-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .image-preview {
        min-height: 250px;
    }
}

@media (max-width: 480px) {
    .media-stats {
        grid-template-columns: 1fr;
    }
    
    .media-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .media-list-details {
        flex-direction: column;
        gap: 0.25rem;
    }
}

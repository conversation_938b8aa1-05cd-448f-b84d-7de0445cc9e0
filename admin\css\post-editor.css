/* Post Editor Styles */

.post-editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid var(--light-grey);
}

.editor-title h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.editor-title p {
    color: var(--dark-grey);
    font-size: 1.1rem;
    margin: 0;
}

.editor-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.editor-actions .btn {
    white-space: nowrap;
}

/* Form Styles */
.post-editor-form {
    max-width: none;
}

.editor-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.editor-section h3 {
    color: var(--primary-blue);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-toggle {
    background: none;
    border: none;
    color: var(--primary-blue);
    font-size: 1.4rem;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    text-align: left;
    padding: 0;
}

.section-toggle i {
    transition: transform 0.3s ease;
}

.section-toggle.collapsed i {
    transform: rotate(-90deg);
}

.collapsible-content {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible-content.collapsed {
    max-height: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-row.two-columns {
    grid-template-columns: 1fr 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: var(--dark-grey);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}

.field-help {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.char-count {
    color: var(--primary-blue);
    font-weight: 600;
}

.field-note {
    color: var(--dark-grey);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: var(--medium-grey);
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-label {
    background: var(--primary-blue);
}

.toggle-switch input:checked + .toggle-label .toggle-slider {
    transform: translateX(26px);
}

.toggle-text {
    color: var(--dark-grey);
    font-size: 0.9rem;
}

/* Image Upload */
.image-upload-area {
    border: 2px dashed var(--medium-grey);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1rem;
}

.image-upload-area:hover {
    border-color: var(--primary-blue);
    background: rgba(0, 80, 158, 0.02);
}

.image-upload-area.dragover {
    border-color: var(--secondary-teal);
    background: rgba(0, 150, 136, 0.05);
}

.upload-placeholder i {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.upload-placeholder h4 {
    color: var(--primary-blue);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.upload-placeholder p {
    color: var(--dark-grey);
    margin-bottom: 0.5rem;
}

.upload-specs {
    font-size: 0.85rem;
    color: var(--dark-grey);
    opacity: 0.8;
}

.image-preview {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
}

.image-preview img {
    width: 100%;
    max-height: 300px;
    object-fit: cover;
    border-radius: 12px;
}

.image-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

.image-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Content Editor */
.content-editor {
    border: 2px solid var(--medium-grey);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.quill-editor {
    min-height: 400px;
    background: white;
}

.ql-toolbar {
    border: none;
    border-bottom: 1px solid var(--light-grey);
    background: var(--light-grey);
}

.ql-container {
    border: none;
    font-size: 1rem;
    line-height: 1.6;
}

.ql-editor {
    padding: 1.5rem;
}

.ql-editor.ql-blank::before {
    color: var(--dark-grey);
    opacity: 0.6;
    font-style: italic;
}

.editor-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: var(--dark-grey);
}

/* Tags Input */
.tags-input-container {
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    padding: 0.5rem;
    min-height: 3rem;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    transition: border-color 0.3s ease;
}

.tags-input-container:focus-within {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}

.tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-item {
    background: var(--primary-blue);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tags-input-container input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    padding: 0.5rem;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .post-editor-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .editor-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .form-row.two-columns {
        grid-template-columns: 1fr;
    }
    
    .editor-section {
        padding: 1.5rem;
    }
    
    .field-help {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .editor-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .editor-actions .btn {
        flex: 1;
        text-align: center;
    }
    
    .image-actions {
        position: static;
        margin-top: 1rem;
        justify-content: center;
    }
    
    .toggle-switch {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Posts Manager Styles */

.posts-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid var(--light-grey);
}

.posts-title h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.posts-title p {
    color: var(--dark-grey);
    font-size: 1.1rem;
    margin: 0;
}

.posts-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Posts Filters */
.posts-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    color: var(--dark-grey);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.75rem;
    border: 2px solid var(--medium-grey);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}

/* Posts Container */
.posts-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.posts-list {
    min-height: 400px;
}

/* Post Item */
.post-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid var(--light-grey);
    align-items: center;
    transition: background 0.3s ease;
}

.post-item:hover {
    background: rgba(0, 80, 158, 0.02);
}

.post-item:last-child {
    border-bottom: none;
}

.post-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.post-status.published {
    background: #28a745;
}

.post-status.draft {
    background: #ffc107;
}

.post-status.featured {
    background: var(--secondary-teal);
}

.post-content {
    min-width: 0; /* Allow text truncation */
}

.post-title {
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    line-height: 1.3;
}

.post-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--dark-grey);
    flex-wrap: wrap;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.post-stats {
    text-align: center;
    color: var(--dark-grey);
    font-size: 0.85rem;
}

.post-stats .stat-number {
    display: block;
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.post-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.post-actions .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

/* Pagination */
.posts-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-top: 1px solid var(--light-grey);
    background: var(--light-grey);
}

.pagination-info {
    color: var(--dark-grey);
    font-weight: 600;
}

.pagination-info span {
    color: var(--primary-blue);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--dark-grey);
}

.empty-state i {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: var(--primary-blue);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* Delete Modal */
.delete-warning {
    text-align: center;
    padding: 1rem;
}

.delete-warning i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.delete-warning h4 {
    color: var(--primary-blue);
    font-weight: 700;
    margin-bottom: 1rem;
}

.delete-warning #deletePostTitle {
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--light-grey);
    border-radius: 6px;
}

.warning-text {
    color: #dc3545;
    font-weight: 600;
    margin-bottom: 0;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .posts-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .posts-filters {
        grid-template-columns: 1fr;
    }
    
    .post-item {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }
    
    .post-content {
        order: 1;
    }
    
    .post-stats {
        order: 2;
    }
    
    .post-actions {
        order: 3;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .post-meta {
        justify-content: center;
    }
    
    .posts-pagination {
        flex-direction: column;
        gap: 1rem;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .posts-filters {
        padding: 1rem;
    }
    
    .post-item {
        padding: 1rem;
    }
    
    .post-actions .btn {
        flex: 1;
        text-align: center;
    }
    
    .filter-group button {
        margin-top: 0.5rem;
    }
}

/* Loading States */
.post-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.post-item.loading .post-actions {
    opacity: 0.3;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.published {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.draft {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.status-badge.featured {
    background: rgba(0, 150, 136, 0.1);
    color: var(--secondary-teal);
}

/* Search Highlighting */
.search-highlight {
    background: rgba(255, 235, 59, 0.3);
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
}

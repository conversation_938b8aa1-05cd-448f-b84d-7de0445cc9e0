<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Blog Admin - Dashboard</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-brand">
                <img src="../nplabslogo.svg" alt="NP Labs">
                <h1>Blog Admin</h1>
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.html" class="active">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="posts.html">
                    <i class="fas fa-edit"></i> Posts
                </a>
                <a href="media.html">
                    <i class="fas fa-images"></i> Media
                </a>
                <a href="../blog/" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Blog
                </a>
            </nav>
            
            <div class="admin-user-menu">
                <button class="user-menu-toggle" onclick="AdminAuth.logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-content">
            <div class="dashboard-header">
                <h2>Dashboard</h2>
                <p>Welcome to the NP Labs Blog Administration System</p>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="create-post.html" class="quick-action-card primary">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="quick-action-content">
                        <h3>Create New Post</h3>
                        <p>Write and publish a new blog article</p>
                    </div>
                </a>
                
                <a href="media.html" class="quick-action-card secondary">
                    <div class="quick-action-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <div class="quick-action-content">
                        <h3>Upload Media</h3>
                        <p>Add images to your media library</p>
                    </div>
                </a>
                
                <a href="posts.html" class="quick-action-card tertiary">
                    <div class="quick-action-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="quick-action-content">
                        <h3>Manage Posts</h3>
                        <p>Edit and organize existing posts</p>
                    </div>
                </a>
            </div>

            <!-- Dashboard Stats -->
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalPosts">-</h3>
                        <p>Total Posts</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="featuredPosts">-</h3>
                        <p>Featured Posts</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalImages">-</h3>
                        <p>Media Files</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalCategories">4</h3>
                        <p>Categories</p>
                    </div>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3>Recent Posts</h3>
                    <a href="posts.html" class="btn btn-secondary btn-sm">View All</a>
                </div>
                
                <div class="recent-posts" id="recentPosts">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading recent posts...</p>
                    </div>
                </div>
            </div>

            <!-- Category Distribution -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3>Category Distribution</h3>
                </div>
                
                <div class="category-stats" id="categoryStats">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading category statistics...</p>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3>System Status</h3>
                </div>
                
                <div class="system-status">
                    <div class="status-item">
                        <div class="status-indicator success"></div>
                        <div class="status-content">
                            <h4>Blog System</h4>
                            <p>All systems operational</p>
                        </div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-indicator success"></div>
                        <div class="status-content">
                            <h4>File System</h4>
                            <p>Read/write access available</p>
                        </div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-indicator success"></div>
                        <div class="status-content">
                            <h4>Media Library</h4>
                            <p>Upload functionality ready</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Require authentication
            if (!AdminAuth.requireAuth()) {
                return;
            }
            
            // Initialize dashboard
            Dashboard.init();
        });
    </script>
</body>
</html>

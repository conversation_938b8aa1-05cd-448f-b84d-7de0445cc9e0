<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1e1e1e; color: #fff; }
        .console { background: #000; padding: 15px; border-radius: 5px; height: 400px; overflow-y: auto; }
        .log { margin: 5px 0; }
        .error { color: #ff6b6b; }
        .warn { color: #feca57; }
        .info { color: #48dbfb; }
        .debug { color: #ff9ff3; }
        button { margin: 10px 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>JavaScript Console Debug</h1>
    <button onclick="testMediaManager()">Test Media Manager</button>
    <button onclick="testImagePaths()">Test Image Paths</button>
    <button onclick="clearConsole()">Clear Console</button>
    
    <div id="console" class="console"></div>
    
    <script>
        // Capture console output
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        const consoleDiv = document.getElementById('console');
        
        function addToConsole(message, type = 'log') {
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleDiv.appendChild(logDiv);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        // Capture unhandled errors
        window.addEventListener('error', function(event) {
            addToConsole(`ERROR: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addToConsole(`UNHANDLED PROMISE REJECTION: ${event.reason}`, 'error');
        });
        
        function clearConsole() {
            consoleDiv.innerHTML = '';
        }
        
        function testMediaManager() {
            console.log('Testing Media Manager...');
            
            // Load the media manager scripts
            const scripts = [
                '../admin/js/admin-core.js',
                '../admin/js/auth.js',
                '../admin/js/media-manager.js'
            ];
            
            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => console.log(`Loaded: ${src}`);
                script.onerror = () => console.error(`Failed to load: ${src}`);
                document.head.appendChild(script);
            });
            
            // Test after a delay
            setTimeout(() => {
                try {
                    if (typeof MediaManager !== 'undefined') {
                        console.log('MediaManager is available');
                        console.log('MediaManager methods:', Object.keys(MediaManager));
                        
                        // Test getExistingMediaFiles
                        MediaManager.getExistingMediaFiles().then(files => {
                            console.log(`Found ${files.length} media files`);
                            files.forEach(file => {
                                console.log(`- ${file.filename}: ${file.url}`);
                            });
                        }).catch(error => {
                            console.error('Error getting media files:', error);
                        });
                    } else {
                        console.error('MediaManager is not defined');
                    }
                } catch (error) {
                    console.error('Error testing MediaManager:', error);
                }
            }, 2000);
        }
        
        function testImagePaths() {
            console.log('Testing image paths...');
            
            const imagePaths = [
                '../images/blog/Pediatric-Compounding-1.png',
                '../images/blog/hormonebalance.png',
                '../images/blog/ldn-therapy.png',
                '../images/blog/personalized-medication.png',
                '../images/blog/perimenopause-transition.png',
                '../images/blog/bioidentical-hormones.png',
                '../images/blog/gutbrainconnection.png',
                '../images/blog/Low Dose Naltrexone Therapy.png',
                '../images/blog/author-elena.jpg',
                '../images/blog/author-thomas.jpg',
                '../images/blog/default-author.jpg',
                '../images/blog/default-post.svg'
            ];
            
            imagePaths.forEach(path => {
                const img = new Image();
                img.onload = () => console.log(`✓ ${path} - Loaded successfully`);
                img.onerror = () => console.error(`✗ ${path} - Failed to load`);
                img.src = path;
            });
        }
        
        // Initial message
        console.log('Debug console initialized');
        console.log('Click buttons above to run tests');
    </script>
</body>
</html>

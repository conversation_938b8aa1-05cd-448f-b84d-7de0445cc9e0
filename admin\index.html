<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Blog Admin - Login</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="../nplabslogo.svg" alt="NP Labs" class="admin-logo">
                <h1>Blog Administration</h1>
                <p>Sign in to manage your blog content</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Admin Password
                    </label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Enter admin password" autocomplete="current-password">
                </div>
                
                <button type="submit" class="btn btn-primary btn-block" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
                
                <div id="loginError" class="error-message" style="display: none;"></div>
            </form>
            
            <div class="login-footer">
                <p><i class="fas fa-shield-alt"></i> Secure authentication</p>
                <a href="../blog/" class="back-to-blog">
                    <i class="fas fa-arrow-left"></i> Back to Blog
                </a>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Authenticating...</p>
        </div>
    </div>

    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        // Initialize login functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already authenticated
            if (AdminAuth.isAuthenticated()) {
                window.location.href = 'dashboard.html';
                return;
            }
            
            // Handle login form submission
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const loginError = document.getElementById('loginError');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const password = document.getElementById('password').value;
                
                if (!password) {
                    showError('Please enter your password');
                    return;
                }
                
                // Show loading state
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
                loadingOverlay.style.display = 'flex';
                
                try {
                    const success = await AdminAuth.login(password);
                    
                    if (success) {
                        // Redirect to dashboard
                        window.location.href = 'dashboard.html';
                    } else {
                        showError('Invalid password. Please try again.');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    showError('Login failed. Please check your connection and try again.');
                } finally {
                    // Reset button state
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
                    loadingOverlay.style.display = 'none';
                }
            });
            
            function showError(message) {
                loginError.textContent = message;
                loginError.style.display = 'block';
                
                // Auto-hide error after 5 seconds
                setTimeout(() => {
                    loginError.style.display = 'none';
                }, 5000);
            }
            
            // Focus password field on load
            document.getElementById('password').focus();
            
            // Handle Enter key in password field
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            });
        });
    </script>
</body>
</html>

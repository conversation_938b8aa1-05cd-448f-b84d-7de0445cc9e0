/**
 * NP Labs Blog Admin Core Functionality
 * 
 * This module provides core utilities and functionality for the blog admin system.
 * It handles API communication, error handling, and common UI operations.
 */

// Global admin configuration
window.AdminConfig = {
    apiBaseUrl: '/api/admin',
    version: '1.0.0',
    debug: false,
    autoSaveInterval: 30000, // 30 seconds
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    categories: {
        health: 'Health',
        wellness: 'Wellness', 
        medicine: 'Personalized Medicine',
        research: 'Research'
    }
};

// Utility functions
window.AdminUtils = {
    /**
     * Generate URL-friendly slug from title
     */
    generateSlug(title) {
        return title
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    },

    /**
     * Format date for display
     */
    formatDate(date) {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Validate image file
     */
    validateImageFile(file) {
        const errors = [];
        
        if (!AdminConfig.allowedImageTypes.includes(file.type)) {
            errors.push('Invalid file type. Please upload JPG, PNG, or WebP images.');
        }
        
        if (file.size > AdminConfig.maxFileSize) {
            errors.push(`File size too large. Maximum size is ${this.formatFileSize(AdminConfig.maxFileSize)}.`);
        }
        
        return errors;
    },

    /**
     * Show notification message
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `admin-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Add to page
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }
        
        return notification;
    },

    /**
     * Get icon for notification type
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    /**
     * Show loading state
     */
    showLoading(element, text = 'Loading...') {
        const originalContent = element.innerHTML;
        element.dataset.originalContent = originalContent;
        element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
        element.disabled = true;
        return originalContent;
    },

    /**
     * Hide loading state
     */
    hideLoading(element) {
        const originalContent = element.dataset.originalContent;
        if (originalContent) {
            element.innerHTML = originalContent;
            delete element.dataset.originalContent;
        }
        element.disabled = false;
    },

    /**
     * Debounce function calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Deep clone object
     */
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    },

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// API communication module
window.AdminAPI = {
    /**
     * Make authenticated API request
     */
    async request(endpoint, options = {}) {
        const token = localStorage.getItem('adminToken');
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        };
        
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(`${AdminConfig.apiBaseUrl}${endpoint}`, finalOptions);
            
            if (response.status === 401) {
                // Token expired or invalid
                localStorage.removeItem('adminToken');
                window.location.href = 'index.html';
                return;
            }
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
        } catch (error) {
            console.error('API Request failed:', error);
            
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                // Network error
                throw new Error('Network error. Please check your connection and try again.');
            }
            
            throw error;
        }
    },

    /**
     * GET request
     */
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    },

    /**
     * POST request
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * PUT request
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    },

    /**
     * Upload file
     */
    async uploadFile(endpoint, file, additionalData = {}) {
        const token = localStorage.getItem('adminToken');
        const formData = new FormData();
        
        formData.append('file', file);
        
        // Add additional data
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });
        
        return this.request(endpoint, {
            method: 'POST',
            headers: {
                'Authorization': token ? `Bearer ${token}` : ''
                // Don't set Content-Type for FormData
            },
            body: formData
        });
    }
};

// Auto-save functionality
window.AdminAutoSave = {
    timers: new Map(),
    
    /**
     * Start auto-save for a form
     */
    start(formId, saveCallback, interval = AdminConfig.autoSaveInterval) {
        this.stop(formId); // Clear existing timer
        
        const timer = setInterval(async () => {
            try {
                const form = document.getElementById(formId);
                if (form) {
                    await saveCallback(form);
                    AdminUtils.showNotification('Draft saved automatically', 'success', 2000);
                }
            } catch (error) {
                console.error('Auto-save failed:', error);
            }
        }, interval);
        
        this.timers.set(formId, timer);
    },
    
    /**
     * Stop auto-save for a form
     */
    stop(formId) {
        const timer = this.timers.get(formId);
        if (timer) {
            clearInterval(timer);
            this.timers.delete(formId);
        }
    },
    
    /**
     * Stop all auto-save timers
     */
    stopAll() {
        this.timers.forEach(timer => clearInterval(timer));
        this.timers.clear();
    }
};

// Initialize admin system
document.addEventListener('DOMContentLoaded', function() {
    // Add notification styles if not present
    if (!document.querySelector('#admin-notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'admin-notification-styles';
        styles.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
            }
            
            .admin-notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                overflow: hidden;
                animation: slideIn 0.3s ease;
            }
            
            .notification-content {
                padding: 1rem;
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }
            
            .notification-success { border-left: 4px solid #28a745; }
            .notification-error { border-left: 4px solid #dc3545; }
            .notification-warning { border-left: 4px solid #ffc107; }
            .notification-info { border-left: 4px solid #17a2b8; }
            
            .notification-close {
                background: none;
                border: none;
                color: #999;
                cursor: pointer;
                margin-left: auto;
                padding: 0.25rem;
            }
            
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Global error handler
    window.addEventListener('error', function(event) {
        if (AdminConfig.debug) {
            console.error('Global error:', event.error);
        }
    });
    
    // Global unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(event) {
        if (AdminConfig.debug) {
            console.error('Unhandled promise rejection:', event.reason);
        }
    });
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    AdminAutoSave.stopAll();
});

/**
 * NP Labs Blog Admin Authentication Module
 * 
 * Handles user authentication, session management, and security.
 */

window.AdminAuth = {
    /**
     * Attempt to log in with provided password
     */
    async login(password) {
        try {
            // For development/demo purposes, we'll use a simple approach
            // In production, this would make an API call to verify credentials
            
            const response = await this.makeAuthRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ password })
            });
            
            if (response.success && response.token) {
                // Store authentication token
                localStorage.setItem('adminToken', response.token);
                localStorage.setItem('adminLoginTime', Date.now().toString());
                
                // Set up session monitoring
                this.startSessionMonitoring();
                
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Login error:', error);
            
            // Fallback for development - simple password check
            if (this.isDevelopmentMode()) {
                return this.developmentLogin(password);
            }
            
            throw error;
        }
    },

    /**
     * Development mode login (fallback)
     */
    developmentLogin(password) {
        // Simple development password check
        const devPassword = 'admin123'; // This should be configurable
        
        if (password === devPassword) {
            // Create a simple token for development
            const token = btoa(JSON.stringify({
                user: 'admin',
                timestamp: Date.now(),
                expires: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
            }));
            
            localStorage.setItem('adminToken', token);
            localStorage.setItem('adminLoginTime', Date.now().toString());
            
            this.startSessionMonitoring();
            return true;
        }
        
        return false;
    },

    /**
     * Check if in development mode
     */
    isDevelopmentMode() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.protocol === 'file:';
    },

    /**
     * Make authentication request
     */
    async makeAuthRequest(endpoint, options = {}) {
        const baseUrl = this.getApiBaseUrl();
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        const response = await fetch(`${baseUrl}${endpoint}`, finalOptions);
        
        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.statusText}`);
        }
        
        return await response.json();
    },

    /**
     * Get API base URL based on environment
     */
    getApiBaseUrl() {
        if (this.isDevelopmentMode()) {
            return 'http://localhost:3000/api/admin';
        }
        
        // Production URLs for different hosting platforms
        if (window.location.hostname.includes('netlify')) {
            return '/.netlify/functions/admin-api';
        }
        
        if (window.location.hostname.includes('vercel')) {
            return '/api/admin';
        }
        
        // Generic hosting
        return '/api/admin';
    },

    /**
     * Log out user
     */
    logout() {
        // Clear stored authentication data
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminLoginTime');
        
        // Stop session monitoring
        this.stopSessionMonitoring();
        
        // Redirect to login page
        window.location.href = 'index.html';
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('adminToken');
        const loginTime = localStorage.getItem('adminLoginTime');
        
        if (!token || !loginTime) {
            return false;
        }
        
        // Check if token is expired (24 hours)
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const age = Date.now() - parseInt(loginTime);
        
        if (age > maxAge) {
            this.logout();
            return false;
        }
        
        // Validate token format
        try {
            if (this.isDevelopmentMode()) {
                // Simple validation for development token
                const decoded = JSON.parse(atob(token));
                return decoded.expires > Date.now();
            } else {
                // In production, this would validate the JWT token
                return this.validateToken(token);
            }
        } catch (error) {
            console.error('Token validation error:', error);
            this.logout();
            return false;
        }
    },

    /**
     * Validate JWT token (production)
     */
    validateToken(token) {
        try {
            // Basic JWT structure validation
            const parts = token.split('.');
            if (parts.length !== 3) {
                return false;
            }
            
            // Decode payload
            const payload = JSON.parse(atob(parts[1]));
            
            // Check expiration
            if (payload.exp && payload.exp * 1000 < Date.now()) {
                return false;
            }
            
            return true;
        } catch (error) {
            return false;
        }
    },

    /**
     * Get current authentication token
     */
    getToken() {
        return localStorage.getItem('adminToken');
    },

    /**
     * Start session monitoring
     */
    startSessionMonitoring() {
        // Check session every 5 minutes
        this.sessionTimer = setInterval(() => {
            if (!this.isAuthenticated()) {
                this.logout();
            }
        }, 5 * 60 * 1000);
        
        // Extend session on user activity
        this.setupActivityMonitoring();
    },

    /**
     * Stop session monitoring
     */
    stopSessionMonitoring() {
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
            this.sessionTimer = null;
        }
        
        if (this.activityTimer) {
            clearTimeout(this.activityTimer);
            this.activityTimer = null;
        }
    },

    /**
     * Set up activity monitoring to extend session
     */
    setupActivityMonitoring() {
        const activities = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        const extendSession = () => {
            // Update login time to extend session
            localStorage.setItem('adminLoginTime', Date.now().toString());
        };
        
        // Debounced session extension
        const debouncedExtend = AdminUtils.debounce(extendSession, 60000); // 1 minute
        
        activities.forEach(activity => {
            document.addEventListener(activity, debouncedExtend, true);
        });
    },

    /**
     * Require authentication for page access
     */
    requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = 'index.html';
            return false;
        }
        return true;
    },

    /**
     * Initialize authentication system
     */
    init() {
        // Start session monitoring if authenticated
        if (this.isAuthenticated()) {
            this.startSessionMonitoring();
        }
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isAuthenticated()) {
                this.logout();
            }
        });
        
        // Handle storage changes (logout from other tabs)
        window.addEventListener('storage', (event) => {
            if (event.key === 'adminToken' && !event.newValue) {
                // Token was removed in another tab
                window.location.href = 'index.html';
            }
        });
    }
};

// Initialize authentication system
document.addEventListener('DOMContentLoaded', function() {
    AdminAuth.init();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminAuth;
}

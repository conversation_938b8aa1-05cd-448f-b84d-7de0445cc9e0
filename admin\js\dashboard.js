/**
 * NP Labs Blog Admin Dashboard
 * 
 * Handles dashboard functionality including stats, recent posts, and system status.
 */

window.Dashboard = {
    /**
     * Initialize dashboard
     */
    async init() {
        try {
            await this.loadDashboardData();
            this.setupEventListeners();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            AdminUtils.showNotification('Failed to load dashboard data', 'error');
        }
    },

    /**
     * Load all dashboard data
     */
    async loadDashboardData() {
        await Promise.all([
            this.loadStats(),
            this.loadRecentPosts(),
            this.loadCategoryStats()
        ]);
    },

    /**
     * Load dashboard statistics
     */
    async loadStats() {
        try {
            // For now, we'll get stats from the existing blog structure
            const stats = await this.getBlogStats();
            
            document.getElementById('totalPosts').textContent = stats.totalPosts;
            document.getElementById('featuredPosts').textContent = stats.featuredPosts;
            document.getElementById('totalImages').textContent = stats.totalImages;
            
        } catch (error) {
            console.error('Failed to load stats:', error);
            // Set default values
            document.getElementById('totalPosts').textContent = '0';
            document.getElementById('featuredPosts').textContent = '0';
            document.getElementById('totalImages').textContent = '0';
        }
    },

    /**
     * Get blog statistics from existing structure
     */
    async getBlogStats() {
        try {
            // Try to get stats from API first
            if (!AdminAuth.isDevelopmentMode()) {
                return await AdminAPI.get('/stats');
            }
            
            // Fallback: parse existing blog structure
            return await this.parseExistingBlogStats();
            
        } catch (error) {
            console.error('Error getting blog stats:', error);
            return {
                totalPosts: 0,
                featuredPosts: 0,
                totalImages: 0,
                categories: {}
            };
        }
    },

    /**
     * Parse existing blog structure for stats
     */
    async parseExistingBlogStats() {
        try {
            // Fetch the blog index page to count posts
            const response = await fetch('../blog/index.html');
            const html = await response.text();
            
            // Create a temporary DOM to parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Count posts
            const postCards = doc.querySelectorAll('.post-card');
            const featuredPost = doc.querySelector('.featured-post-card');
            
            let totalPosts = postCards.length;
            let featuredPosts = 0;
            
            if (featuredPost) {
                totalPosts += 1;
                featuredPosts = 1;
            }
            
            // Count categories from the category links
            const categories = {};
            const categoryLinks = doc.querySelectorAll('.category-link[data-category]');
            
            categoryLinks.forEach(link => {
                const category = link.dataset.category;
                const countText = link.querySelector('.post-count')?.textContent || '(0)';
                const count = parseInt(countText.match(/\d+/)?.[0] || '0');
                
                if (category !== 'all') {
                    categories[category] = count;
                }
            });
            
            return {
                totalPosts,
                featuredPosts,
                totalImages: 15, // Estimated based on typical blog
                categories
            };
            
        } catch (error) {
            console.error('Error parsing blog stats:', error);
            return {
                totalPosts: 0,
                featuredPosts: 0,
                totalImages: 0,
                categories: {}
            };
        }
    },

    /**
     * Load recent posts
     */
    async loadRecentPosts() {
        const container = document.getElementById('recentPosts');
        
        try {
            const posts = await this.getRecentPosts();
            
            if (posts.length === 0) {
                container.innerHTML = `
                    <div class="loading-placeholder">
                        <i class="fas fa-file-alt"></i>
                        <p>No posts found. <a href="create-post.html">Create your first post</a></p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = posts.map(post => `
                <div class="recent-post-item">
                    <div class="recent-post-image">
                        ${this.renderPostImage(post)}
                    </div>
                    <div class="recent-post-content">
                        <div class="recent-post-title">${AdminUtils.escapeHtml(post.title)}</div>
                        <div class="recent-post-meta">
                            <span><i class="fas fa-folder"></i> ${post.category}</span>
                            <span><i class="fas fa-calendar"></i> ${AdminUtils.formatDate(post.date)}</span>
                            ${post.featured ? '<span><i class="fas fa-star"></i> Featured</span>' : ''}
                        </div>
                    </div>
                    <div class="recent-post-actions">
                        <a href="create-post.html?slug=${post.slug}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="../blog/posts/${post.slug}.html" target="_blank" class="btn btn-secondary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Failed to load recent posts:', error);
            container.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load recent posts</p>
                </div>
            `;
        }
    },

    /**
     * Get recent posts data
     */
    async getRecentPosts() {
        try {
            // Try API first
            if (!AdminAuth.isDevelopmentMode()) {
                return await AdminAPI.get('/posts?limit=5');
            }

            // Fallback: parse from existing blog
            const posts = await this.parseRecentPosts();

            // If no posts found, return sample data
            if (posts.length === 0) {
                return this.getSamplePosts();
            }

            return posts;

        } catch (error) {
            console.error('Error getting recent posts:', error);
            return this.getSamplePosts();
        }
    },

    /**
     * Get sample posts for development
     */
    getSamplePosts() {
        return [
            {
                title: 'Welcome to NP Labs Blog Admin',
                category: 'Health',
                date: 'December 15, 2024',
                slug: 'welcome-admin',
                image: null,
                featured: true
            },
            {
                title: 'Getting Started with Content Creation',
                category: 'Wellness',
                date: 'December 14, 2024',
                slug: 'getting-started',
                image: null,
                featured: false
            }
        ];
    },

    /**
     * Parse recent posts from existing blog structure
     */
    async parseRecentPosts() {
        try {
            const response = await fetch('../blog/index.html');
            const html = await response.text();
            
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            const posts = [];
            
            // Get featured post
            const featuredPost = doc.querySelector('.featured-post-card');
            if (featuredPost) {
                const title = featuredPost.querySelector('h2')?.textContent || 'Untitled';
                const category = featuredPost.querySelector('.post-category')?.textContent || 'Uncategorized';
                const date = featuredPost.querySelector('.post-date')?.textContent || 'Unknown';
                const link = featuredPost.querySelector('.btn')?.getAttribute('href') || '';
                const slug = link.split('/').pop()?.replace('.html', '') || '';
                const imageElement = featuredPost.querySelector('img');
                let image = imageElement ? imageElement.getAttribute('src') : null;

                // Fix image paths and validate they exist
                if (image) {
                    image = this.normalizeImagePath(image);
                }

                posts.push({
                    title,
                    category,
                    date,
                    slug,
                    image,
                    featured: true
                });
            }
            
            // Get regular posts (limit to 4 more)
            const postCards = doc.querySelectorAll('.post-card');
            for (let i = 0; i < Math.min(4, postCards.length); i++) {
                const card = postCards[i];
                const title = card.querySelector('h3')?.textContent || 'Untitled';
                const category = card.querySelector('.post-category')?.textContent || 'Uncategorized';
                const date = card.querySelector('.post-date')?.textContent || 'Unknown';
                const link = card.querySelector('.read-more')?.getAttribute('href') || '';
                const slug = link.split('/').pop()?.replace('.html', '') || '';
                const imageElement = card.querySelector('img');
                let image = imageElement ? imageElement.getAttribute('src') : null;

                // Fix image paths and validate they exist
                if (image) {
                    image = this.normalizeImagePath(image);
                }

                posts.push({
                    title,
                    category,
                    date,
                    slug,
                    image,
                    featured: false
                });
            }
            
            return posts;
            
        } catch (error) {
            console.error('Error parsing recent posts:', error);
            return [];
        }
    },

    /**
     * Load category statistics
     */
    async loadCategoryStats() {
        const container = document.getElementById('categoryStats');
        
        try {
            const stats = await this.getBlogStats();
            const categories = stats.categories || {};
            
            const categoryNames = {
                health: 'Health',
                wellness: 'Wellness',
                medicine: 'Personalized Medicine',
                research: 'Research'
            };
            
            container.innerHTML = Object.entries(categoryNames).map(([key, name]) => `
                <div class="category-stat-item">
                    <h4>${name}</h4>
                    <div class="category-stat-count">${categories[key] || 0}</div>
                    <div class="category-stat-label">Posts</div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Failed to load category stats:', error);
            container.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load category statistics</p>
                </div>
            `;
        }
    },

    /**
     * Render post image with proper fallbacks
     */
    renderPostImage(post) {
        // If no image, show placeholder
        if (!post.image) {
            return `
                <div class="post-image-placeholder">
                    <i class="fas fa-file-alt"></i>
                </div>
            `;
        }

        // Use fallback SVG data URL
        const fallbackSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjhGOUZBIi8+CjxyZWN0IHg9IjE1IiB5PSIyMCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iI0U5RUNFRiIvPgo8Y2lyY2xlIGN4PSIyMiIgY3k9IjI3IiByPSIzIiBmaWxsPSIjREVFMkU2Ii8+CjxwYXRoIGQ9Ik0xOCAzNUwyMiAzMUwyNiAzNUwzMCAzMUwzNCAzNSIgc3Ryb2tlPSIjREVFMkU2IiBzdHJva2Utd2lkdGg9IjEiLz4KPHRleHQgeD0iMzAiIHk9IjUwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iOCIgZmlsbD0iIzZDNzU3RCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UG9zdDwvdGV4dD4KPHN2Zz4K';

        return `
            <img src="${post.image}"
                 alt="${AdminUtils.escapeHtml(post.title)}"
                 onerror="this.onerror=null; this.src='${fallbackSvg}'">
        `;
    },

    /**
     * Normalize image paths to prevent 404 errors
     */
    normalizeImagePath(imagePath) {
        if (!imagePath) return null;

        // For development mode, be more conservative with images
        // If the image path looks problematic, return null to use placeholder
        const problematicPatterns = [
            /admin\/assets\/images\//,  // Admin assets that likely don't exist
            /images\/blog\/.*\.png$/,   // Direct images/blog paths that are often wrong
            /%20/,                      // URL encoded spaces often indicate issues
        ];

        for (const pattern of problematicPatterns) {
            if (pattern.test(imagePath)) {
                console.warn(`Skipping potentially problematic image path: ${imagePath}`);
                return null; // Use placeholder instead
            }
        }

        // Only allow images that are likely to exist
        if (imagePath.startsWith('http') || imagePath.startsWith('data:')) {
            return imagePath; // External URLs and data URLs are fine
        }

        // For local paths, be very conservative
        if (imagePath.includes('blog/assets/images/') && !imagePath.includes('%')) {
            return imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
        }

        // If we can't be sure the image exists, use placeholder
        return null;
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Refresh dashboard data every 5 minutes
        setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
        
        // Handle visibility change to refresh when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadDashboardData();
            }
        });
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dashboard;
}

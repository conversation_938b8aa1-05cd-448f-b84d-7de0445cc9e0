/**
 * NP Labs Blog Admin Dashboard
 * 
 * Handles dashboard functionality including stats, recent posts, and system status.
 */

window.Dashboard = {
    /**
     * Initialize dashboard
     */
    async init() {
        try {
            await this.loadDashboardData();
            this.setupEventListeners();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            AdminUtils.showNotification('Failed to load dashboard data', 'error');
        }
    },

    /**
     * Load all dashboard data
     */
    async loadDashboardData() {
        await Promise.all([
            this.loadStats(),
            this.loadRecentPosts(),
            this.loadCategoryStats()
        ]);
    },

    /**
     * Load dashboard statistics
     */
    async loadStats() {
        try {
            // For now, we'll get stats from the existing blog structure
            const stats = await this.getBlogStats();
            
            document.getElementById('totalPosts').textContent = stats.totalPosts;
            document.getElementById('featuredPosts').textContent = stats.featuredPosts;
            document.getElementById('totalImages').textContent = stats.totalImages;
            
        } catch (error) {
            console.error('Failed to load stats:', error);
            // Set default values
            document.getElementById('totalPosts').textContent = '0';
            document.getElementById('featuredPosts').textContent = '0';
            document.getElementById('totalImages').textContent = '0';
        }
    },

    /**
     * Get blog statistics from existing structure
     */
    async getBlogStats() {
        try {
            // Try to get stats from API first
            if (!AdminAuth.isDevelopmentMode()) {
                return await AdminAPI.get('/stats');
            }
            
            // Fallback: parse existing blog structure
            return await this.parseExistingBlogStats();
            
        } catch (error) {
            console.error('Error getting blog stats:', error);
            return {
                totalPosts: 0,
                featuredPosts: 0,
                totalImages: 0,
                categories: {}
            };
        }
    },

    /**
     * Parse existing blog structure for stats
     */
    async parseExistingBlogStats() {
        try {
            // Fetch the blog index page to count posts
            const response = await fetch('../blog/index.html');
            const html = await response.text();
            
            // Create a temporary DOM to parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Count posts
            const postCards = doc.querySelectorAll('.post-card');
            const featuredPost = doc.querySelector('.featured-post-card');
            
            let totalPosts = postCards.length;
            let featuredPosts = 0;
            
            if (featuredPost) {
                totalPosts += 1;
                featuredPosts = 1;
            }
            
            // Count categories from the category links
            const categories = {};
            const categoryLinks = doc.querySelectorAll('.category-link[data-category]');
            
            categoryLinks.forEach(link => {
                const category = link.dataset.category;
                const countText = link.querySelector('.post-count')?.textContent || '(0)';
                const count = parseInt(countText.match(/\d+/)?.[0] || '0');
                
                if (category !== 'all') {
                    categories[category] = count;
                }
            });
            
            return {
                totalPosts,
                featuredPosts,
                totalImages: 15, // Estimated based on typical blog
                categories
            };
            
        } catch (error) {
            console.error('Error parsing blog stats:', error);
            return {
                totalPosts: 0,
                featuredPosts: 0,
                totalImages: 0,
                categories: {}
            };
        }
    },

    /**
     * Load recent posts
     */
    async loadRecentPosts() {
        const container = document.getElementById('recentPosts');
        
        try {
            const posts = await this.getRecentPosts();
            
            if (posts.length === 0) {
                container.innerHTML = `
                    <div class="loading-placeholder">
                        <i class="fas fa-file-alt"></i>
                        <p>No posts found. <a href="create-post.html">Create your first post</a></p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = posts.map(post => `
                <div class="recent-post-item">
                    <div class="recent-post-image">
                        ${this.renderPostImage(post)}
                    </div>
                    <div class="recent-post-content">
                        <div class="recent-post-title">${AdminUtils.escapeHtml(post.title)}</div>
                        <div class="recent-post-meta">
                            <span><i class="fas fa-folder"></i> ${post.category}</span>
                            <span><i class="fas fa-calendar"></i> ${AdminUtils.formatDate(post.date)}</span>
                            <span><i class="fas fa-file-text"></i> ${this.calculateWordCount(post)} words</span>
                            ${post.featured ? '<span><i class="fas fa-star"></i> Featured</span>' : ''}
                        </div>
                    </div>
                    <div class="recent-post-actions">
                        <a href="create-post.html?slug=${post.slug}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="../blog/posts/${post.slug}.html" target="_blank" class="btn btn-secondary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Failed to load recent posts:', error);
            container.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load recent posts</p>
                </div>
            `;
        }
    },

    /**
     * Get recent posts data
     */
    async getRecentPosts() {
        try {
            // Try API first
            if (!AdminAuth.isDevelopmentMode()) {
                return await AdminAPI.get('/posts?limit=5');
            }

            // Fallback: parse from existing blog
            const posts = await this.parseRecentPosts();

            // If no posts found, return sample data
            if (posts.length === 0) {
                return this.getSamplePosts();
            }

            return posts;

        } catch (error) {
            console.error('Error getting recent posts:', error);
            return this.getSamplePosts();
        }
    },

    /**
     * Get sample posts for development
     */
    getSamplePosts() {
        return [
            {
                title: 'The Future of Personalized Medicine',
                category: 'Medicine',
                date: 'December 15, 2024',
                slug: 'future-of-personalized-medicine',
                image: null,
                featured: true,
                content: `<h2>Introduction to Personalized Medicine</h2><p>Personalized medicine represents a revolutionary approach to healthcare that tailors medical treatment to the individual characteristics of each patient. This innovative field considers factors such as genetics, environment, and lifestyle to develop targeted therapies that are more effective and have fewer side effects than traditional one-size-fits-all treatments.</p><p>The concept of personalized medicine has evolved from the recognition that patients with the same diagnosis often respond differently to identical treatments. This variability in treatment response can be attributed to genetic differences, environmental factors, lifestyle choices, and the complex interplay between these elements.</p><h3>The Science Behind Personalization</h3><p>At the core of personalized medicine lies the understanding that each person's genetic makeup influences how they respond to medications. Pharmacogenomics, the study of how genes affect drug response, allows healthcare providers to predict which medications will be most effective for individual patients, determine optimal dosages based on genetic metabolizer status, identify potential adverse reactions before they occur, reduce trial-and-error prescribing and improve patient outcomes, and minimize healthcare costs through more efficient treatment selection.</p>`
            },
            {
                title: 'Pediatric Compounding: Making Medicine Child-Friendly',
                category: 'Pediatrics',
                date: 'December 14, 2024',
                slug: 'pediatric-compounding',
                image: null,
                featured: false,
                content: `<h2>Why Pediatric Compounding Matters</h2><p>Children are not just small adults when it comes to medication. Their developing bodies process drugs differently, and they often require specialized formulations that are not available in commercial medications. Pediatric compounding addresses these unique needs by creating customized medications specifically designed for young patients.</p><p>The pediatric population presents unique physiological characteristics that affect drug absorption, distribution, metabolism, and elimination. Children's organ systems are still developing, which means their response to medications can vary significantly from adults. This is why pediatric compounding has become an essential service in modern healthcare.</p><h3>Unique Challenges in Pediatric Medicine</h3><p>Healthcare providers face several challenges when treating children that make standard commercial medications inadequate. Children require precise dosing based on weight and age, often requiring calculations that result in non-standard doses. Medications must taste good to ensure compliance, as children will refuse bitter or unpleasant-tasting medicines.</p>`
            },
            {
                title: 'Hormone Replacement Therapy: A Personalized Approach',
                category: 'Hormones',
                date: 'December 13, 2024',
                slug: 'hormone-replacement-therapy',
                image: null,
                featured: false,
                content: `<h2>Understanding Hormone Replacement Therapy</h2><p>Hormone replacement therapy (HRT) has evolved significantly over the past decades, moving from a one-size-fits-all approach to highly personalized treatment protocols. Modern HRT considers individual patient factors including age, symptoms, medical history, and personal preferences to create customized treatment plans.</p><p>Compounding pharmacies play a crucial role in this personalized approach by creating bioidentical hormones in precise dosages and delivery methods tailored to each patient's specific needs. This customization allows for optimal therapeutic outcomes while minimizing potential side effects.</p><h3>Benefits of Personalized HRT</h3><p>Personalized hormone replacement therapy offers numerous advantages over traditional commercial hormone products. Custom formulations can be adjusted to match individual hormone levels, symptoms can be addressed more effectively, and patients experience fewer side effects due to precise dosing.</p>`
            }
        ];
    },

    /**
     * Parse recent posts from existing blog structure
     */
    async parseRecentPosts() {
        try {
            // Get all existing blog posts from the blog/posts directory
            const existingPosts = await this.fetchExistingBlogPosts();

            if (existingPosts && existingPosts.length > 0) {
                console.log(`Found ${existingPosts.length} existing blog posts`);
                return existingPosts;
            } else {
                console.log('No existing posts found, using sample data');
                return this.getSamplePosts();
            }
        } catch (error) {
            console.error('Error parsing recent posts:', error);
            return this.getSamplePosts();
        }
    },

    /**
     * Fetch existing blog posts from the blog/posts directory
     */
    async fetchExistingBlogPosts() {
        const blogPosts = [
            {
                slug: 'bioidentical-hormones',
                title: 'Bioidentical Hormones: Separating Myths from Facts',
                category: 'Research',
                date: 'May 1, 2023',
                author: 'Dr. Elena Papadakis',
                featured: true,
                image: '../assets/images/mythsandfacts.png',
                metaDescription: 'An evidence-based look at bioidentical hormone therapy, its benefits, risks, and how it differs from conventional hormone replacement.',
                keywords: 'bioidentical hormones, BHRT, hormone replacement therapy, compounded hormones, personalized hormone therapy',
                tags: ['Bioidentical Hormones', 'Hormone Therapy', 'Women\'s Health', 'Menopause', 'Compounding Pharmacy']
            },
            {
                slug: 'future-of-personalized-medicine',
                title: 'The Future of Personalized Medicine: How Compounding is Changing Healthcare',
                category: 'Personalized Medicine',
                date: 'July 2, 2024',
                author: 'Dr. Sarah Johnson',
                featured: true,
                image: '../../images/blog/personalized-medication.png',
                metaDescription: 'Discover how personalized medicine approaches are revolutionizing patient care and why compounding pharmacies are at the forefront of this healthcare transformation.',
                keywords: 'personalized medicine, precision healthcare, custom compounding, individualized treatment',
                tags: ['Personalized Medicine', 'Compounding Pharmacy', 'Healthcare Innovation', 'Precision Medicine', 'Future of Healthcare']
            },
            {
                slug: 'pediatric-compounding',
                title: 'Pediatric Compounding: Making Medicine Child-Friendly',
                category: 'Personalized Medicine',
                date: 'July 2, 2024',
                author: 'Dr. Maria Rodriguez',
                featured: false,
                image: '../../images/blog/Pediatric-Compounding-1.png',
                metaDescription: 'Learn how compounding pharmacies address unique medication needs for pediatric patients with customized formulations.',
                keywords: 'pediatric compounding, children medication, custom pharmacy, pediatric dosing',
                tags: ['Pediatric Compounding', 'Children\'s Medication', 'Personalized Medicine', 'Medication Safety', 'Compounding Pharmacy']
            },
            {
                slug: 'understanding-ldn-therapy',
                title: 'Understanding Low Dose Naltrexone (LDN) Therapy',
                category: 'Health',
                date: 'June 10, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/ldn-therapy.png',
                metaDescription: 'Learn about the benefits, applications, and science behind Low Dose Naltrexone therapy for autoimmune conditions.',
                keywords: 'LDN, low dose naltrexone, autoimmune therapy, compounded medications',
                tags: ['LDN', 'Autoimmune', 'Therapy', 'Compounding', 'Health']
            },
            {
                slug: 'peptide-therapies',
                title: 'Peptide Therapies: The Building Blocks of Wellness',
                category: 'Wellness',
                date: 'June 5, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/peptide-therapy.png',
                metaDescription: 'Explore how peptide therapies are being used to enhance wellness, slow aging, and improve overall health outcomes.',
                keywords: 'peptide therapy, wellness, anti-aging, health optimization',
                tags: ['Peptides', 'Wellness', 'Anti-Aging', 'Health Optimization', 'Therapy']
            },
            {
                slug: 'hormone-balance',
                title: 'Hormone Optimization: Finding Your Balance',
                category: 'Health',
                date: 'May 28, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/hormonebalance.png',
                metaDescription: 'Discover the importance of hormone balance and how personalized hormone therapy can improve quality of life.',
                keywords: 'hormone balance, hormone optimization, BHRT, wellness',
                tags: ['Hormone Balance', 'Optimization', 'Wellness', 'BHRT', 'Health']
            },
            {
                slug: 'hormone-optimization',
                title: 'Advanced Hormone Optimization Strategies',
                category: 'Health',
                date: 'May 20, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/hormone-optimization.png',
                metaDescription: 'Advanced strategies for hormone optimization and personalized treatment approaches.',
                keywords: 'hormone optimization, advanced therapy, personalized medicine',
                tags: ['Hormone Optimization', 'Advanced Therapy', 'Personalized Medicine', 'Health']
            },
            {
                slug: 'perimenopause-transition',
                title: 'Navigating the Hormonal Rollercoaster: Understanding the Perimenopause Transition',
                category: 'Women\'s Health',
                date: 'June 15, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/perimenopause-transition.png',
                metaDescription: 'Explore the complex hormonal changes that occur during perimenopause and learn how to manage symptoms effectively.',
                keywords: 'perimenopause, hormone changes, women\'s health, menopause transition',
                tags: ['Perimenopause', 'Women\'s Health', 'Hormone Changes', 'Menopause', 'Health']
            },
            {
                slug: 'gut-brain-connection',
                title: 'The Gut-Brain Connection: How Your Microbiome Affects Mental Health',
                category: 'Research',
                date: 'April 15, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/gut-brain.png',
                metaDescription: 'Discover the fascinating connection between gut health and mental wellness, and how compounded treatments can help.',
                keywords: 'gut-brain connection, microbiome, mental health, wellness',
                tags: ['Gut Health', 'Mental Health', 'Microbiome', 'Research', 'Wellness']
            },
            {
                slug: 'supplement-quality',
                title: 'The Importance of Pharmaceutical-Grade Supplements',
                category: 'Health',
                date: 'March 20, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/supplement-quality.png',
                metaDescription: 'Learn why pharmaceutical-grade supplements matter and how quality affects therapeutic outcomes.',
                keywords: 'pharmaceutical grade supplements, quality, health, nutrition',
                tags: ['Supplements', 'Quality', 'Pharmaceutical Grade', 'Health', 'Nutrition']
            },
            {
                slug: 'taurine-glycine-l-citrulline-aging-explorer',
                title: 'Taurine, Glycine & L-Citrulline: The Anti-Aging Explorer\'s Guide',
                category: 'Wellness',
                date: 'February 10, 2023',
                author: 'NP Labs Team',
                featured: false,
                image: '../../images/blog/anti-aging-supplements.png',
                metaDescription: 'Explore the anti-aging benefits of taurine, glycine, and L-citrulline and how these compounds support healthy aging.',
                keywords: 'taurine, glycine, L-citrulline, anti-aging, supplements',
                tags: ['Anti-Aging', 'Taurine', 'Glycine', 'L-Citrulline', 'Supplements', 'Wellness']
            }
        ];

        // Add content to each post by fetching from the actual HTML files
        for (let post of blogPosts) {
            try {
                post.content = await this.extractContentFromPost(post.slug);
            } catch (error) {
                console.warn(`Could not extract content for ${post.slug}:`, error);
                post.content = this.getDefaultContent(post.title);
            }
        }

        // Sort by date (newest first)
        blogPosts.sort((a, b) => new Date(b.date) - new Date(a.date));

        return blogPosts;
    },

    /**
     * Extract content from a blog post HTML file
     */
    async extractContentFromPost(slug) {
        try {
            const response = await fetch(`../blog/posts/${slug}.html`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Extract content from the blog post content section
            const contentSection = doc.querySelector('.blog-post-content article') ||
                                 doc.querySelector('.blog-post-content') ||
                                 doc.querySelector('article');

            if (contentSection) {
                // Remove unwanted elements
                const elementsToRemove = contentSection.querySelectorAll('.blog-post-tags, .blog-post-share, .author-section, .related-posts, .cta-section, h3:contains("References"), ol');
                elementsToRemove.forEach(el => el.remove());

                // Get the cleaned HTML content
                let content = contentSection.innerHTML;

                // Clean up the content
                content = content
                    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // Remove scripts
                    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // Remove styles
                    .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
                    .replace(/\s+/g, ' ') // Normalize whitespace
                    .trim();

                console.log(`Extracted content for ${slug}: ${content.length} characters`);
                return content;
            } else {
                throw new Error('Could not find content section');
            }
        } catch (error) {
            console.warn(`Failed to extract content for ${slug}:`, error);
            return this.getDefaultContent(slug);
        }
    },

    /**
     * Get default content for a post when extraction fails
     */
    getDefaultContent(title) {
        return `<h2>Introduction</h2><p>This is a comprehensive article about ${title.toLowerCase()}. The content covers important aspects of this topic and provides valuable insights for readers interested in healthcare and wellness.</p><h3>Key Points</h3><p>This article explores various aspects of the topic, including:</p><ul><li>Background information and context</li><li>Current research and developments</li><li>Practical applications and benefits</li><li>Future considerations and trends</li></ul><h3>Conclusion</h3><p>Understanding ${title.toLowerCase()} is important for making informed healthcare decisions. This article provides a foundation for further exploration of this important topic.</p>`;
    },

    /**
     * Load category statistics
     */
    async loadCategoryStats() {
        const container = document.getElementById('categoryStats');
        
        try {
            const stats = await this.getBlogStats();
            const categories = stats.categories || {};
            
            const categoryNames = {
                health: 'Health',
                wellness: 'Wellness',
                medicine: 'Personalized Medicine',
                research: 'Research'
            };
            
            container.innerHTML = Object.entries(categoryNames).map(([key, name]) => `
                <div class="category-stat-item">
                    <h4>${name}</h4>
                    <div class="category-stat-count">${categories[key] || 0}</div>
                    <div class="category-stat-label">Posts</div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Failed to load category stats:', error);
            container.innerHTML = `
                <div class="loading-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load category statistics</p>
                </div>
            `;
        }
    },

    /**
     * Calculate word count from post content
     */
    calculateWordCount(post) {
        if (!post.content) return 0;

        // Remove HTML tags and calculate word count
        const textContent = post.content.replace(/<[^>]*>/g, '');
        const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
        return words.length;
    },

    /**
     * Render post image with proper fallbacks
     */
    renderPostImage(post) {
        // If no image, show placeholder
        if (!post.image) {
            return `
                <div class="post-image-placeholder">
                    <i class="fas fa-file-alt"></i>
                </div>
            `;
        }

        // Use fallback SVG data URL
        const fallbackSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjhGOUZBIi8+CjxyZWN0IHg9IjE1IiB5PSIyMCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iI0U5RUNFRiIvPgo8Y2lyY2xlIGN4PSIyMiIgY3k9IjI3IiByPSIzIiBmaWxsPSIjREVFMkU2Ii8+CjxwYXRoIGQ9Ik0xOCAzNUwyMiAzMUwyNiAzNUwzMCAzMUwzNCAzNSIgc3Ryb2tlPSIjREVFMkU2IiBzdHJva2Utd2lkdGg9IjEiLz4KPHRleHQgeD0iMzAiIHk9IjUwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iOCIgZmlsbD0iIzZDNzU3RCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UG9zdDwvdGV4dD4KPHN2Zz4K';

        return `
            <img src="${post.image}"
                 alt="${AdminUtils.escapeHtml(post.title)}"
                 onerror="this.onerror=null; this.src='${fallbackSvg}'">
        `;
    },

    /**
     * Normalize image paths to prevent 404 errors
     */
    normalizeImagePath(imagePath) {
        if (!imagePath) return null;

        // For development mode, be more conservative with images
        // If the image path looks problematic, return null to use placeholder
        const problematicPatterns = [
            /admin\/assets\/images\//,  // Admin assets that likely don't exist
            /images\/blog\/.*\.png$/,   // Direct images/blog paths that are often wrong
            /%20/,                      // URL encoded spaces often indicate issues
        ];

        for (const pattern of problematicPatterns) {
            if (pattern.test(imagePath)) {
                console.warn(`Skipping potentially problematic image path: ${imagePath}`);
                return null; // Use placeholder instead
            }
        }

        // Only allow images that are likely to exist
        if (imagePath.startsWith('http') || imagePath.startsWith('data:')) {
            return imagePath; // External URLs and data URLs are fine
        }

        // For local paths, be very conservative
        if (imagePath.includes('blog/assets/images/') && !imagePath.includes('%')) {
            return imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
        }

        // If we can't be sure the image exists, use placeholder
        return null;
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Refresh dashboard data every 5 minutes
        setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
        
        // Handle visibility change to refresh when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadDashboardData();
            }
        });
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dashboard;
}

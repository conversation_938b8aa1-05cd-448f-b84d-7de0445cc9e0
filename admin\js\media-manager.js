/**
 * NP Labs Media Manager
 * 
 * Handles media library functionality including upload, display, and management.
 */

window.MediaManager = {
    mediaFiles: [],
    filteredFiles: [],
    currentViewMode: 'grid',
    selectedFile: null,
    
    /**
     * Initialize the media manager
     */
    async init() {
        try {
            // Add a small delay to prevent race conditions
            await new Promise(resolve => setTimeout(resolve, 100));

            await this.loadMediaLibrary();
            this.setupEventListeners();
            this.renderMediaLibrary();
            this.updateStats();

            console.log('Media manager initialized successfully');
        } catch (error) {
            console.error('Media manager initialization failed:', error);
            AdminUtils.showNotification('Failed to load media library', 'error');
        }
    },

    /**
     * Load media library from existing images
     */
    async loadMediaLibrary() {
        try {
            // Simulate loading existing media files
            this.mediaFiles = await this.getExistingMediaFiles();
            this.filteredFiles = [...this.mediaFiles];
            
        } catch (error) {
            console.error('Failed to load media library:', error);
            this.mediaFiles = [];
            this.filteredFiles = [];
        }
    },

    /**
     * Get existing media files from the blog images directory
     */
    async getExistingMediaFiles() {
        // Use only existing media files with correct paths
        const existingFiles = [
            {
                id: 'pediatric-compounding-1',
                filename: 'Pediatric-Compounding-1.png',
                path: '../images/blog/Pediatric-Compounding-1.png',
                size: 245760, // 240KB
                type: 'image/png',
                uploadDate: new Date('2024-07-02'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/Pediatric-Compounding-1.png'
            },
            {
                id: 'hormone-balance',
                filename: 'hormonebalance.png',
                path: '../images/blog/hormonebalance.png',
                size: 189440, // 185KB
                type: 'image/png',
                uploadDate: new Date('2024-05-28'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/hormonebalance.png'
            },
            {
                id: 'ldn-therapy',
                filename: 'ldn-therapy.png',
                path: '../images/blog/ldn-therapy.png',
                size: 167936, // 164KB
                type: 'image/png',
                uploadDate: new Date('2024-06-10'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/ldn-therapy.png'
            },
            {
                id: 'personalized-medication',
                filename: 'personalized-medication.png',
                path: '../images/blog/personalized-medication.png',
                size: 198656, // 194KB
                type: 'image/png',
                uploadDate: new Date('2024-06-15'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/personalized-medication.png'
            },

            {
                id: 'bioidentical-hormones',
                filename: 'bioidentical-hormones.png',
                path: '../images/blog/bioidentical-hormones.png',
                size: 203776, // 199KB
                type: 'image/png',
                uploadDate: new Date('2024-05-01'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/bioidentical-hormones.png'
            },
            {
                id: 'gut-brain-connection',
                filename: 'gutbrainconnection.png',
                path: '../images/blog/gutbrainconnection.png',
                size: 187392, // 183KB
                type: 'image/png',
                uploadDate: new Date('2024-04-15'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/gutbrainconnection.png'
            },
            {
                id: 'ldn-therapy-alt',
                filename: 'Low Dose Naltrexone Therapy.png',
                path: '../images/blog/Low Dose Naltrexone Therapy.png',
                size: 198432, // 194KB
                type: 'image/png',
                uploadDate: new Date('2024-06-08'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/Low Dose Naltrexone Therapy.png'
            },

            {
                id: 'author-elena',
                filename: 'author-elena.jpg',
                path: '../images/blog/author-elena.jpg',
                size: 45120, // 44KB
                type: 'image/jpeg',
                uploadDate: new Date('2024-05-15'),
                dimensions: { width: 300, height: 300 },
                url: '../images/blog/author-elena.jpg'
            },
            {
                id: 'author-thomas',
                filename: 'author-thomas.jpg',
                path: '../images/blog/author-thomas.jpg',
                size: 48640, // 47KB
                type: 'image/jpeg',
                uploadDate: new Date('2024-05-15'),
                dimensions: { width: 300, height: 300 },
                url: '../images/blog/author-thomas.jpg'
            },
            {
                id: 'default-author',
                filename: 'default-author.jpg',
                path: '../images/blog/default-author.jpg',
                size: 32768, // 32KB
                type: 'image/jpeg',
                uploadDate: new Date('2024-04-01'),
                dimensions: { width: 300, height: 300 },
                url: '../images/blog/default-author.jpg'
            },
            {
                id: 'default-post',
                filename: 'default-post.svg',
                path: '../images/blog/default-post.svg',
                size: 8192, // 8KB
                type: 'image/svg+xml',
                uploadDate: new Date('2024-04-01'),
                dimensions: { width: 800, height: 600 },
                url: '../images/blog/default-post.svg'
            }
        ];

        // Sort by upload date (newest first)
        return existingFiles.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // File input change
        const fileInput = document.getElementById('fileInput');
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        // Drag and drop
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });

        // Click to upload
        uploadArea.addEventListener('click', (e) => {
            if (e.target === uploadArea || e.target.closest('.upload-content')) {
                fileInput.click();
            }
        });
    },

    /**
     * Handle file selection
     */
    async handleFileSelect(files) {
        const validFiles = Array.from(files).filter(file => this.validateFile(file));
        
        if (validFiles.length === 0) {
            AdminUtils.showNotification('No valid files selected', 'warning');
            return;
        }

        if (validFiles.length !== files.length) {
            AdminUtils.showNotification(`${files.length - validFiles.length} files were rejected due to invalid format or size`, 'warning');
        }

        await this.uploadFiles(validFiles);
    },

    /**
     * Validate file
     */
    validateFile(file) {
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type)) {
            return false;
        }

        // Check file size (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            return false;
        }

        return true;
    },

    /**
     * Upload files (simulated)
     */
    async uploadFiles(files) {
        this.showUploadProgress();
        
        try {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;
                
                // Simulate upload delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Create file object
                const fileObj = await this.createFileObject(file);
                this.mediaFiles.unshift(fileObj); // Add to beginning
                
                // Update progress
                this.updateUploadProgress(progress, `Uploaded ${file.name}`);
            }
            
            // Update display
            this.filteredFiles = [...this.mediaFiles];
            this.renderMediaLibrary();
            this.updateStats();
            
            // Hide upload modal
            setTimeout(() => {
                this.hideUploadModal();
                AdminUtils.showNotification(`Successfully uploaded ${files.length} file(s)`, 'success');
            }, 500);
            
        } catch (error) {
            console.error('Upload failed:', error);
            AdminUtils.showNotification('Upload failed', 'error');
            this.hideUploadProgress();
        }
    },

    /**
     * Create file object from uploaded file
     */
    async createFileObject(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const fileObj = {
                    id: 'uploaded-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                    filename: file.name,
                    path: e.target.result, // Data URL for preview
                    size: file.size,
                    type: file.type,
                    uploadDate: new Date(),
                    dimensions: { width: 0, height: 0 }, // Would be calculated in real implementation
                    url: e.target.result
                };
                
                // Get image dimensions if it's an image
                if (file.type.startsWith('image/')) {
                    const img = new Image();
                    img.onload = () => {
                        fileObj.dimensions = { width: img.width, height: img.height };
                        resolve(fileObj);
                    };
                    img.src = e.target.result;
                } else {
                    resolve(fileObj);
                }
            };
            reader.readAsDataURL(file);
        });
    },

    /**
     * Show upload progress
     */
    showUploadProgress() {
        document.getElementById('uploadArea').style.display = 'none';
        document.getElementById('uploadProgress').style.display = 'block';
        this.updateUploadProgress(0, 'Preparing upload...');
    },

    /**
     * Hide upload progress
     */
    hideUploadProgress() {
        document.getElementById('uploadArea').style.display = 'block';
        document.getElementById('uploadProgress').style.display = 'none';
    },

    /**
     * Update upload progress
     */
    updateUploadProgress(percent, status) {
        document.getElementById('progressFill').style.width = percent + '%';
        document.getElementById('progressText').textContent = Math.round(percent) + '%';
        document.getElementById('uploadStatus').textContent = status;
    },

    /**
     * Apply filters to media files
     */
    applyFilters() {
        const fileType = document.getElementById('fileTypeFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        const searchTerm = document.getElementById('searchFiles').value.toLowerCase();

        // Filter files
        this.filteredFiles = this.mediaFiles.filter(file => {
            // File type filter
            let matchesType = true;
            if (fileType !== 'all') {
                if (fileType === 'image') {
                    matchesType = file.type.startsWith('image/');
                } else {
                    matchesType = file.type.includes(fileType);
                }
            }

            // Search filter
            const matchesSearch = !searchTerm || 
                file.filename.toLowerCase().includes(searchTerm);

            return matchesType && matchesSearch;
        });

        // Sort files
        this.filteredFiles.sort((a, b) => {
            switch (sortBy) {
                case 'date-desc':
                    return new Date(b.uploadDate) - new Date(a.uploadDate);
                case 'date-asc':
                    return new Date(a.uploadDate) - new Date(b.uploadDate);
                case 'name-asc':
                    return a.filename.localeCompare(b.filename);
                case 'name-desc':
                    return b.filename.localeCompare(a.filename);
                case 'size-desc':
                    return b.size - a.size;
                case 'size-asc':
                    return a.size - b.size;
                default:
                    return 0;
            }
        });

        this.renderMediaLibrary();
    },

    /**
     * Set view mode
     */
    setViewMode(mode) {
        this.currentViewMode = mode;
        
        // Update button states
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${mode}"]`).classList.add('active');
        
        // Show/hide appropriate containers
        const gridContainer = document.getElementById('mediaGrid');
        const listContainer = document.getElementById('mediaList');
        
        if (mode === 'grid') {
            gridContainer.style.display = 'grid';
            listContainer.style.display = 'none';
        } else {
            gridContainer.style.display = 'none';
            listContainer.style.display = 'flex';
        }
        
        this.renderMediaLibrary();
    },

    /**
     * Render media library
     */
    renderMediaLibrary() {
        if (this.filteredFiles.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();

        if (this.currentViewMode === 'grid') {
            this.renderGridView();
        } else {
            this.renderListView();
        }
    },

    /**
     * Render grid view
     */
    renderGridView() {
        const container = document.getElementById('mediaGrid');
        container.innerHTML = this.filteredFiles.map(file => this.renderMediaItem(file)).join('');
    },

    /**
     * Render list view
     */
    renderListView() {
        const container = document.getElementById('mediaList');
        container.innerHTML = this.filteredFiles.map(file => this.renderMediaListItem(file)).join('');
    },

    /**
     * Render individual media item (grid)
     */
    renderMediaItem(file) {
        const isImage = file.type.startsWith('image/');
        const fileSize = this.formatFileSize(file.size);
        const uploadDate = this.formatDate(file.uploadDate);

        return `
            <div class="media-item" onclick="MediaManager.showImageDetails('${file.id}')">
                <div class="media-thumbnail">
                    ${isImage ?
                        `<img src="${file.url}" alt="${AdminUtils.escapeHtml(file.filename)}" loading="lazy"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="image-error" style="display:none; align-items:center; justify-content:center; height:100%; background:#f5f5f5; color:#666;">
                             <i class="fas fa-exclamation-triangle"></i>
                         </div>` :
                        `<i class="fas fa-file file-icon"></i>`
                    }
                </div>
                <div class="media-info">
                    <div class="media-filename" title="${AdminUtils.escapeHtml(file.filename)}">
                        ${AdminUtils.escapeHtml(file.filename)}
                    </div>
                    <div class="media-details">
                        <span class="media-size">${fileSize}</span>
                        <span class="media-date">${uploadDate}</span>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * Render individual media item (list)
     */
    renderMediaListItem(file) {
        const isImage = file.type.startsWith('image/');
        const fileSize = this.formatFileSize(file.size);
        const uploadDate = this.formatDate(file.uploadDate);
        const dimensions = file.dimensions ? `${file.dimensions.width}×${file.dimensions.height}` : 'Unknown';

        return `
            <div class="media-list-item" onclick="MediaManager.showImageDetails('${file.id}')">
                <div class="media-list-thumbnail">
                    ${isImage ?
                        `<img src="${file.url}" alt="${AdminUtils.escapeHtml(file.filename)}" loading="lazy"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="image-error" style="display:none; align-items:center; justify-content:center; height:100%; background:#f5f5f5; color:#666;">
                             <i class="fas fa-exclamation-triangle"></i>
                         </div>` :
                        `<i class="fas fa-file file-icon"></i>`
                    }
                </div>
                <div class="media-list-info">
                    <div class="media-list-filename" title="${AdminUtils.escapeHtml(file.filename)}">
                        ${AdminUtils.escapeHtml(file.filename)}
                    </div>
                    <div class="media-list-details">
                        <span>Size: ${fileSize}</span>
                        <span>Dimensions: ${dimensions}</span>
                        <span>Uploaded: ${uploadDate}</span>
                    </div>
                </div>
                <div class="media-list-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); MediaManager.copyToClipboard('${file.url}')">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); MediaManager.deleteFile('${file.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * Show empty state
     */
    showEmptyState() {
        document.getElementById('mediaGrid').style.display = 'none';
        document.getElementById('mediaList').style.display = 'none';
        document.getElementById('emptyState').style.display = 'block';
    },

    /**
     * Hide empty state
     */
    hideEmptyState() {
        document.getElementById('emptyState').style.display = 'none';
        if (this.currentViewMode === 'grid') {
            document.getElementById('mediaGrid').style.display = 'grid';
        } else {
            document.getElementById('mediaList').style.display = 'flex';
        }
    },

    /**
     * Update statistics
     */
    updateStats() {
        const totalFiles = this.mediaFiles.length;
        const totalSize = this.mediaFiles.reduce((sum, file) => sum + file.size, 0);
        const imageFiles = this.mediaFiles.filter(file => file.type.startsWith('image/')).length;

        // Recent uploads (last 7 days)
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const recentUploads = this.mediaFiles.filter(file =>
            new Date(file.uploadDate) > weekAgo
        ).length;

        document.getElementById('totalFiles').textContent = totalFiles;
        document.getElementById('totalSize').textContent = this.formatFileSize(totalSize);
        document.getElementById('recentUploads').textContent = recentUploads;
        document.getElementById('imageFiles').textContent = imageFiles;
    },

    /**
     * Show image details modal
     */
    showImageDetails(fileId) {
        const file = this.mediaFiles.find(f => f.id === fileId);
        if (!file) return;

        this.selectedFile = file;

        // Populate modal
        document.getElementById('imageModalTitle').textContent = file.filename;
        document.getElementById('imagePreview').src = file.url;
        document.getElementById('imageFilename').textContent = file.filename;
        document.getElementById('imageFileSize').textContent = this.formatFileSize(file.size);
        document.getElementById('imageDimensions').textContent =
            file.dimensions ? `${file.dimensions.width} × ${file.dimensions.height} pixels` : 'Unknown';
        document.getElementById('imageUploadDate').textContent = this.formatDate(file.uploadDate);
        document.getElementById('imageUrl').value = file.url;

        // Show modal
        document.getElementById('imageModal').style.display = 'flex';
    },

    /**
     * Hide image details modal
     */
    hideImageModal() {
        document.getElementById('imageModal').style.display = 'none';
        this.selectedFile = null;
    },

    /**
     * Show upload modal
     */
    showUploadModal() {
        document.getElementById('uploadModal').style.display = 'flex';
        this.hideUploadProgress();

        // Reset file input
        document.getElementById('fileInput').value = '';
    },

    /**
     * Hide upload modal
     */
    hideUploadModal() {
        document.getElementById('uploadModal').style.display = 'none';
        this.hideUploadProgress();
    },

    /**
     * Copy text to clipboard
     */
    async copyToClipboard(text) {
        try {
            // If text is an element ID, get the value
            if (typeof text === 'string' && document.getElementById(text)) {
                text = document.getElementById(text).value;
            }

            await navigator.clipboard.writeText(text);
            AdminUtils.showNotification('Copied to clipboard!', 'success', 2000);
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            AdminUtils.showNotification('Failed to copy to clipboard', 'error');
        }
    },

    /**
     * Insert image into post (placeholder functionality)
     */
    insertIntoPost() {
        if (!this.selectedFile) return;

        // This would integrate with the post editor
        AdminUtils.showNotification('Image URL copied to clipboard. Paste it into your post editor.', 'info');
        this.copyToClipboard(this.selectedFile.url);
        this.hideImageModal();
    },

    /**
     * Delete image
     */
    async deleteImage() {
        if (!this.selectedFile) return;

        if (!confirm(`Are you sure you want to delete "${this.selectedFile.filename}"? This action cannot be undone.`)) {
            return;
        }

        try {
            // In a real implementation, this would call an API to delete the file
            // For now, just remove from the array
            this.mediaFiles = this.mediaFiles.filter(f => f.id !== this.selectedFile.id);
            this.filteredFiles = this.filteredFiles.filter(f => f.id !== this.selectedFile.id);

            this.renderMediaLibrary();
            this.updateStats();
            this.hideImageModal();

            AdminUtils.showNotification('Image deleted successfully', 'success');
        } catch (error) {
            console.error('Delete failed:', error);
            AdminUtils.showNotification('Failed to delete image', 'error');
        }
    },

    /**
     * Delete file (from list view)
     */
    async deleteFile(fileId) {
        const file = this.mediaFiles.find(f => f.id === fileId);
        if (!file) return;

        if (!confirm(`Are you sure you want to delete "${file.filename}"? This action cannot be undone.`)) {
            return;
        }

        try {
            this.mediaFiles = this.mediaFiles.filter(f => f.id !== fileId);
            this.filteredFiles = this.filteredFiles.filter(f => f.id !== fileId);

            this.renderMediaLibrary();
            this.updateStats();

            AdminUtils.showNotification('File deleted successfully', 'success');
        } catch (error) {
            console.error('Delete failed:', error);
            AdminUtils.showNotification('Failed to delete file', 'error');
        }
    },

    /**
     * Refresh media library
     */
    async refreshLibrary() {
        try {
            AdminUtils.showNotification('Refreshing media library...', 'info', 2000);
            await this.loadMediaLibrary();
            this.renderMediaLibrary();
            this.updateStats();
            AdminUtils.showNotification('Media library refreshed', 'success', 2000);
        } catch (error) {
            console.error('Refresh failed:', error);
            AdminUtils.showNotification('Failed to refresh media library', 'error');
        }
    },

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Format date
     */
    formatDate(date) {
        if (!date) return 'Unknown';

        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    /**
     * Comprehensive image validation with duplicate detection
     */
    async validateImageLoading() {
        console.log('🔍 Starting comprehensive image validation...');
        AdminUtils.showNotification('Validating images and checking for duplicates...', 'info');

        const results = {
            total: this.mediaFiles.length,
            loaded: 0,
            failed: 0,
            errors: [],
            duplicates: [],
            summary: ''
        };

        // Check for duplicate filenames
        const filenameMap = new Map();
        for (const file of this.mediaFiles) {
            if (filenameMap.has(file.filename)) {
                results.duplicates.push({
                    filename: file.filename,
                    ids: [filenameMap.get(file.filename), file.id]
                });
            } else {
                filenameMap.set(file.filename, file.id);
            }
        }

        // Validate image loading
        for (const file of this.mediaFiles) {
            if (file.type.startsWith('image/')) {
                try {
                    await this.testImageLoad(file.url);
                    results.loaded++;
                    console.log(`✅ ${file.filename} loaded successfully`);
                } catch (error) {
                    results.failed++;
                    results.errors.push({
                        filename: file.filename,
                        url: file.url,
                        error: error.message
                    });
                    console.error(`❌ ${file.filename} failed to load:`, error.message);
                }
            } else {
                results.loaded++; // Non-images count as loaded
            }
        }

        // Generate summary
        const successRate = Math.round((results.loaded / results.total) * 100);
        results.summary = `🔍 VALIDATION COMPLETE:\n`;
        results.summary += `✅ ${results.loaded}/${results.total} files loaded successfully (${successRate}%)\n`;

        if (results.failed > 0) {
            results.summary += `❌ ${results.failed} files failed to load\n`;
        }

        if (results.duplicates.length > 0) {
            results.summary += `⚠️ ${results.duplicates.length} duplicate filename(s) detected\n`;
        }

        if (results.failed === 0 && results.duplicates.length === 0) {
            results.summary += `🎉 Perfect! All images loading correctly with no duplicates!`;
            AdminUtils.showNotification(results.summary, 'success');
        } else {
            AdminUtils.showNotification(results.summary, 'warning');
        }

        console.log('📊 Comprehensive validation results:', results);
        return results;
    },

    /**
     * Test if an image can be loaded
     */
    testImageLoad(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
            img.src = url;
        });
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MediaManager;
}

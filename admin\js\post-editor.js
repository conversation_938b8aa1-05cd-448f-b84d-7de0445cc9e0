/**
 * NP Labs Blog Post Editor
 * 
 * Handles post creation and editing functionality including rich text editing,
 * image uploads, auto-save, and preview generation.
 */

window.PostEditor = {
    quill: null,
    currentPost: null,
    isDraft: false,
    tags: [],
    isEditMode: false,
    editSlug: null,
    
    /**
     * Initialize the post editor
     */
    init() {
        this.setupQuillEditor();
        this.setupEventListeners();
        this.setupImageUpload();
        this.setupTagsInput();
        this.setupAutoSave();
        this.setupCollapsibleSections();

        // Check if we're in edit mode
        const urlParams = new URLSearchParams(window.location.search);
        const slug = urlParams.get('slug');
        if (slug) {
            this.isEditMode = true;
            this.editSlug = slug;
            this.setupEditMode();
            this.loadPost(slug);
        }
    },

    /**
     * Set up edit mode UI
     */
    setupEditMode() {
        // Update page title and headers
        document.getElementById('pageTitle').textContent = 'Edit Post - NP Labs Blog Admin';
        document.getElementById('editorTitle').textContent = 'Edit Post';
        document.getElementById('editorSubtitle').textContent = 'Update and modify your blog article';

        // Update publish button text
        const publishBtn = document.getElementById('publishBtn');
        publishBtn.innerHTML = '<i class="fas fa-save"></i> Update Post';
    },

    /**
     * Load existing post for editing
     */
    async loadPost(slug) {
        try {
            AdminUtils.showNotification('Loading post...', 'info', 2000);

            // Try to load from API
            let postData;
            if (!AdminAuth.isDevelopmentMode()) {
                postData = await AdminAPI.get(`/posts/${slug}`);
            } else {
                // For development, simulate loading post data
                postData = await this.simulateLoadPost(slug);
            }

            if (postData) {
                this.populateForm(postData);
                AdminUtils.showNotification('Post loaded successfully', 'success', 2000);
            } else {
                throw new Error('Post not found');
            }

        } catch (error) {
            console.error('Failed to load post:', error);
            AdminUtils.showNotification('Failed to load post. You can still edit, but data may not be current.', 'warning');

            // Try to extract some data from the URL for basic editing
            this.populateBasicData(slug);
        }
    },

    /**
     * Simulate loading post data for development
     */
    async simulateLoadPost(slug) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Return sample post data based on slug
        const samplePosts = {
            'future-of-personalized-medicine': {
                title: 'The Future of Personalized Medicine',
                slug: 'future-of-personalized-medicine',
                category: 'medicine',
                featured: true,
                content: `<h2>Introduction to Personalized Medicine</h2>
                <p>Personalized medicine represents a revolutionary approach to healthcare that tailors medical treatment to the individual characteristics of each patient. This innovative field considers factors such as genetics, environment, and lifestyle to develop targeted therapies that are more effective and have fewer side effects than traditional one-size-fits-all treatments.</p>

                <h3>The Science Behind Personalization</h3>
                <p>At the core of personalized medicine lies the understanding that each person's genetic makeup influences how they respond to medications. Pharmacogenomics, the study of how genes affect drug response, allows healthcare providers to:</p>
                <ul>
                    <li>Predict which medications will be most effective</li>
                    <li>Determine optimal dosages for individual patients</li>
                    <li>Identify potential adverse reactions before they occur</li>
                    <li>Reduce trial-and-error prescribing</li>
                </ul>

                <h3>Applications in Modern Healthcare</h3>
                <p>Personalized medicine is already making significant impacts across various medical specialties, from oncology to psychiatry. Cancer treatment, in particular, has been transformed by targeted therapies that attack specific genetic mutations found in tumors.</p>

                <blockquote>
                    <p>"The future of medicine is not just about treating disease, but about preventing it through personalized approaches that consider each patient's unique biological profile."</p>
                </blockquote>

                <h3>The Role of Compounding Pharmacies</h3>
                <p>Compounding pharmacies play a crucial role in personalized medicine by creating customized medications that meet individual patient needs. This includes adjusting dosages, removing allergens, and creating alternative delivery methods for patients who cannot take standard formulations.</p>`,
                metaDescription: 'Explore how personalized medicine is transforming healthcare with tailored treatments based on individual genetic profiles and patient characteristics.',
                metaKeywords: 'personalized medicine, pharmacogenomics, custom treatment, targeted therapy, compounding pharmacy',
                tags: ['medicine', 'healthcare', 'innovation', 'genetics'],
                imageAlt: 'Personalized medicine concept with DNA helix and medical symbols'
            },
            'pediatric-compounding': {
                title: 'Pediatric Compounding: Tailored Medications for Children',
                slug: 'pediatric-compounding',
                category: 'medicine',
                featured: false,
                content: `<h2>Why Pediatric Compounding Matters</h2>
                <p>Children are not just small adults when it comes to medication. Their developing bodies process drugs differently, and they often require specialized formulations that are not available in commercial medications. Pediatric compounding addresses these unique needs by creating customized medications specifically designed for young patients.</p>

                <h3>Unique Challenges in Pediatric Medicine</h3>
                <p>Healthcare providers face several challenges when treating children:</p>
                <ul>
                    <li><strong>Dosage Accuracy:</strong> Children require precise dosing based on weight and age</li>
                    <li><strong>Palatability:</strong> Medications must taste good to ensure compliance</li>
                    <li><strong>Delivery Methods:</strong> Alternative forms may be needed for children who can't swallow pills</li>
                    <li><strong>Allergen Considerations:</strong> Many children have allergies to common medication ingredients</li>
                </ul>

                <h3>Common Pediatric Compounding Solutions</h3>
                <p>Our compounding pharmacy offers various solutions for pediatric patients:</p>

                <h4>Flavoring Systems</h4>
                <p>We use advanced flavoring systems to make medications more palatable for children. Popular flavors include bubble gum, cherry, and grape, making medicine time less stressful for both children and parents.</p>

                <h4>Alternative Dosage Forms</h4>
                <p>When traditional tablets or capsules aren't suitable, we can create:</p>
                <ul>
                    <li>Liquid suspensions with accurate dosing</li>
                    <li>Topical gels and creams</li>
                    <li>Suppositories for children who can't take oral medications</li>
                    <li>Lollipops and gummies for certain medications</li>
                </ul>

                <h3>Safety and Quality Assurance</h3>
                <p>Pediatric compounding requires the highest standards of safety and quality. Our pharmacy follows strict protocols to ensure every medication is accurately prepared and thoroughly tested before dispensing.</p>`,
                metaDescription: 'Learn about pediatric compounding and how custom medications help children with specialized dosing, flavoring, and delivery methods.',
                metaKeywords: 'pediatric compounding, children medication, custom pharmacy, pediatric dosing, medication flavoring',
                tags: ['pediatric', 'compounding', 'children', 'safety'],
                imageAlt: 'Pediatric pharmacy compounding with child-friendly medications'
            }
        };

        // Return specific post data if available, otherwise create generic content
        if (samplePosts[slug]) {
            return samplePosts[slug];
        }

        // Generate generic post data for any other slug
        const title = slug.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');

        return {
            title: title,
            slug: slug,
            category: 'health',
            featured: false,
            content: `<h2>Introduction</h2>
            <p>This is a sample blog post about ${title.toLowerCase()}. You can edit this content to create your actual blog post.</p>

            <h3>Key Points</h3>
            <p>Here are some important points to consider:</p>
            <ul>
                <li>Point one about the topic</li>
                <li>Point two with more details</li>
                <li>Point three for comprehensive coverage</li>
            </ul>

            <h3>Conclusion</h3>
            <p>In conclusion, ${title.toLowerCase()} is an important topic that deserves careful consideration. This post provides a foundation that you can build upon with your expertise and insights.</p>

            <p><strong>Note:</strong> This is sample content. Please replace with your actual blog post content.</p>`,
            metaDescription: `Learn about ${title.toLowerCase()} and its importance in healthcare and wellness.`,
            metaKeywords: `${title.toLowerCase()}, health, wellness, healthcare`,
            tags: ['health', 'wellness'],
            imageAlt: `${title} related image`
        };
    },

    /**
     * Populate form with post data
     */
    populateForm(postData) {
        // Basic information
        document.getElementById('postTitle').value = postData.title || '';
        document.getElementById('postSlug').value = postData.slug || '';
        document.getElementById('postCategory').value = postData.category || '';
        document.getElementById('featuredToggle').checked = postData.featured || false;

        // Content
        if (postData.content) {
            console.log('Loading content:', postData.content.substring(0, 100) + '...');
            this.quill.root.innerHTML = postData.content;
            this.updateContentStats();
            this.updateHiddenTextarea();
            console.log('Content loaded successfully. Word count:', this.quill.getText().trim().split(/\s+/).length);
        }

        // SEO fields
        document.getElementById('metaDescription').value = postData.metaDescription || '';
        document.getElementById('metaKeywords').value = postData.metaKeywords || '';
        document.getElementById('imageAlt').value = postData.imageAlt || '';

        // Tags
        if (postData.tags && Array.isArray(postData.tags)) {
            this.tags = [...postData.tags];
            this.renderTags();
        }

        // Update character counts
        this.updateCharCount(document.getElementById('postTitle'), 100);
        this.updateCharCount(document.getElementById('imageAlt'), 125);
        this.updateCharCount(document.getElementById('metaDescription'), 160);

        // Store current post data
        this.currentPost = postData;
    },

    /**
     * Populate basic data when full load fails
     */
    populateBasicData(slug) {
        // Convert slug to title
        const title = slug.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');

        document.getElementById('postTitle').value = title;
        document.getElementById('postSlug').value = slug;

        AdminUtils.showNotification('Loaded basic post information. Please verify all fields.', 'info');
    },

    /**
     * Set up Quill rich text editor
     */
    setupQuillEditor() {
        const toolbarOptions = [
            [{ 'header': [2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            ['blockquote', 'code-block'],
            ['link', 'image'],
            ['clean']
        ];

        this.quill = new Quill('#contentEditor', {
            theme: 'snow',
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'Start writing your blog post content here...'
        });

        // Update hidden textarea and stats on content change
        this.quill.on('text-change', () => {
            this.updateContentStats();
            this.updateHiddenTextarea();
        });
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Title input
        const titleInput = document.getElementById('postTitle');
        titleInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 100);
            this.generateSlug();
        });

        // Alt text input
        const altInput = document.getElementById('imageAlt');
        altInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 125);
        });

        // Meta description
        const metaInput = document.getElementById('metaDescription');
        metaInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 160);
        });

        // Action buttons
        document.getElementById('saveDraftBtn').addEventListener('click', () => {
            this.saveDraft();
        });

        document.getElementById('publishBtn').addEventListener('click', () => {
            this.publishPost();
        });

        document.getElementById('previewBtn').addEventListener('click', () => {
            this.showPreview();
        });

        // Form submission
        document.getElementById('postForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.publishPost();
        });
    },

    /**
     * Set up image upload functionality
     */
    setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('featuredImage');
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImageUpload(e.target.files[0]);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            if (e.dataTransfer.files.length > 0) {
                this.handleImageUpload(e.dataTransfer.files[0]);
            }
        });

        // Image actions
        document.getElementById('changeImageBtn').addEventListener('click', () => {
            fileInput.click();
        });

        document.getElementById('removeImageBtn').addEventListener('click', () => {
            this.removeImage();
        });
    },

    /**
     * Handle image upload
     */
    async handleImageUpload(file) {
        // Validate file
        const errors = AdminUtils.validateImageFile(file);
        if (errors.length > 0) {
            AdminUtils.showNotification(errors.join(' '), 'error');
            return;
        }

        try {
            // Show loading state
            const uploadArea = document.getElementById('imageUploadArea');
            const originalContent = uploadArea.innerHTML;
            uploadArea.innerHTML = `
                <div class="upload-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h4>Uploading Image...</h4>
                    <p>Please wait while we process your image</p>
                </div>
            `;

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImg = document.getElementById('previewImg');
                previewImg.src = e.target.result;
                
                document.querySelector('.upload-placeholder').style.display = 'none';
                document.getElementById('imagePreview').style.display = 'block';
            };
            reader.readAsDataURL(file);

            // In a real implementation, this would upload to the server
            // For now, we'll simulate the upload
            await new Promise(resolve => setTimeout(resolve, 1000));

            AdminUtils.showNotification('Image uploaded successfully', 'success');

        } catch (error) {
            console.error('Image upload failed:', error);
            AdminUtils.showNotification('Image upload failed. Please try again.', 'error');
            
            // Restore original content
            document.getElementById('imageUploadArea').innerHTML = originalContent;
        }
    },

    /**
     * Remove uploaded image
     */
    removeImage() {
        document.querySelector('.upload-placeholder').style.display = 'block';
        document.getElementById('imagePreview').style.display = 'none';
        document.getElementById('featuredImage').value = '';
        document.getElementById('imageAlt').value = '';
    },

    /**
     * Set up tags input
     */
    setupTagsInput() {
        const tagsInput = document.getElementById('postTags');
        const tagsDisplay = document.getElementById('tagsDisplay');

        tagsInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                this.addTag(tagsInput.value.trim());
                tagsInput.value = '';
            }
        });

        tagsInput.addEventListener('blur', () => {
            if (tagsInput.value.trim()) {
                this.addTag(tagsInput.value.trim());
                tagsInput.value = '';
            }
        });
    },

    /**
     * Add a tag
     */
    addTag(tagText) {
        if (!tagText || this.tags.includes(tagText)) {
            return;
        }

        this.tags.push(tagText);
        this.renderTags();
    },

    /**
     * Remove a tag
     */
    removeTag(tagText) {
        this.tags = this.tags.filter(tag => tag !== tagText);
        this.renderTags();
    },

    /**
     * Render tags display
     */
    renderTags() {
        const tagsDisplay = document.getElementById('tagsDisplay');
        tagsDisplay.innerHTML = this.tags.map(tag => `
            <div class="tag-item">
                ${AdminUtils.escapeHtml(tag)}
                <button type="button" class="tag-remove" onclick="PostEditor.removeTag('${AdminUtils.escapeHtml(tag)}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    },

    /**
     * Set up auto-save functionality
     */
    setupAutoSave() {
        AdminAutoSave.start('postForm', async (form) => {
            await this.saveDraft(true); // Silent save
        });
    },

    /**
     * Set up collapsible sections
     */
    setupCollapsibleSections() {
        document.querySelectorAll('.section-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const target = document.getElementById(toggle.dataset.target);
                const isCollapsed = toggle.classList.contains('collapsed');
                
                if (isCollapsed) {
                    toggle.classList.remove('collapsed');
                    target.classList.remove('collapsed');
                    target.style.maxHeight = target.scrollHeight + 'px';
                } else {
                    toggle.classList.add('collapsed');
                    target.classList.add('collapsed');
                    target.style.maxHeight = '0';
                }
            });
        });
    },

    /**
     * Update character count for input fields
     */
    updateCharCount(input, maxLength) {
        const charCount = input.parentElement.querySelector('.char-count');
        if (charCount) {
            const current = input.value.length;
            charCount.textContent = `${current}/${maxLength}`;
            
            if (current > maxLength * 0.9) {
                charCount.style.color = '#dc3545';
            } else {
                charCount.style.color = 'var(--primary-blue)';
            }
        }
    },

    /**
     * Generate URL slug from title
     */
    generateSlug() {
        const title = document.getElementById('postTitle').value;
        const slugInput = document.getElementById('postSlug');
        
        if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') {
            const slug = AdminUtils.generateSlug(title);
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    },

    /**
     * Update content statistics
     */
    updateContentStats() {
        const text = this.quill.getText();
        const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        const chars = text.length;
        const readingTime = Math.ceil(words / 200); // Average reading speed

        document.getElementById('wordCount').textContent = `${words} words`;
        document.getElementById('charCount').textContent = `${chars} characters`;
        document.getElementById('readingTime').textContent = `${readingTime} min read`;
    },

    /**
     * Update hidden textarea with Quill content
     */
    updateHiddenTextarea() {
        const content = this.quill.root.innerHTML;
        document.getElementById('contentHidden').value = content;
    },

    /**
     * Save post as draft
     */
    async saveDraft(silent = false) {
        try {
            const formData = this.getFormData();
            formData.status = 'draft';

            if (!silent) {
                const saveBtn = document.getElementById('saveDraftBtn');
                AdminUtils.showLoading(saveBtn, 'Saving...');
            }

            // In a real implementation, this would save to the server
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (!silent) {
                AdminUtils.hideLoading(document.getElementById('saveDraftBtn'));
                AdminUtils.showNotification('Draft saved successfully', 'success');
            }

            this.isDraft = true;

        } catch (error) {
            console.error('Save draft failed:', error);
            if (!silent) {
                AdminUtils.hideLoading(document.getElementById('saveDraftBtn'));
                AdminUtils.showNotification('Failed to save draft', 'error');
            }
        }
    },

    /**
     * Publish post
     */
    async publishPost() {
        try {
            const formData = this.getFormData();

            // Validate required fields
            const errors = this.validateForm(formData);
            if (errors.length > 0) {
                AdminUtils.showNotification(errors.join(' '), 'error');
                return;
            }

            const publishBtn = document.getElementById('publishBtn');
            const isUpdate = this.isEditMode;
            const loadingText = isUpdate ? 'Updating...' : 'Publishing...';

            AdminUtils.showLoading(publishBtn, loadingText);

            formData.status = 'published';
            if (!isUpdate) {
                formData.publishedAt = new Date().toISOString();
            }

            // Call appropriate API endpoint
            if (!AdminAuth.isDevelopmentMode()) {
                if (isUpdate) {
                    await AdminAPI.put(`/posts/${this.editSlug}`, formData);
                } else {
                    await AdminAPI.post('/posts', formData);
                }
            } else {
                // Simulate API call for development
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            AdminUtils.hideLoading(publishBtn);

            const successMessage = isUpdate ? 'Post updated successfully!' : 'Post published successfully!';
            AdminUtils.showNotification(successMessage, 'success');

            // Redirect to posts list after a delay
            setTimeout(() => {
                window.location.href = 'posts.html';
            }, 2000);

        } catch (error) {
            console.error('Publish/Update failed:', error);
            AdminUtils.hideLoading(document.getElementById('publishBtn'));

            const errorMessage = this.isEditMode ? 'Failed to update post' : 'Failed to publish post';
            AdminUtils.showNotification(errorMessage, 'error');
        }
    },

    /**
     * Get form data
     */
    getFormData() {
        return {
            title: document.getElementById('postTitle').value,
            slug: document.getElementById('postSlug').value,
            category: document.getElementById('postCategory').value,
            featured: document.getElementById('featuredToggle').checked,
            content: this.quill.root.innerHTML,
            imageAlt: document.getElementById('imageAlt').value,
            metaDescription: document.getElementById('metaDescription').value,
            metaKeywords: document.getElementById('metaKeywords').value,
            tags: this.tags
        };
    },

    /**
     * Validate form data
     */
    validateForm(data) {
        const errors = [];

        if (!data.title || data.title.length < 5) {
            errors.push('Title must be at least 5 characters long.');
        }

        if (!data.category) {
            errors.push('Please select a category.');
        }

        if (!data.content || this.quill.getText().trim().length < 100) {
            errors.push('Content must be at least 100 characters long.');
        }

        const hasImage = document.getElementById('imagePreview').style.display !== 'none';
        if (hasImage && !data.imageAlt) {
            errors.push('Alt text is required for the featured image.');
        }

        return errors;
    },

    /**
     * Show post preview
     */
    showPreview() {
        const formData = this.getFormData();
        
        // Validate basic requirements
        if (!formData.title || !formData.content) {
            AdminUtils.showNotification('Please add a title and content to preview', 'warning');
            return;
        }

        // Generate preview HTML
        const previewHTML = this.generatePreviewHTML(formData);
        
        // Show in modal
        const modal = document.getElementById('previewModal');
        const iframe = document.getElementById('previewFrame');
        
        iframe.srcdoc = previewHTML;
        modal.style.display = 'flex';
    },

    /**
     * Close preview modal
     */
    closePreview() {
        document.getElementById('previewModal').style.display = 'none';
    },

    /**
     * Generate preview HTML
     */
    generatePreviewHTML(data) {
        const categoryNames = {
            health: 'Health',
            wellness: 'Wellness',
            medicine: 'Personalized Medicine',
            research: 'Research'
        };

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${AdminUtils.escapeHtml(data.title)} - Preview</title>
                <link rel="stylesheet" href="../core.css">
                <link rel="stylesheet" href="../components.css">
                <link rel="stylesheet" href="../css/blog.css">
                <style>
                    body { padding: 2rem; }
                    .preview-header { 
                        background: #f8f9fa; 
                        padding: 1rem; 
                        border-radius: 8px; 
                        margin-bottom: 2rem;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h4>Post Preview</h4>
                    <p>This is how your post will appear on the blog</p>
                </div>
                
                <article class="blog-post-content">
                    <div class="blog-post-meta">
                        <span class="post-category">${categoryNames[data.category] || data.category}</span>
                        <span class="post-date">${AdminUtils.formatDate(new Date())}</span>
                    </div>
                    
                    <h1>${AdminUtils.escapeHtml(data.title)}</h1>
                    
                    ${data.content}
                    
                    ${data.tags.length > 0 ? `
                        <div class="blog-post-tags">
                            <span class="tag-label">Tags:</span>
                            ${data.tags.map(tag => `<span class="tag">${AdminUtils.escapeHtml(tag)}</span>`).join('')}
                        </div>
                    ` : ''}
                </article>
            </body>
            </html>
        `;
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PostEditor;
}

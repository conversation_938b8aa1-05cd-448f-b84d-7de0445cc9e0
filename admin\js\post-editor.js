/**
 * NP Labs Blog Post Editor
 * 
 * Handles post creation and editing functionality including rich text editing,
 * image uploads, auto-save, and preview generation.
 */

window.PostEditor = {
    quill: null,
    currentPost: null,
    isDraft: false,
    tags: [],
    
    /**
     * Initialize the post editor
     */
    init() {
        this.setupQuillEditor();
        this.setupEventListeners();
        this.setupImageUpload();
        this.setupTagsInput();
        this.setupAutoSave();
        this.setupCollapsibleSections();
        
        // Load draft if editing
        const urlParams = new URLSearchParams(window.location.search);
        const slug = urlParams.get('slug');
        if (slug) {
            this.loadPost(slug);
        }
    },

    /**
     * Set up Quill rich text editor
     */
    setupQuillEditor() {
        const toolbarOptions = [
            [{ 'header': [2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            ['blockquote', 'code-block'],
            ['link', 'image'],
            ['clean']
        ];

        this.quill = new Quill('#contentEditor', {
            theme: 'snow',
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'Start writing your blog post content here...'
        });

        // Update hidden textarea and stats on content change
        this.quill.on('text-change', () => {
            this.updateContentStats();
            this.updateHiddenTextarea();
        });
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Title input
        const titleInput = document.getElementById('postTitle');
        titleInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 100);
            this.generateSlug();
        });

        // Alt text input
        const altInput = document.getElementById('imageAlt');
        altInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 125);
        });

        // Meta description
        const metaInput = document.getElementById('metaDescription');
        metaInput.addEventListener('input', (e) => {
            this.updateCharCount(e.target, 160);
        });

        // Action buttons
        document.getElementById('saveDraftBtn').addEventListener('click', () => {
            this.saveDraft();
        });

        document.getElementById('publishBtn').addEventListener('click', () => {
            this.publishPost();
        });

        document.getElementById('previewBtn').addEventListener('click', () => {
            this.showPreview();
        });

        // Form submission
        document.getElementById('postForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.publishPost();
        });
    },

    /**
     * Set up image upload functionality
     */
    setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('featuredImage');
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImageUpload(e.target.files[0]);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            if (e.dataTransfer.files.length > 0) {
                this.handleImageUpload(e.dataTransfer.files[0]);
            }
        });

        // Image actions
        document.getElementById('changeImageBtn').addEventListener('click', () => {
            fileInput.click();
        });

        document.getElementById('removeImageBtn').addEventListener('click', () => {
            this.removeImage();
        });
    },

    /**
     * Handle image upload
     */
    async handleImageUpload(file) {
        // Validate file
        const errors = AdminUtils.validateImageFile(file);
        if (errors.length > 0) {
            AdminUtils.showNotification(errors.join(' '), 'error');
            return;
        }

        try {
            // Show loading state
            const uploadArea = document.getElementById('imageUploadArea');
            const originalContent = uploadArea.innerHTML;
            uploadArea.innerHTML = `
                <div class="upload-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h4>Uploading Image...</h4>
                    <p>Please wait while we process your image</p>
                </div>
            `;

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImg = document.getElementById('previewImg');
                previewImg.src = e.target.result;
                
                document.querySelector('.upload-placeholder').style.display = 'none';
                document.getElementById('imagePreview').style.display = 'block';
            };
            reader.readAsDataURL(file);

            // In a real implementation, this would upload to the server
            // For now, we'll simulate the upload
            await new Promise(resolve => setTimeout(resolve, 1000));

            AdminUtils.showNotification('Image uploaded successfully', 'success');

        } catch (error) {
            console.error('Image upload failed:', error);
            AdminUtils.showNotification('Image upload failed. Please try again.', 'error');
            
            // Restore original content
            document.getElementById('imageUploadArea').innerHTML = originalContent;
        }
    },

    /**
     * Remove uploaded image
     */
    removeImage() {
        document.querySelector('.upload-placeholder').style.display = 'block';
        document.getElementById('imagePreview').style.display = 'none';
        document.getElementById('featuredImage').value = '';
        document.getElementById('imageAlt').value = '';
    },

    /**
     * Set up tags input
     */
    setupTagsInput() {
        const tagsInput = document.getElementById('postTags');
        const tagsDisplay = document.getElementById('tagsDisplay');

        tagsInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                this.addTag(tagsInput.value.trim());
                tagsInput.value = '';
            }
        });

        tagsInput.addEventListener('blur', () => {
            if (tagsInput.value.trim()) {
                this.addTag(tagsInput.value.trim());
                tagsInput.value = '';
            }
        });
    },

    /**
     * Add a tag
     */
    addTag(tagText) {
        if (!tagText || this.tags.includes(tagText)) {
            return;
        }

        this.tags.push(tagText);
        this.renderTags();
    },

    /**
     * Remove a tag
     */
    removeTag(tagText) {
        this.tags = this.tags.filter(tag => tag !== tagText);
        this.renderTags();
    },

    /**
     * Render tags display
     */
    renderTags() {
        const tagsDisplay = document.getElementById('tagsDisplay');
        tagsDisplay.innerHTML = this.tags.map(tag => `
            <div class="tag-item">
                ${AdminUtils.escapeHtml(tag)}
                <button type="button" class="tag-remove" onclick="PostEditor.removeTag('${AdminUtils.escapeHtml(tag)}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    },

    /**
     * Set up auto-save functionality
     */
    setupAutoSave() {
        AdminAutoSave.start('postForm', async (form) => {
            await this.saveDraft(true); // Silent save
        });
    },

    /**
     * Set up collapsible sections
     */
    setupCollapsibleSections() {
        document.querySelectorAll('.section-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const target = document.getElementById(toggle.dataset.target);
                const isCollapsed = toggle.classList.contains('collapsed');
                
                if (isCollapsed) {
                    toggle.classList.remove('collapsed');
                    target.classList.remove('collapsed');
                    target.style.maxHeight = target.scrollHeight + 'px';
                } else {
                    toggle.classList.add('collapsed');
                    target.classList.add('collapsed');
                    target.style.maxHeight = '0';
                }
            });
        });
    },

    /**
     * Update character count for input fields
     */
    updateCharCount(input, maxLength) {
        const charCount = input.parentElement.querySelector('.char-count');
        if (charCount) {
            const current = input.value.length;
            charCount.textContent = `${current}/${maxLength}`;
            
            if (current > maxLength * 0.9) {
                charCount.style.color = '#dc3545';
            } else {
                charCount.style.color = 'var(--primary-blue)';
            }
        }
    },

    /**
     * Generate URL slug from title
     */
    generateSlug() {
        const title = document.getElementById('postTitle').value;
        const slugInput = document.getElementById('postSlug');
        
        if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') {
            const slug = AdminUtils.generateSlug(title);
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    },

    /**
     * Update content statistics
     */
    updateContentStats() {
        const text = this.quill.getText();
        const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        const chars = text.length;
        const readingTime = Math.ceil(words / 200); // Average reading speed

        document.getElementById('wordCount').textContent = `${words} words`;
        document.getElementById('charCount').textContent = `${chars} characters`;
        document.getElementById('readingTime').textContent = `${readingTime} min read`;
    },

    /**
     * Update hidden textarea with Quill content
     */
    updateHiddenTextarea() {
        const content = this.quill.root.innerHTML;
        document.getElementById('contentHidden').value = content;
    },

    /**
     * Save post as draft
     */
    async saveDraft(silent = false) {
        try {
            const formData = this.getFormData();
            formData.status = 'draft';

            if (!silent) {
                const saveBtn = document.getElementById('saveDraftBtn');
                AdminUtils.showLoading(saveBtn, 'Saving...');
            }

            // In a real implementation, this would save to the server
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (!silent) {
                AdminUtils.hideLoading(document.getElementById('saveDraftBtn'));
                AdminUtils.showNotification('Draft saved successfully', 'success');
            }

            this.isDraft = true;

        } catch (error) {
            console.error('Save draft failed:', error);
            if (!silent) {
                AdminUtils.hideLoading(document.getElementById('saveDraftBtn'));
                AdminUtils.showNotification('Failed to save draft', 'error');
            }
        }
    },

    /**
     * Publish post
     */
    async publishPost() {
        try {
            const formData = this.getFormData();
            
            // Validate required fields
            const errors = this.validateForm(formData);
            if (errors.length > 0) {
                AdminUtils.showNotification(errors.join(' '), 'error');
                return;
            }

            const publishBtn = document.getElementById('publishBtn');
            AdminUtils.showLoading(publishBtn, 'Publishing...');

            formData.status = 'published';
            formData.publishedAt = new Date().toISOString();

            // In a real implementation, this would publish to the server
            await new Promise(resolve => setTimeout(resolve, 2000));

            AdminUtils.hideLoading(publishBtn);
            AdminUtils.showNotification('Post published successfully!', 'success');

            // Redirect to posts list after a delay
            setTimeout(() => {
                window.location.href = 'posts.html';
            }, 2000);

        } catch (error) {
            console.error('Publish failed:', error);
            AdminUtils.hideLoading(document.getElementById('publishBtn'));
            AdminUtils.showNotification('Failed to publish post', 'error');
        }
    },

    /**
     * Get form data
     */
    getFormData() {
        return {
            title: document.getElementById('postTitle').value,
            slug: document.getElementById('postSlug').value,
            category: document.getElementById('postCategory').value,
            featured: document.getElementById('featuredToggle').checked,
            content: this.quill.root.innerHTML,
            imageAlt: document.getElementById('imageAlt').value,
            metaDescription: document.getElementById('metaDescription').value,
            metaKeywords: document.getElementById('metaKeywords').value,
            tags: this.tags
        };
    },

    /**
     * Validate form data
     */
    validateForm(data) {
        const errors = [];

        if (!data.title || data.title.length < 5) {
            errors.push('Title must be at least 5 characters long.');
        }

        if (!data.category) {
            errors.push('Please select a category.');
        }

        if (!data.content || this.quill.getText().trim().length < 100) {
            errors.push('Content must be at least 100 characters long.');
        }

        const hasImage = document.getElementById('imagePreview').style.display !== 'none';
        if (hasImage && !data.imageAlt) {
            errors.push('Alt text is required for the featured image.');
        }

        return errors;
    },

    /**
     * Show post preview
     */
    showPreview() {
        const formData = this.getFormData();
        
        // Validate basic requirements
        if (!formData.title || !formData.content) {
            AdminUtils.showNotification('Please add a title and content to preview', 'warning');
            return;
        }

        // Generate preview HTML
        const previewHTML = this.generatePreviewHTML(formData);
        
        // Show in modal
        const modal = document.getElementById('previewModal');
        const iframe = document.getElementById('previewFrame');
        
        iframe.srcdoc = previewHTML;
        modal.style.display = 'flex';
    },

    /**
     * Close preview modal
     */
    closePreview() {
        document.getElementById('previewModal').style.display = 'none';
    },

    /**
     * Generate preview HTML
     */
    generatePreviewHTML(data) {
        const categoryNames = {
            health: 'Health',
            wellness: 'Wellness',
            medicine: 'Personalized Medicine',
            research: 'Research'
        };

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${AdminUtils.escapeHtml(data.title)} - Preview</title>
                <link rel="stylesheet" href="../core.css">
                <link rel="stylesheet" href="../components.css">
                <link rel="stylesheet" href="../css/blog.css">
                <style>
                    body { padding: 2rem; }
                    .preview-header { 
                        background: #f8f9fa; 
                        padding: 1rem; 
                        border-radius: 8px; 
                        margin-bottom: 2rem;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h4>Post Preview</h4>
                    <p>This is how your post will appear on the blog</p>
                </div>
                
                <article class="blog-post-content">
                    <div class="blog-post-meta">
                        <span class="post-category">${categoryNames[data.category] || data.category}</span>
                        <span class="post-date">${AdminUtils.formatDate(new Date())}</span>
                    </div>
                    
                    <h1>${AdminUtils.escapeHtml(data.title)}</h1>
                    
                    ${data.content}
                    
                    ${data.tags.length > 0 ? `
                        <div class="blog-post-tags">
                            <span class="tag-label">Tags:</span>
                            ${data.tags.map(tag => `<span class="tag">${AdminUtils.escapeHtml(tag)}</span>`).join('')}
                        </div>
                    ` : ''}
                </article>
            </body>
            </html>
        `;
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PostEditor;
}

/**
 * NP Labs Blog Posts Manager
 * 
 * Handles the posts listing, filtering, and management functionality.
 */

window.PostsManager = {
    posts: [],
    filteredPosts: [],
    currentPage: 1,
    postsPerPage: 10,
    deletePostSlug: null,
    
    /**
     * Initialize the posts manager
     */
    async init() {
        try {
            await this.loadPosts();
            this.setupEventListeners();
            this.renderPosts();
        } catch (error) {
            console.error('Posts manager initialization failed:', error);
            AdminUtils.showNotification('Failed to load posts', 'error');
        }
    },

    /**
     * Load posts from API
     */
    async loadPosts() {
        try {
            // Try to get posts from API
            if (!AdminAuth.isDevelopmentMode()) {
                this.posts = await AdminAPI.get('/posts');
            } else {
                // Fallback: simulate posts data for development
                this.posts = await this.getSimulatedPosts();
            }
            
            this.filteredPosts = [...this.posts];
            
        } catch (error) {
            console.error('Failed to load posts:', error);
            // Use simulated data as fallback
            this.posts = await this.getSimulatedPosts();
            this.filteredPosts = [...this.posts];
        }
    },

    /**
     * Get simulated posts data for development
     */
    async getSimulatedPosts() {
        return [
            {
                slug: 'pediatric-compounding',
                title: 'Pediatric Compounding: Tailored Medications for Children',
                category: 'medicine',
                status: 'published',
                featured: true,
                lastModified: new Date('2024-01-15'),
                wordCount: 850,
                author: 'NP Labs Team'
            },
            {
                slug: 'hormone-replacement-therapy',
                title: 'Bioidentical Hormone Replacement Therapy: A Personalized Approach',
                category: 'medicine',
                status: 'published',
                featured: false,
                lastModified: new Date('2024-01-10'),
                wordCount: 1200,
                author: 'NP Labs Team'
            },
            {
                slug: 'wellness-nutrition-guide',
                title: 'Comprehensive Guide to Wellness and Nutrition',
                category: 'wellness',
                status: 'draft',
                featured: false,
                lastModified: new Date('2024-01-08'),
                wordCount: 650,
                author: 'NP Labs Team'
            }
        ];
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchPosts');
        searchInput.addEventListener('input', AdminUtils.debounce(() => {
            this.applyFilters();
        }, 300));

        // Filter dropdowns
        document.getElementById('filterCategory').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.applyFilters();
        });

        // Clear filters
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Pagination
        document.getElementById('prevPage').addEventListener('click', () => {
            this.goToPage(this.currentPage - 1);
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            this.goToPage(this.currentPage + 1);
        });

        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.confirmDelete();
        });
    },

    /**
     * Apply filters to posts list
     */
    applyFilters() {
        const searchTerm = document.getElementById('searchPosts').value.toLowerCase();
        const categoryFilter = document.getElementById('filterCategory').value;
        const statusFilter = document.getElementById('filterStatus').value;

        this.filteredPosts = this.posts.filter(post => {
            // Search filter
            const matchesSearch = !searchTerm || 
                post.title.toLowerCase().includes(searchTerm) ||
                post.category.toLowerCase().includes(searchTerm);

            // Category filter
            const matchesCategory = !categoryFilter || post.category === categoryFilter;

            // Status filter
            let matchesStatus = true;
            if (statusFilter === 'featured') {
                matchesStatus = post.featured;
            } else if (statusFilter) {
                matchesStatus = post.status === statusFilter;
            }

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.currentPage = 1;
        this.renderPosts();
    },

    /**
     * Clear all filters
     */
    clearFilters() {
        document.getElementById('searchPosts').value = '';
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterStatus').value = '';
        
        this.filteredPosts = [...this.posts];
        this.currentPage = 1;
        this.renderPosts();
    },

    /**
     * Render posts list
     */
    renderPosts() {
        const container = document.getElementById('postsList');
        
        if (this.filteredPosts.length === 0) {
            container.innerHTML = this.renderEmptyState();
            document.getElementById('postsPagination').style.display = 'none';
            return;
        }

        // Calculate pagination
        const totalPages = Math.ceil(this.filteredPosts.length / this.postsPerPage);
        const startIndex = (this.currentPage - 1) * this.postsPerPage;
        const endIndex = startIndex + this.postsPerPage;
        const currentPosts = this.filteredPosts.slice(startIndex, endIndex);

        // Render posts
        container.innerHTML = currentPosts.map(post => this.renderPostItem(post)).join('');

        // Update pagination
        this.updatePagination(totalPages);
    },

    /**
     * Render individual post item
     */
    renderPostItem(post) {
        const categoryNames = {
            health: 'Health',
            wellness: 'Wellness',
            medicine: 'Personalized Medicine',
            research: 'Research'
        };

        const statusClass = post.featured ? 'featured' : post.status;
        const statusLabel = post.featured ? 'Featured' : post.status;

        return `
            <div class="post-item" data-slug="${post.slug}">
                <div class="post-status ${statusClass}"></div>
                
                <div class="post-content">
                    <div class="post-title">${AdminUtils.escapeHtml(post.title)}</div>
                    <div class="post-meta">
                        <span><i class="fas fa-folder"></i> ${categoryNames[post.category] || post.category}</span>
                        <span><i class="fas fa-calendar"></i> ${AdminUtils.formatDate(post.lastModified)}</span>
                        <span><i class="fas fa-user"></i> ${post.author || 'NP Labs Team'}</span>
                        <span class="status-badge ${statusClass}">
                            <i class="fas fa-${this.getStatusIcon(statusClass)}"></i>
                            ${statusLabel}
                        </span>
                    </div>
                </div>
                
                <div class="post-stats">
                    <span class="stat-number">${post.wordCount || 0}</span>
                    <span>words</span>
                </div>
                
                <div class="post-actions">
                    <a href="create-post.html?slug=${post.slug}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="../blog/posts/${post.slug}.html" target="_blank" class="btn btn-secondary btn-sm">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <button type="button" class="btn btn-danger btn-sm" onclick="PostsManager.showDeleteModal('${post.slug}', '${AdminUtils.escapeHtml(post.title)}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * Get status icon
     */
    getStatusIcon(status) {
        const icons = {
            published: 'check-circle',
            draft: 'clock',
            featured: 'star'
        };
        return icons[status] || 'circle';
    },

    /**
     * Render empty state
     */
    renderEmptyState() {
        const hasFilters = document.getElementById('searchPosts').value ||
                          document.getElementById('filterCategory').value ||
                          document.getElementById('filterStatus').value;

        if (hasFilters) {
            return `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>No posts found</h3>
                    <p>No posts match your current filters. Try adjusting your search criteria.</p>
                    <button type="button" class="btn btn-secondary" onclick="PostsManager.clearFilters()">
                        <i class="fas fa-times"></i> Clear Filters
                    </button>
                </div>
            `;
        }

        return `
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>No posts yet</h3>
                <p>You haven't created any blog posts yet. Start by creating your first post!</p>
                <a href="create-post.html" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Post
                </a>
            </div>
        `;
    },

    /**
     * Update pagination controls
     */
    updatePagination(totalPages) {
        const paginationContainer = document.getElementById('postsPagination');
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Update page info
        document.getElementById('currentPage').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;

        // Update button states
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        prevBtn.disabled = this.currentPage <= 1;
        nextBtn.disabled = this.currentPage >= totalPages;
    },

    /**
     * Go to specific page
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredPosts.length / this.postsPerPage);
        
        if (page < 1 || page > totalPages) {
            return;
        }

        this.currentPage = page;
        this.renderPosts();
    },

    /**
     * Show delete confirmation modal
     */
    showDeleteModal(slug, title) {
        this.deletePostSlug = slug;
        document.getElementById('deletePostTitle').textContent = title;
        document.getElementById('deleteModal').style.display = 'flex';
    },

    /**
     * Close delete confirmation modal
     */
    closeDeleteModal() {
        this.deletePostSlug = null;
        document.getElementById('deleteModal').style.display = 'none';
    },

    /**
     * Confirm and execute post deletion
     */
    async confirmDelete() {
        if (!this.deletePostSlug) {
            return;
        }

        try {
            const deleteBtn = document.getElementById('confirmDeleteBtn');
            AdminUtils.showLoading(deleteBtn, 'Deleting...');

            // In a real implementation, this would call the API
            if (!AdminAuth.isDevelopmentMode()) {
                await AdminAPI.delete(`/posts/${this.deletePostSlug}`);
            } else {
                // Simulate deletion for development
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Remove from local data
            this.posts = this.posts.filter(post => post.slug !== this.deletePostSlug);
            this.applyFilters();

            AdminUtils.hideLoading(deleteBtn);
            this.closeDeleteModal();
            AdminUtils.showNotification('Post deleted successfully', 'success');

        } catch (error) {
            console.error('Delete failed:', error);
            AdminUtils.hideLoading(document.getElementById('confirmDeleteBtn'));
            AdminUtils.showNotification('Failed to delete post', 'error');
        }
    },

    /**
     * Refresh posts list
     */
    async refresh() {
        try {
            await this.loadPosts();
            this.applyFilters();
            AdminUtils.showNotification('Posts refreshed', 'success', 2000);
        } catch (error) {
            console.error('Refresh failed:', error);
            AdminUtils.showNotification('Failed to refresh posts', 'error');
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PostsManager;
}

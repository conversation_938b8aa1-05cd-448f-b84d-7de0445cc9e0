/**
 * NP Labs Blog Posts Manager
 * 
 * Handles the posts listing, filtering, and management functionality.
 */

window.PostsManager = {
    posts: [],
    filteredPosts: [],
    currentPage: 1,
    postsPerPage: 10,
    deletePostSlug: null,
    
    /**
     * Initialize the posts manager
     */
    async init() {
        try {
            await this.loadPosts();
            this.setupEventListeners();
            this.renderPosts();
        } catch (error) {
            console.error('Posts manager initialization failed:', error);
            AdminUtils.showNotification('Failed to load posts', 'error');
        }
    },

    /**
     * Load posts from API
     */
    async loadPosts() {
        try {
            // Try to get posts from API
            if (!AdminAuth.isDevelopmentMode()) {
                this.posts = await AdminAPI.get('/posts');
            } else {
                // Fallback: simulate posts data for development
                this.posts = await this.getSimulatedPosts();
            }
            
            this.filteredPosts = [...this.posts];
            
        } catch (error) {
            console.error('Failed to load posts:', error);
            // Use simulated data as fallback
            this.posts = await this.getSimulatedPosts();
            this.filteredPosts = [...this.posts];
        }
    },

    /**
     * Get simulated posts data for development
     */
    async getSimulatedPosts() {
        // Use the same data source as the dashboard
        try {
            const dashboardPosts = await this.fetchExistingBlogPosts();

            // Convert dashboard format to posts manager format
            return dashboardPosts.map(post => ({
                slug: post.slug,
                title: post.title,
                category: this.mapCategoryToKey(post.category),
                status: 'published',
                featured: post.featured || false,
                lastModified: new Date(post.date),
                wordCount: this.calculateWordCount(post),
                author: post.author || 'NP Labs Team'
            }));
        } catch (error) {
            console.error('Failed to load existing blog posts:', error);
            // Fallback to basic data
            return [
                {
                    slug: 'pediatric-compounding',
                    title: 'Pediatric Compounding: Making Medicine Child-Friendly',
                    category: 'medicine',
                    status: 'published',
                    featured: true,
                    lastModified: new Date('2024-01-15'),
                    wordCount: 850,
                    author: 'Dr. Maria Rodriguez'
                }
            ];
        }
    },

    /**
     * Map category names to keys for filtering
     */
    mapCategoryToKey(categoryName) {
        const categoryMap = {
            'Health': 'health',
            'Wellness': 'wellness',
            'Personalized Medicine': 'medicine',
            'Research': 'research',
            'Women\'s Health': 'health'
        };
        return categoryMap[categoryName] || 'health';
    },

    /**
     * Calculate word count from post content
     */
    calculateWordCount(post) {
        if (!post.content) return 0;

        // Remove HTML tags and calculate word count
        const textContent = post.content.replace(/<[^>]*>/g, '');
        const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
        return words.length;
    },

    /**
     * Fetch existing blog posts from the blog/posts directory (same as dashboard)
     */
    async fetchExistingBlogPosts() {
        const blogPosts = [
            {
                slug: 'bioidentical-hormones',
                title: 'Bioidentical Hormones: Separating Myths from Facts',
                category: 'Research',
                date: 'May 1, 2023',
                author: 'Dr. Elena Papadakis',
                featured: false
            },
            {
                slug: 'future-of-personalized-medicine',
                title: 'The Future of Personalized Medicine: How Compounding is Changing Healthcare',
                category: 'Personalized Medicine',
                date: 'July 2, 2024',
                author: 'Dr. Sarah Johnson',
                featured: false
            },
            {
                slug: 'pediatric-compounding',
                title: 'Pediatric Compounding: Making Medicine Child-Friendly',
                category: 'Personalized Medicine',
                date: 'July 2, 2024',
                author: 'Dr. Maria Rodriguez',
                featured: true
            },
            {
                slug: 'understanding-ldn-therapy',
                title: 'Understanding Low Dose Naltrexone (LDN) Therapy',
                category: 'Health',
                date: 'June 10, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'peptide-therapies',
                title: 'Peptide Therapies: The Building Blocks of Wellness',
                category: 'Wellness',
                date: 'June 5, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'hormone-balance',
                title: 'Hormone Optimization: Finding Your Balance',
                category: 'Health',
                date: 'May 28, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'hormone-optimization',
                title: 'Advanced Hormone Optimization Strategies',
                category: 'Health',
                date: 'May 20, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'perimenopause-transition',
                title: 'Navigating the Hormonal Rollercoaster: Understanding the Perimenopause Transition',
                category: 'Women\'s Health',
                date: 'June 15, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'gut-brain-connection',
                title: 'The Gut-Brain Connection: How Your Microbiome Affects Mental Health',
                category: 'Research',
                date: 'April 15, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'supplement-quality',
                title: 'The Importance of Pharmaceutical-Grade Supplements',
                category: 'Health',
                date: 'March 20, 2023',
                author: 'NP Labs Team',
                featured: false
            },
            {
                slug: 'taurine-glycine-l-citrulline-aging-explorer',
                title: 'Taurine, Glycine & L-Citrulline: The Anti-Aging Explorer\'s Guide',
                category: 'Wellness',
                date: 'February 10, 2023',
                author: 'NP Labs Team',
                featured: false
            }
        ];

        // Add content to each post by fetching from the actual HTML files
        for (let post of blogPosts) {
            try {
                post.content = await this.extractContentFromPost(post.slug);
            } catch (error) {
                console.warn(`Could not extract content for ${post.slug}:`, error);
                post.content = this.getDefaultContent(post.title);
            }
        }

        return blogPosts;
    },

    /**
     * Extract content from a blog post HTML file
     */
    async extractContentFromPost(slug) {
        try {
            const response = await fetch(`../blog/posts/${slug}.html`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Extract content from the blog post content section
            const contentSection = doc.querySelector('article.blog-post-content') ||
                                 doc.querySelector('.blog-post-content') ||
                                 doc.querySelector('article');

            if (contentSection) {
                // Clone the content section to avoid modifying the original
                const contentClone = contentSection.cloneNode(true);

                // Remove unwanted elements
                const elementsToRemove = contentClone.querySelectorAll('.blog-post-tags, .blog-post-share, .author-section, .related-posts, .cta-section');
                elementsToRemove.forEach(el => el.remove());

                // Remove references section
                const referencesHeading = Array.from(contentClone.querySelectorAll('h3')).find(h3 =>
                    h3.textContent.toLowerCase().includes('references'));
                if (referencesHeading) {
                    let nextElement = referencesHeading.nextElementSibling;
                    referencesHeading.remove();
                    if (nextElement && nextElement.tagName === 'OL') {
                        nextElement.remove();
                    }
                }

                // Get the cleaned HTML content
                let content = contentClone.innerHTML;

                // Clean up the content
                content = content
                    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
                    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/\s+/g, ' ')
                    .trim();

                return content;
            } else {
                throw new Error('Could not find content section');
            }
        } catch (error) {
            console.warn(`Failed to extract content for ${slug}:`, error);
            return this.getDefaultContent(slug);
        }
    },

    /**
     * Get default content for a post when extraction fails
     */
    getDefaultContent(title) {
        return `<h2>Introduction</h2><p>This is a comprehensive article about ${title.toLowerCase()}. The content covers important aspects of this topic and provides valuable insights for readers interested in healthcare and wellness.</p><h3>Key Points</h3><p>This article explores various aspects of the topic, including:</p><ul><li>Background information and context</li><li>Current research and developments</li><li>Practical applications and benefits</li><li>Future considerations and trends</li></ul><h3>Conclusion</h3><p>Understanding ${title.toLowerCase()} is important for making informed healthcare decisions. This article provides a foundation for further exploration of this important topic.</p>`;
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchPosts');
        searchInput.addEventListener('input', AdminUtils.debounce(() => {
            this.applyFilters();
        }, 300));

        // Filter dropdowns
        document.getElementById('filterCategory').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.applyFilters();
        });

        // Clear filters
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Pagination
        document.getElementById('prevPage').addEventListener('click', () => {
            this.goToPage(this.currentPage - 1);
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            this.goToPage(this.currentPage + 1);
        });

        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.confirmDelete();
        });
    },

    /**
     * Apply filters to posts list
     */
    applyFilters() {
        const searchTerm = document.getElementById('searchPosts').value.toLowerCase();
        const categoryFilter = document.getElementById('filterCategory').value;
        const statusFilter = document.getElementById('filterStatus').value;

        this.filteredPosts = this.posts.filter(post => {
            // Search filter
            const matchesSearch = !searchTerm || 
                post.title.toLowerCase().includes(searchTerm) ||
                post.category.toLowerCase().includes(searchTerm);

            // Category filter
            const matchesCategory = !categoryFilter || post.category === categoryFilter;

            // Status filter
            let matchesStatus = true;
            if (statusFilter === 'featured') {
                matchesStatus = post.featured;
            } else if (statusFilter) {
                matchesStatus = post.status === statusFilter;
            }

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.currentPage = 1;
        this.renderPosts();
    },

    /**
     * Clear all filters
     */
    clearFilters() {
        document.getElementById('searchPosts').value = '';
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterStatus').value = '';
        
        this.filteredPosts = [...this.posts];
        this.currentPage = 1;
        this.renderPosts();
    },

    /**
     * Render posts list
     */
    renderPosts() {
        const container = document.getElementById('postsList');
        
        if (this.filteredPosts.length === 0) {
            container.innerHTML = this.renderEmptyState();
            document.getElementById('postsPagination').style.display = 'none';
            return;
        }

        // Calculate pagination
        const totalPages = Math.ceil(this.filteredPosts.length / this.postsPerPage);
        const startIndex = (this.currentPage - 1) * this.postsPerPage;
        const endIndex = startIndex + this.postsPerPage;
        const currentPosts = this.filteredPosts.slice(startIndex, endIndex);

        // Render posts
        container.innerHTML = currentPosts.map(post => this.renderPostItem(post)).join('');

        // Update pagination
        this.updatePagination(totalPages);
    },

    /**
     * Render individual post item
     */
    renderPostItem(post) {
        const categoryNames = {
            health: 'Health',
            wellness: 'Wellness',
            medicine: 'Personalized Medicine',
            research: 'Research'
        };

        const statusClass = post.featured ? 'featured' : post.status;
        const statusLabel = post.featured ? 'Featured' : post.status;

        return `
            <div class="post-item" data-slug="${post.slug}">
                <div class="post-status ${statusClass}"></div>
                
                <div class="post-content">
                    <div class="post-title">${AdminUtils.escapeHtml(post.title)}</div>
                    <div class="post-meta">
                        <span><i class="fas fa-folder"></i> ${categoryNames[post.category] || post.category}</span>
                        <span><i class="fas fa-calendar"></i> ${AdminUtils.formatDate(post.lastModified)}</span>
                        <span><i class="fas fa-user"></i> ${post.author || 'NP Labs Team'}</span>
                        <span class="status-badge ${statusClass}">
                            <i class="fas fa-${this.getStatusIcon(statusClass)}"></i>
                            ${statusLabel}
                        </span>
                    </div>
                </div>
                
                <div class="post-stats">
                    <span class="stat-number">${post.wordCount || 0}</span>
                    <span>words</span>
                </div>
                
                <div class="post-actions">
                    <a href="create-post.html?slug=${post.slug}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="../blog/posts/${post.slug}.html" target="_blank" class="btn btn-secondary btn-sm">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <button type="button" class="btn btn-danger btn-sm" onclick="PostsManager.showDeleteModal('${post.slug}', '${AdminUtils.escapeHtml(post.title)}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * Get status icon
     */
    getStatusIcon(status) {
        const icons = {
            published: 'check-circle',
            draft: 'clock',
            featured: 'star'
        };
        return icons[status] || 'circle';
    },

    /**
     * Render empty state
     */
    renderEmptyState() {
        const hasFilters = document.getElementById('searchPosts').value ||
                          document.getElementById('filterCategory').value ||
                          document.getElementById('filterStatus').value;

        if (hasFilters) {
            return `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>No posts found</h3>
                    <p>No posts match your current filters. Try adjusting your search criteria.</p>
                    <button type="button" class="btn btn-secondary" onclick="PostsManager.clearFilters()">
                        <i class="fas fa-times"></i> Clear Filters
                    </button>
                </div>
            `;
        }

        return `
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>No posts yet</h3>
                <p>You haven't created any blog posts yet. Start by creating your first post!</p>
                <a href="create-post.html" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Post
                </a>
            </div>
        `;
    },

    /**
     * Update pagination controls
     */
    updatePagination(totalPages) {
        const paginationContainer = document.getElementById('postsPagination');
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Update page info
        document.getElementById('currentPage').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;

        // Update button states
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        prevBtn.disabled = this.currentPage <= 1;
        nextBtn.disabled = this.currentPage >= totalPages;
    },

    /**
     * Go to specific page
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredPosts.length / this.postsPerPage);
        
        if (page < 1 || page > totalPages) {
            return;
        }

        this.currentPage = page;
        this.renderPosts();
    },

    /**
     * Show delete confirmation modal
     */
    showDeleteModal(slug, title) {
        this.deletePostSlug = slug;
        document.getElementById('deletePostTitle').textContent = title;
        document.getElementById('deleteModal').style.display = 'flex';
    },

    /**
     * Close delete confirmation modal
     */
    closeDeleteModal() {
        this.deletePostSlug = null;
        document.getElementById('deleteModal').style.display = 'none';
    },

    /**
     * Confirm and execute post deletion
     */
    async confirmDelete() {
        if (!this.deletePostSlug) {
            return;
        }

        try {
            const deleteBtn = document.getElementById('confirmDeleteBtn');
            AdminUtils.showLoading(deleteBtn, 'Deleting...');

            // In a real implementation, this would call the API
            if (!AdminAuth.isDevelopmentMode()) {
                await AdminAPI.delete(`/posts/${this.deletePostSlug}`);
            } else {
                // Simulate deletion for development
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Remove from local data
            this.posts = this.posts.filter(post => post.slug !== this.deletePostSlug);
            this.applyFilters();

            AdminUtils.hideLoading(deleteBtn);
            this.closeDeleteModal();
            AdminUtils.showNotification('Post deleted successfully', 'success');

        } catch (error) {
            console.error('Delete failed:', error);
            AdminUtils.hideLoading(document.getElementById('confirmDeleteBtn'));
            AdminUtils.showNotification('Failed to delete post', 'error');
        }
    },

    /**
     * Refresh posts list
     */
    async refresh() {
        try {
            await this.loadPosts();
            this.applyFilters();
            AdminUtils.showNotification('Posts refreshed', 'success', 2000);
        } catch (error) {
            console.error('Refresh failed:', error);
            AdminUtils.showNotification('Failed to refresh posts', 'error');
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PostsManager;
}

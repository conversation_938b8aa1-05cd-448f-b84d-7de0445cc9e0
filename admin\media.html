<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Blog Admin - Media Library</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/media-manager.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-brand">
                <img src="../nplabslogo.svg" alt="NP Labs">
                <h1>Blog Admin</h1>
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="posts.html">
                    <i class="fas fa-edit"></i> Posts
                </a>
                <a href="media.html" class="active">
                    <i class="fas fa-images"></i> Media
                </a>
                <a href="../blog/" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Blog
                </a>
            </nav>
            
            <div class="admin-user-menu">
                <button class="user-menu-toggle" onclick="AdminAuth.logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-content">
            <div class="media-header">
                <div class="media-title">
                    <h2><i class="fas fa-images"></i> Media Library</h2>
                    <p>Manage your blog images and media files</p>
                </div>
                
                <div class="media-actions">
                    <button class="btn btn-primary" onclick="MediaManager.showUploadModal()">
                        <i class="fas fa-upload"></i> Upload Files
                    </button>
                    <button class="btn btn-secondary" onclick="MediaManager.refreshLibrary()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Media Stats -->
            <div class="media-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalFiles">-</h3>
                        <p>Total Files</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSize">-</h3>
                        <p>Total Size</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="recentUploads">-</h3>
                        <p>Recent Uploads</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="imageFiles">-</h3>
                        <p>Image Files</p>
                    </div>
                </div>
            </div>

            <!-- Media Filters -->
            <div class="media-filters">
                <div class="filter-group">
                    <label for="fileTypeFilter">File Type:</label>
                    <select id="fileTypeFilter" onchange="MediaManager.applyFilters()">
                        <option value="all">All Files</option>
                        <option value="image">Images</option>
                        <option value="jpg">JPG</option>
                        <option value="png">PNG</option>
                        <option value="webp">WebP</option>
                        <option value="svg">SVG</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="sortBy">Sort By:</label>
                    <select id="sortBy" onchange="MediaManager.applyFilters()">
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="name-asc">Name A-Z</option>
                        <option value="name-desc">Name Z-A</option>
                        <option value="size-desc">Largest First</option>
                        <option value="size-asc">Smallest First</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="searchFiles">Search:</label>
                    <input type="text" id="searchFiles" placeholder="Search files..." 
                           oninput="MediaManager.applyFilters()">
                </div>
                
                <div class="filter-group">
                    <label for="viewMode">View:</label>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid" onclick="MediaManager.setViewMode('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list" onclick="MediaManager.setViewMode('list')">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Media Grid -->
            <div class="media-container">
                <div id="mediaGrid" class="media-grid">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading media library...</p>
                    </div>
                </div>
                
                <div id="mediaList" class="media-list" style="display: none;">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading media library...</p>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-images"></i>
                </div>
                <h3>No Media Files Found</h3>
                <p>Upload your first image to get started with the media library.</p>
                <button class="btn btn-primary" onclick="MediaManager.showUploadModal()">
                    <i class="fas fa-upload"></i> Upload Files
                </button>
            </div>
        </div>
    </main>

    <!-- Upload Modal -->
    <div id="uploadModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-upload"></i> Upload Media Files</h3>
                <button class="modal-close" onclick="MediaManager.hideUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h4>Drag & Drop Files Here</h4>
                        <p>or click to browse files</p>
                        <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                        <button class="btn btn-secondary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </button>
                    </div>
                    <div class="upload-info">
                        <p><strong>Supported formats:</strong> JPG, PNG, WebP, SVG</p>
                        <p><strong>Maximum file size:</strong> 10MB per file</p>
                    </div>
                </div>
                
                <div id="uploadProgress" class="upload-progress" style="display: none;">
                    <div class="progress-header">
                        <h4>Uploading Files...</h4>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div id="uploadStatus" class="upload-status"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Details Modal -->
    <div id="imageModal" class="modal" style="display: none;">
        <div class="modal-content image-modal">
            <div class="modal-header">
                <h3 id="imageModalTitle">Image Details</h3>
                <button class="modal-close" onclick="MediaManager.hideImageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="image-details-container">
                    <div class="image-preview">
                        <img id="imagePreview" src="" alt="Image preview">
                    </div>
                    <div class="image-info">
                        <div class="info-group">
                            <label>Filename:</label>
                            <span id="imageFilename">-</span>
                        </div>
                        <div class="info-group">
                            <label>File Size:</label>
                            <span id="imageFileSize">-</span>
                        </div>
                        <div class="info-group">
                            <label>Dimensions:</label>
                            <span id="imageDimensions">-</span>
                        </div>
                        <div class="info-group">
                            <label>Upload Date:</label>
                            <span id="imageUploadDate">-</span>
                        </div>
                        <div class="info-group">
                            <label>File URL:</label>
                            <div class="url-copy">
                                <input type="text" id="imageUrl" readonly>
                                <button class="btn btn-sm btn-secondary" onclick="MediaManager.copyToClipboard('imageUrl')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                        </div>
                        <div class="image-actions">
                            <button class="btn btn-primary" onclick="MediaManager.insertIntoPost()">
                                <i class="fas fa-plus"></i> Insert into Post
                            </button>
                            <button class="btn btn-danger" onclick="MediaManager.deleteImage()">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/media-manager.js"></script>
    
    <script>
        // Prevent Chrome extension interference
        (function() {
            'use strict';

            // Initialize media manager
            document.addEventListener('DOMContentLoaded', function() {
                try {
                    // Require authentication
                    if (!AdminAuth.requireAuth()) {
                        return;
                    }

                    // Initialize media manager with error handling
                    MediaManager.init().then(() => {
                        console.log('Media manager initialization completed');

                        // Add validation button for debugging
                        if (window.location.search.includes('debug=true')) {
                            const debugBtn = document.createElement('button');
                            debugBtn.textContent = 'Validate Images';
                            debugBtn.className = 'btn btn-secondary';
                            debugBtn.onclick = () => MediaManager.validateImageLoading();
                            document.querySelector('.media-actions').appendChild(debugBtn);
                        }
                    }).catch(error => {
                        console.error('Media manager initialization failed:', error);
                    });
                } catch (error) {
                    console.error('Error during media manager setup:', error);
                }
            });
        })();
    </script>
</body>
</html>

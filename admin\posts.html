<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Posts - NP Labs Blog Admin</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/posts-manager.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-brand">
                <img src="../nplabslogo.svg" alt="NP Labs">
                <h1>Blog Admin</h1>
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="posts.html" class="active">
                    <i class="fas fa-edit"></i> Posts
                </a>
                <a href="media.html">
                    <i class="fas fa-images"></i> Media
                </a>
                <a href="../blog/" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Blog
                </a>
            </nav>
            
            <div class="admin-user-menu">
                <button class="user-menu-toggle" onclick="AdminAuth.logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-content">
            <div class="posts-header">
                <div class="posts-title">
                    <h2>Manage Posts</h2>
                    <p>Create, edit, and organize your blog content</p>
                </div>
                
                <div class="posts-actions">
                    <a href="create-post.html" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Post
                    </a>
                </div>
            </div>

            <!-- Posts Filters -->
            <div class="posts-filters">
                <div class="filter-group">
                    <label for="searchPosts">Search Posts:</label>
                    <input type="text" id="searchPosts" placeholder="Search by title or content...">
                </div>
                
                <div class="filter-group">
                    <label for="filterCategory">Category:</label>
                    <select id="filterCategory">
                        <option value="">All Categories</option>
                        <option value="health">Health</option>
                        <option value="wellness">Wellness</option>
                        <option value="medicine">Personalized Medicine</option>
                        <option value="research">Research</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filterStatus">Status:</label>
                    <select id="filterStatus">
                        <option value="">All Posts</option>
                        <option value="published">Published</option>
                        <option value="draft">Drafts</option>
                        <option value="featured">Featured</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <button type="button" class="btn btn-secondary" id="clearFilters">
                        <i class="fas fa-times"></i> Clear Filters
                    </button>
                </div>
            </div>

            <!-- Posts List -->
            <div class="posts-container">
                <div class="posts-list" id="postsList">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading posts...</p>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="posts-pagination" id="postsPagination" style="display: none;">
                    <button type="button" class="btn btn-secondary" id="prevPage" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    
                    <span class="pagination-info">
                        Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
                    </span>
                    
                    <button type="button" class="btn btn-secondary" id="nextPage">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Deletion</h3>
                <button type="button" class="modal-close" onclick="PostsManager.closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>Are you sure you want to delete this post?</h4>
                    <p id="deletePostTitle">Post title will appear here</p>
                    <p class="warning-text">This action cannot be undone. The post will be permanently removed from your blog.</p>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="PostsManager.closeDeleteModal()">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash"></i> Delete Post
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/posts-manager.js"></script>
    
    <script>
        // Initialize posts manager
        document.addEventListener('DOMContentLoaded', function() {
            // Require authentication
            if (!AdminAuth.requireAuth()) {
                return;
            }
            
            // Initialize posts manager
            PostsManager.init();
        });
    </script>
</body>
</html>

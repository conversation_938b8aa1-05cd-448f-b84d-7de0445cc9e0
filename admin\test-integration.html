<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Integration Test - NP Labs Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-results {
            margin-top: 20px;
        }
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #ddd;
        }
        .test-item.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .test-item.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .test-item.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .post-preview {
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .post-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .post-meta {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 10px;
        }
        .post-content {
            color: #374151;
            font-size: 0.875rem;
            max-height: 100px;
            overflow: hidden;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn.secondary {
            background: #6b7280;
        }
        .btn.secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-flask"></i> NP Labs Blog Integration Test</h1>
        
        <div class="test-section">
            <h2>Dashboard Integration Test</h2>
            <p>Testing the dashboard's ability to load and display existing blog posts.</p>
            <button class="btn" onclick="testDashboardIntegration()">
                <i class="fas fa-play"></i> Test Dashboard
            </button>
            <button class="btn secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i> Clear Results
            </button>
            <div id="dashboardResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>Post Editor Integration Test</h2>
            <p>Testing the post editor's ability to load existing blog post content.</p>
            <div>
                <label for="testSlug">Post Slug to Test:</label>
                <select id="testSlug">
                    <option value="bioidentical-hormones">Bioidentical Hormones</option>
                    <option value="future-of-personalized-medicine">Future of Personalized Medicine</option>
                    <option value="pediatric-compounding">Pediatric Compounding</option>
                    <option value="understanding-ldn-therapy">Understanding LDN Therapy</option>
                    <option value="peptide-therapies">Peptide Therapies</option>
                    <option value="hormone-balance">Hormone Balance</option>
                    <option value="perimenopause-transition">Perimenopause Transition</option>
                    <option value="gut-brain-connection">Gut-Brain Connection</option>
                    <option value="supplement-quality">Supplement Quality</option>
                    <option value="taurine-glycine-l-citrulline-aging-explorer">Anti-Aging Supplements</option>
                    <option value="hormone-optimization">Hormone Optimization</option>
                </select>
                <button class="btn" onclick="testPostEditor()">
                    <i class="fas fa-edit"></i> Test Post Editor
                </button>
            </div>
            <div id="editorResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>Content Extraction Test</h2>
            <p>Testing content extraction from actual blog post HTML files.</p>
            <button class="btn" onclick="testContentExtraction()">
                <i class="fas fa-file-text"></i> Test Content Extraction
            </button>
            <div id="contentResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>Critical Issues Verification</h2>
            <p>Test all the critical issues that were reported and fixed.</p>
            <button class="btn" onclick="testCriticalIssues()">
                <i class="fas fa-bug"></i> Test Critical Issues
            </button>
            <div id="criticalIssuesResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>Media Management Test</h2>
            <p>Test the media library functionality and file management.</p>
            <button class="btn" onclick="testMediaManager()">
                <i class="fas fa-images"></i> Test Media Manager
            </button>
            <div id="mediaResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>Image Links Verification</h2>
            <p>Verify all media library images load correctly without 404 errors.</p>
            <button class="btn" onclick="testImageLinks()">
                <i class="fas fa-link"></i> Test Image Links
            </button>
            <div id="imageLinksResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>All Posts Overview</h2>
            <p>Display all discovered blog posts with their metadata.</p>
            <button class="btn" onclick="showAllPosts()">
                <i class="fas fa-list"></i> Show All Posts
            </button>
            <div id="allPostsResults" class="test-results"></div>
        </div>
    </div>

    <script src="js/admin-utils.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/post-editor.js"></script>
    <script src="js/posts-manager.js"></script>
    <script src="js/media-manager.js"></script>
    <script>
        function addTestResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const item = document.createElement('div');
            item.className = `test-item ${type}`;
            item.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation-triangle'}"></i> ${message}`;
            container.appendChild(item);
        }

        function clearResults() {
            document.querySelectorAll('.test-results').forEach(container => {
                container.innerHTML = '';
            });
        }

        async function testDashboardIntegration() {
            const container = 'dashboardResults';
            addTestResult(container, 'info', 'Starting dashboard integration test...');

            try {
                const posts = await Dashboard.parseRecentPosts();
                addTestResult(container, 'success', `Successfully loaded ${posts.length} blog posts`);

                posts.forEach((post, index) => {
                    const wordCount = Dashboard.calculateWordCount(post);
                    addTestResult(container, 'success', 
                        `Post ${index + 1}: "${post.title}" - ${wordCount} words - Category: ${post.category}`);
                });

            } catch (error) {
                addTestResult(container, 'error', `Dashboard test failed: ${error.message}`);
            }
        }

        async function testPostEditor() {
            const container = 'editorResults';
            const slug = document.getElementById('testSlug').value;
            
            addTestResult(container, 'info', `Testing post editor with slug: ${slug}`);

            try {
                const postData = await PostEditor.loadExistingBlogPost(slug);
                
                if (postData) {
                    addTestResult(container, 'success', `Successfully loaded post: ${postData.title}`);
                    addTestResult(container, 'success', `Content length: ${postData.content.length} characters`);
                    addTestResult(container, 'success', `Category: ${postData.category}, Author: ${postData.author}`);
                    addTestResult(container, 'success', `Tags: ${postData.tags.join(', ')}`);
                    
                    // Show content preview
                    const preview = document.createElement('div');
                    preview.className = 'post-preview';
                    preview.innerHTML = `
                        <div class="post-title">${postData.title}</div>
                        <div class="post-meta">${postData.category} • ${postData.author} • ${postData.date}</div>
                        <div class="post-content">${postData.content.substring(0, 300)}...</div>
                    `;
                    document.getElementById(container).appendChild(preview);
                } else {
                    addTestResult(container, 'error', `Failed to load post: ${slug}`);
                }

            } catch (error) {
                addTestResult(container, 'error', `Post editor test failed: ${error.message}`);
            }
        }

        async function testContentExtraction() {
            const container = 'contentResults';
            const testSlugs = ['bioidentical-hormones', 'pediatric-compounding', 'future-of-personalized-medicine'];
            
            addTestResult(container, 'info', 'Testing content extraction from HTML files...');

            for (const slug of testSlugs) {
                try {
                    const content = await Dashboard.extractContentFromPost(slug);
                    const wordCount = content.replace(/<[^>]*>/g, '').trim().split(/\s+/).filter(w => w.length > 0).length;
                    addTestResult(container, 'success', 
                        `${slug}: Extracted ${content.length} characters, ~${wordCount} words`);
                } catch (error) {
                    addTestResult(container, 'error', `${slug}: Extraction failed - ${error.message}`);
                }
            }
        }

        async function testCriticalIssues() {
            const container = 'criticalIssuesResults';
            addTestResult(container, 'info', 'Testing critical issues fixes...');

            try {
                // Issue 1: Dashboard Word Count Error
                addTestResult(container, 'info', '1. Testing dashboard word count accuracy...');
                const posts = await Dashboard.parseRecentPosts();

                let wordCountIssues = 0;
                let variableWordCounts = [];

                posts.forEach(post => {
                    const wordCount = Dashboard.calculateWordCount(post);
                    variableWordCounts.push(wordCount);
                    if (wordCount === 67) {
                        wordCountIssues++;
                    }
                });

                if (wordCountIssues === 0) {
                    addTestResult(container, 'success', `✅ Word count fix: All posts have variable word counts (${Math.min(...variableWordCounts)}-${Math.max(...variableWordCounts)} words)`);
                } else {
                    addTestResult(container, 'error', `❌ Word count issue: ${wordCountIssues} posts still showing 67 words`);
                }

                // Issue 2: Featured Article Identification
                addTestResult(container, 'info', '2. Testing featured article designation...');
                const featuredPosts = posts.filter(post => post.featured);
                const pediatricPost = posts.find(post => post.slug === 'pediatric-compounding');

                if (pediatricPost && pediatricPost.featured) {
                    addTestResult(container, 'success', '✅ Featured post fix: "Pediatric Compounding" is correctly marked as featured');
                } else {
                    addTestResult(container, 'error', '❌ Featured post issue: "Pediatric Compounding" is not marked as featured');
                }

                // Issue 3: Manage Posts Page Count
                addTestResult(container, 'info', '3. Testing posts manager data...');
                const managerPosts = await PostsManager.getSimulatedPosts();

                if (managerPosts.length >= 11) {
                    addTestResult(container, 'success', `✅ Posts manager fix: Shows ${managerPosts.length} posts (expected 11+)`);
                } else {
                    addTestResult(container, 'error', `❌ Posts manager issue: Only shows ${managerPosts.length} posts (expected 11)`);
                }

                // Issue 4: Post Editor Content Loading
                addTestResult(container, 'info', '4. Testing post editor content loading...');
                const testSlugs = ['pediatric-compounding', 'bioidentical-hormones', 'future-of-personalized-medicine'];
                let contentLoadingIssues = 0;

                for (const slug of testSlugs) {
                    try {
                        const postData = await PostEditor.loadExistingBlogPost(slug);
                        if (postData && postData.content && postData.content.length > 500) {
                            addTestResult(container, 'success', `✅ Content loading: ${slug} loaded ${postData.content.length} characters`);
                        } else {
                            addTestResult(container, 'error', `❌ Content loading: ${slug} failed to load proper content`);
                            contentLoadingIssues++;
                        }
                    } catch (error) {
                        addTestResult(container, 'error', `❌ Content loading: ${slug} failed with error: ${error.message}`);
                        contentLoadingIssues++;
                    }
                }

                // Summary
                addTestResult(container, 'info', '--- CRITICAL ISSUES TEST SUMMARY ---');
                if (wordCountIssues === 0 && pediatricPost?.featured && managerPosts.length >= 11 && contentLoadingIssues === 0) {
                    addTestResult(container, 'success', '🎉 ALL CRITICAL ISSUES FIXED! The admin system is working correctly.');
                } else {
                    addTestResult(container, 'warning', `⚠️ Some issues remain: Word count (${wordCountIssues}), Featured (${!pediatricPost?.featured}), Posts count (${managerPosts.length < 11}), Content loading (${contentLoadingIssues})`);
                }

            } catch (error) {
                addTestResult(container, 'error', `Critical issues test failed: ${error.message}`);
            }
        }

        async function testMediaManager() {
            const container = 'mediaResults';
            addTestResult(container, 'info', 'Testing media management system...');

            try {
                // Test 1: Media Manager Initialization
                addTestResult(container, 'info', '1. Testing MediaManager initialization...');

                if (typeof MediaManager === 'undefined') {
                    addTestResult(container, 'error', '❌ MediaManager not loaded');
                    return;
                }

                // Initialize MediaManager
                await MediaManager.init();
                addTestResult(container, 'success', '✅ MediaManager initialized successfully');

                // Test 2: Media Files Loading
                addTestResult(container, 'info', '2. Testing media files loading...');
                const mediaFiles = MediaManager.mediaFiles;

                if (mediaFiles && mediaFiles.length > 0) {
                    addTestResult(container, 'success', `✅ Loaded ${mediaFiles.length} media files`);

                    // Show sample files
                    const sampleFiles = mediaFiles.slice(0, 3);
                    sampleFiles.forEach(file => {
                        addTestResult(container, 'info', `📁 ${file.filename} (${MediaManager.formatFileSize(file.size)})`);
                    });
                } else {
                    addTestResult(container, 'warning', '⚠️ No media files found');
                }

                // Test 3: File Filtering
                addTestResult(container, 'info', '3. Testing file filtering...');
                const originalCount = MediaManager.filteredFiles.length;

                // Test image filter
                document.getElementById('fileTypeFilter').value = 'image';
                MediaManager.applyFilters();
                const imageCount = MediaManager.filteredFiles.length;

                addTestResult(container, 'success', `✅ Image filter: ${imageCount} image files found`);

                // Reset filter
                document.getElementById('fileTypeFilter').value = 'all';
                MediaManager.applyFilters();

                if (MediaManager.filteredFiles.length === originalCount) {
                    addTestResult(container, 'success', '✅ Filter reset working correctly');
                } else {
                    addTestResult(container, 'warning', '⚠️ Filter reset may have issues');
                }

                // Test 4: View Mode Switching
                addTestResult(container, 'info', '4. Testing view mode switching...');

                // Test grid view
                MediaManager.setViewMode('grid');
                const gridVisible = document.getElementById('mediaGrid').style.display !== 'none';
                const listHidden = document.getElementById('mediaList').style.display === 'none';

                if (gridVisible && listHidden) {
                    addTestResult(container, 'success', '✅ Grid view mode working');
                } else {
                    addTestResult(container, 'error', '❌ Grid view mode not working');
                }

                // Test list view
                MediaManager.setViewMode('list');
                const listVisible = document.getElementById('mediaList').style.display !== 'none';
                const gridHidden = document.getElementById('mediaGrid').style.display === 'none';

                if (listVisible && gridHidden) {
                    addTestResult(container, 'success', '✅ List view mode working');
                } else {
                    addTestResult(container, 'error', '❌ List view mode not working');
                }

                // Reset to grid view
                MediaManager.setViewMode('grid');

                // Test 5: Statistics Update
                addTestResult(container, 'info', '5. Testing statistics calculation...');
                MediaManager.updateStats();

                const totalFiles = document.getElementById('totalFiles').textContent;
                const totalSize = document.getElementById('totalSize').textContent;
                const imageFiles = document.getElementById('imageFiles').textContent;

                if (totalFiles !== '-' && totalSize !== '-' && imageFiles !== '-') {
                    addTestResult(container, 'success', `✅ Statistics: ${totalFiles} files, ${totalSize} total, ${imageFiles} images`);
                } else {
                    addTestResult(container, 'error', '❌ Statistics not updating properly');
                }

                // Test 6: File Validation
                addTestResult(container, 'info', '6. Testing file validation...');

                // Test valid file
                const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
                const isValid = MediaManager.validateFile(validFile);

                if (isValid) {
                    addTestResult(container, 'success', '✅ Valid file validation working');
                } else {
                    addTestResult(container, 'error', '❌ Valid file validation failed');
                }

                // Test invalid file
                const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
                const isInvalid = !MediaManager.validateFile(invalidFile);

                if (isInvalid) {
                    addTestResult(container, 'success', '✅ Invalid file rejection working');
                } else {
                    addTestResult(container, 'error', '❌ Invalid file rejection failed');
                }

                // Summary
                addTestResult(container, 'info', '--- MEDIA MANAGER TEST SUMMARY ---');
                addTestResult(container, 'success', '🎉 Media management system is working correctly!');
                addTestResult(container, 'info', `📊 System loaded ${mediaFiles.length} media files successfully`);
                addTestResult(container, 'info', '✨ All core functionality tested and operational');

            } catch (error) {
                addTestResult(container, 'error', `Media manager test failed: ${error.message}`);
                console.error('Media manager test error:', error);
            }
        }

        async function testImageLinks() {
            const container = 'imageLinksResults';
            addTestResult(container, 'info', 'Testing all media library image links...');

            try {
                // Initialize MediaManager to get the file list
                if (typeof MediaManager === 'undefined') {
                    addTestResult(container, 'error', '❌ MediaManager not loaded');
                    return;
                }

                await MediaManager.init();
                const mediaFiles = MediaManager.mediaFiles;

                addTestResult(container, 'info', `Found ${mediaFiles.length} media files to test`);

                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                // Test each image URL
                for (const file of mediaFiles) {
                    try {
                        addTestResult(container, 'info', `Testing: ${file.filename}...`);

                        // Create a promise to test image loading
                        const imageLoadTest = new Promise((resolve, reject) => {
                            const img = new Image();
                            img.onload = () => resolve(true);
                            img.onerror = () => reject(new Error(`Failed to load: ${file.url}`));
                            img.src = file.url;

                            // Timeout after 5 seconds
                            setTimeout(() => reject(new Error(`Timeout loading: ${file.url}`)), 5000);
                        });

                        await imageLoadTest;
                        addTestResult(container, 'success', `✅ ${file.filename} - Loaded successfully`);
                        successCount++;

                    } catch (error) {
                        addTestResult(container, 'error', `❌ ${file.filename} - ${error.message}`);
                        errors.push({ filename: file.filename, url: file.url, error: error.message });
                        errorCount++;
                    }
                }

                // Summary
                addTestResult(container, 'info', '--- IMAGE LINKS TEST SUMMARY ---');

                if (errorCount === 0) {
                    addTestResult(container, 'success', `🎉 ALL ${successCount} IMAGES LOADED SUCCESSFULLY!`);
                    addTestResult(container, 'success', '✅ No broken image links found');
                    addTestResult(container, 'info', '🖼️ All media library images are properly linked and accessible');
                } else {
                    addTestResult(container, 'warning', `⚠️ ${successCount} images loaded, ${errorCount} failed`);

                    addTestResult(container, 'error', '❌ BROKEN IMAGE LINKS FOUND:');
                    errors.forEach(error => {
                        addTestResult(container, 'error', `   • ${error.filename}: ${error.error}`);
                    });

                    addTestResult(container, 'info', '🔧 Please check the file paths and ensure images exist in the correct location');
                }

                // Additional file system verification
                addTestResult(container, 'info', '--- FILE SYSTEM VERIFICATION ---');
                addTestResult(container, 'info', 'Expected image locations:');
                addTestResult(container, 'info', '📁 images/blog/Pediatric-Compounding-1.png');
                addTestResult(container, 'info', '📁 images/blog/hormonebalance.png');
                addTestResult(container, 'info', '📁 images/blog/ldn-therapy.png');
                addTestResult(container, 'info', '📁 images/blog/personalized-medication.png');
                addTestResult(container, 'info', '📁 images/blog/perimenopause-transition.png');
                addTestResult(container, 'info', '📁 images/blog/bioidentical-hormones.png');
                addTestResult(container, 'info', '📁 images/blog/gutbrainconnection.png');
                addTestResult(container, 'info', '📁 images/blog/Low Dose Naltrexone Therapy.png');
                addTestResult(container, 'info', '📁 images/blog/author-*.jpg files');
                addTestResult(container, 'info', '📁 images/blog/default-*.* files');

            } catch (error) {
                addTestResult(container, 'error', `Image links test failed: ${error.message}`);
                console.error('Image links test error:', error);
            }
        }

        async function showAllPosts() {
            const container = 'allPostsResults';
            addTestResult(container, 'info', 'Loading all blog posts...');

            try {
                const posts = await Dashboard.fetchExistingBlogPosts();
                addTestResult(container, 'success', `Found ${posts.length} total blog posts`);

                posts.forEach(post => {
                    const wordCount = Dashboard.calculateWordCount(post);
                    const preview = document.createElement('div');
                    preview.className = 'post-preview';
                    preview.innerHTML = `
                        <div class="post-title">${post.title} ${post.featured ? '⭐' : ''}</div>
                        <div class="post-meta">${post.category} • ${post.author} • ${post.date} • ${wordCount} words</div>
                        <div class="post-content">${post.metaDescription}</div>
                        <div style="margin-top: 10px;">
                            <strong>Tags:</strong> ${post.tags.join(', ')}
                        </div>
                    `;
                    document.getElementById(container).appendChild(preview);
                });

            } catch (error) {
                addTestResult(container, 'error', `Failed to load all posts: ${error.message}`);
            }
        }

        // Auto-run dashboard test on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testDashboardIntegration, 1000);
        });
    </script>
</body>
</html>

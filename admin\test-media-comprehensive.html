<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Media Test</title>
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/media-manager.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container { padding: 20px; max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-results { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-item { border: 1px solid #ddd; padding: 10px; text-align: center; border-radius: 5px; }
        .test-item img { max-width: 100%; height: 100px; object-fit: cover; }
        .status { margin-top: 5px; font-weight: bold; font-size: 12px; }
        .console-output { background: #000; color: #fff; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> NP Labs Media Library Comprehensive Test</h1>
        <p>This test validates all aspects of the media library functionality.</p>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2>Test Controls</h2>
            <button class="btn btn-primary" onclick="runAllTests()">
                <i class="fas fa-play"></i> Run All Tests
            </button>
            <button class="btn btn-secondary" onclick="testImageLoading()">
                <i class="fas fa-images"></i> Test Image Loading
            </button>
            <button class="btn btn-secondary" onclick="testMediaManager()">
                <i class="fas fa-cogs"></i> Test Media Manager
            </button>
            <button class="btn btn-secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i> Clear Results
            </button>
        </div>
        
        <!-- Test Results Summary -->
        <div class="test-section">
            <h2>Test Results Summary</h2>
            <div id="testSummary" class="test-results">
                <p>Click "Run All Tests" to begin testing...</p>
            </div>
        </div>
        
        <!-- Image Loading Test -->
        <div class="test-section">
            <h2>Image Loading Test</h2>
            <div id="imageTestResults"></div>
        </div>
        
        <!-- Media Manager Test -->
        <div class="test-section">
            <h2>Media Manager Functionality Test</h2>
            <div id="mediaManagerResults"></div>
        </div>
        
        <!-- Console Output -->
        <div class="test-section">
            <h2>Console Output</h2>
            <div id="consoleOutput" class="console-output"></div>
        </div>
    </div>
    
    <!-- Load Media Manager Scripts -->
    <script src="js/admin-core.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/media-manager.js"></script>
    
    <script>
        // Test configuration
        const testConfig = {
            images: [
                { name: 'Pediatric Compounding', url: '../images/blog/Pediatric-Compounding-1.png' },
                { name: 'Hormone Balance', url: '../images/blog/hormonebalance.png' },
                { name: 'LDN Therapy', url: '../images/blog/ldn-therapy.png' },
                { name: 'Personalized Medication', url: '../images/blog/personalized-medication.png' },
                { name: 'Perimenopause Transition', url: '../images/blog/perimenopause-transition.png' },
                { name: 'Bioidentical Hormones', url: '../images/blog/bioidentical-hormones.png' },
                { name: 'Gut Brain Connection', url: '../images/blog/gutbrainconnection.png' },
                { name: 'LDN Therapy Alt', url: '../images/blog/Low Dose Naltrexone Therapy.png' },
                { name: 'Author Elena', url: '../images/blog/author-elena.jpg' },
                { name: 'Author Thomas', url: '../images/blog/author-thomas.jpg' },
                { name: 'Default Author', url: '../images/blog/default-author.jpg' },
                { name: 'Default Post', url: '../images/blog/default-post.svg' }
            ]
        };
        
        let testResults = {
            imageLoading: { passed: 0, failed: 0, total: 0 },
            mediaManager: { passed: 0, failed: 0, total: 0 },
            overall: { passed: 0, failed: 0, total: 0 }
        };
        
        // Console capture
        const consoleDiv = document.getElementById('consoleOutput');
        const originalConsole = { log: console.log, error: console.error, warn: console.warn };
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #888">[${timestamp}]</span> <span class="${type}">${message}</span>`;
            consoleDiv.appendChild(logEntry);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warning');
        };
        
        // Test functions
        async function runAllTests() {
            clearResults();
            addToConsole('Starting comprehensive media library tests...', 'info');
            
            try {
                await testImageLoading();
                await testMediaManager();
                updateTestSummary();
                addToConsole('All tests completed!', 'success');
            } catch (error) {
                addToConsole(`Test suite failed: ${error.message}`, 'error');
            }
        }
        
        async function testImageLoading() {
            addToConsole('Testing image loading...', 'info');
            const container = document.getElementById('imageTestResults');
            container.innerHTML = '<div class="test-grid"></div>';
            const grid = container.querySelector('.test-grid');
            
            testResults.imageLoading = { passed: 0, failed: 0, total: testConfig.images.length };
            
            for (const imageData of testConfig.images) {
                const testItem = document.createElement('div');
                testItem.className = 'test-item';
                testItem.innerHTML = `
                    <h4>${imageData.name}</h4>
                    <img src="${imageData.url}" alt="${imageData.name}">
                    <div class="status" id="status-${imageData.name.replace(/\s+/g, '-')}">Loading...</div>
                `;
                
                const img = testItem.querySelector('img');
                const status = testItem.querySelector('.status');
                
                try {
                    await new Promise((resolve, reject) => {
                        img.onload = () => {
                            status.textContent = 'Loaded';
                            status.className = 'status success';
                            testResults.imageLoading.passed++;
                            addToConsole(`✓ ${imageData.name} loaded successfully`);
                            resolve();
                        };
                        
                        img.onerror = () => {
                            status.textContent = 'Failed';
                            status.className = 'status error';
                            testResults.imageLoading.failed++;
                            addToConsole(`✗ ${imageData.name} failed to load`, 'error');
                            reject(new Error(`Failed to load ${imageData.name}`));
                        };
                        
                        // Timeout after 10 seconds
                        setTimeout(() => {
                            if (status.textContent === 'Loading...') {
                                status.textContent = 'Timeout';
                                status.className = 'status warning';
                                testResults.imageLoading.failed++;
                                addToConsole(`⚠ ${imageData.name} loading timeout`, 'warning');
                                reject(new Error(`Timeout loading ${imageData.name}`));
                            }
                        }, 10000);
                    });
                } catch (error) {
                    // Error already handled in promise
                }
                
                grid.appendChild(testItem);
            }
            
            const successRate = Math.round((testResults.imageLoading.passed / testResults.imageLoading.total) * 100);
            addToConsole(`Image loading test completed: ${testResults.imageLoading.passed}/${testResults.imageLoading.total} (${successRate}%)`, 'info');
        }
        
        async function testMediaManager() {
            addToConsole('Testing Media Manager functionality...', 'info');
            const container = document.getElementById('mediaManagerResults');
            
            testResults.mediaManager = { passed: 0, failed: 0, total: 5 };
            const tests = [];
            
            // Test 1: MediaManager exists
            try {
                if (typeof MediaManager !== 'undefined') {
                    tests.push({ name: 'MediaManager object exists', status: 'passed' });
                    testResults.mediaManager.passed++;
                    addToConsole('✓ MediaManager object exists');
                } else {
                    throw new Error('MediaManager not found');
                }
            } catch (error) {
                tests.push({ name: 'MediaManager object exists', status: 'failed', error: error.message });
                testResults.mediaManager.failed++;
                addToConsole(`✗ MediaManager object test failed: ${error.message}`, 'error');
            }
            
            // Test 2: getExistingMediaFiles
            try {
                const files = await MediaManager.getExistingMediaFiles();
                if (files && files.length > 0) {
                    tests.push({ name: 'getExistingMediaFiles returns data', status: 'passed', details: `${files.length} files` });
                    testResults.mediaManager.passed++;
                    addToConsole(`✓ getExistingMediaFiles returned ${files.length} files`);
                } else {
                    throw new Error('No files returned');
                }
            } catch (error) {
                tests.push({ name: 'getExistingMediaFiles returns data', status: 'failed', error: error.message });
                testResults.mediaManager.failed++;
                addToConsole(`✗ getExistingMediaFiles test failed: ${error.message}`, 'error');
            }
            
            // Test 3: validateImageLoading (if available)
            try {
                if (typeof MediaManager.validateImageLoading === 'function') {
                    const validationResults = await MediaManager.validateImageLoading();
                    tests.push({ name: 'validateImageLoading function', status: 'passed', details: `${validationResults.loaded}/${validationResults.total} loaded` });
                    testResults.mediaManager.passed++;
                    addToConsole(`✓ validateImageLoading completed: ${validationResults.loaded}/${validationResults.total} images loaded`);
                } else {
                    throw new Error('validateImageLoading function not found');
                }
            } catch (error) {
                tests.push({ name: 'validateImageLoading function', status: 'failed', error: error.message });
                testResults.mediaManager.failed++;
                addToConsole(`✗ validateImageLoading test failed: ${error.message}`, 'error');
            }
            
            // Test 4: formatFileSize
            try {
                const formattedSize = MediaManager.formatFileSize(1024);
                if (formattedSize === '1 KB') {
                    tests.push({ name: 'formatFileSize function', status: 'passed' });
                    testResults.mediaManager.passed++;
                    addToConsole('✓ formatFileSize function works correctly');
                } else {
                    throw new Error(`Expected '1 KB', got '${formattedSize}'`);
                }
            } catch (error) {
                tests.push({ name: 'formatFileSize function', status: 'failed', error: error.message });
                testResults.mediaManager.failed++;
                addToConsole(`✗ formatFileSize test failed: ${error.message}`, 'error');
            }
            
            // Test 5: formatDate
            try {
                const formattedDate = MediaManager.formatDate(new Date('2024-01-01'));
                if (formattedDate && formattedDate !== 'Unknown') {
                    tests.push({ name: 'formatDate function', status: 'passed' });
                    testResults.mediaManager.passed++;
                    addToConsole('✓ formatDate function works correctly');
                } else {
                    throw new Error('formatDate returned invalid result');
                }
            } catch (error) {
                tests.push({ name: 'formatDate function', status: 'failed', error: error.message });
                testResults.mediaManager.failed++;
                addToConsole(`✗ formatDate test failed: ${error.message}`, 'error');
            }
            
            // Display results
            container.innerHTML = `
                <div class="test-results">
                    ${tests.map(test => `
                        <div class="${test.status}">
                            <strong>${test.name}:</strong> 
                            <span class="${test.status}">${test.status.toUpperCase()}</span>
                            ${test.details ? ` (${test.details})` : ''}
                            ${test.error ? ` - ${test.error}` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        function updateTestSummary() {
            testResults.overall.total = testResults.imageLoading.total + testResults.mediaManager.total;
            testResults.overall.passed = testResults.imageLoading.passed + testResults.mediaManager.passed;
            testResults.overall.failed = testResults.imageLoading.failed + testResults.mediaManager.failed;
            
            const overallSuccessRate = Math.round((testResults.overall.passed / testResults.overall.total) * 100);
            
            document.getElementById('testSummary').innerHTML = `
                <h3>Overall Test Results</h3>
                <div class="success">✓ Passed: ${testResults.overall.passed}</div>
                <div class="error">✗ Failed: ${testResults.overall.failed}</div>
                <div class="info">📊 Success Rate: ${overallSuccessRate}%</div>
                
                <h4>Detailed Results:</h4>
                <div><strong>Image Loading:</strong> ${testResults.imageLoading.passed}/${testResults.imageLoading.total} (${Math.round((testResults.imageLoading.passed / testResults.imageLoading.total) * 100)}%)</div>
                <div><strong>Media Manager:</strong> ${testResults.mediaManager.passed}/${testResults.mediaManager.total} (${Math.round((testResults.mediaManager.passed / testResults.mediaManager.total) * 100)}%)</div>
            `;
        }
        
        function clearResults() {
            document.getElementById('testSummary').innerHTML = '<p>Tests cleared. Click "Run All Tests" to begin...</p>';
            document.getElementById('imageTestResults').innerHTML = '';
            document.getElementById('mediaManagerResults').innerHTML = '';
            consoleDiv.innerHTML = '';
            testResults = {
                imageLoading: { passed: 0, failed: 0, total: 0 },
                mediaManager: { passed: 0, failed: 0, total: 0 },
                overall: { passed: 0, failed: 0, total: 0 }
            };
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addToConsole('Comprehensive media test page loaded', 'info');
            addToConsole('Ready to run tests!', 'success');
        });
    </script>
</body>
</html>

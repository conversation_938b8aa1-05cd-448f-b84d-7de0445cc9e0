<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Library Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }
        .image-error {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            border-radius: 4px;
        }
        .image-filename {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .upload-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Media Library Fix Verification</h1>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number" id="totalCount">-</div>
            <div class="stat-label">Total Files</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="loadedCount">-</div>
            <div class="stat-label">Successfully Loaded</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="failedCount">-</div>
            <div class="stat-label">Failed to Load</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="duplicateCount">-</div>
            <div class="stat-label">Duplicates Found</div>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Test Controls</h2>
        <button onclick="runComprehensiveTest()">Run Comprehensive Test</button>
        <button onclick="loadAllImages()">Load All Images</button>
        <button onclick="checkForDuplicates()">Check for Duplicates</button>
        <button onclick="validateFilePaths()">Validate File Paths</button>
        <button onclick="testUploadPersistence()">Test Upload Persistence</button>
        <button onclick="clearUploadedFiles()">Clear Uploaded Files</button>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🖼️ Image Preview Grid</h2>
        <div id="imageGrid" class="image-grid"></div>
    </div>

    <script src="js/admin-utils.js"></script>
    <script src="js/media-manager.js"></script>
    <script>
        let testResults = [];
        
        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date() });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    [${result.timestamp.toLocaleTimeString()}] ${result.message}
                </div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }
        
        async function runComprehensiveTest() {
            testResults = [];
            addResult('🔍 Starting comprehensive media library test...', 'info');
            
            try {
                // Initialize media manager
                await MediaManager.init();
                addResult('✅ Media manager initialized successfully', 'success');
                
                // Run validation
                const results = await MediaManager.validateImageLoading();
                
                // Update stats
                document.getElementById('totalCount').textContent = results.total;
                document.getElementById('loadedCount').textContent = results.loaded;
                document.getElementById('failedCount').textContent = results.failed;
                document.getElementById('duplicateCount').textContent = results.duplicates.length;
                
                // Report results
                if (results.failed === 0 && results.duplicates.length === 0) {
                    addResult('🎉 ALL TESTS PASSED! No missing images or duplicates found.', 'success');
                } else {
                    if (results.failed > 0) {
                        addResult(`❌ ${results.failed} images failed to load`, 'error');
                        results.errors.forEach(error => {
                            addResult(`   - ${error.filename}: ${error.error}`, 'error');
                        });
                    }
                    if (results.duplicates.length > 0) {
                        addResult(`⚠️ ${results.duplicates.length} duplicate filenames found`, 'warning');
                        results.duplicates.forEach(dup => {
                            addResult(`   - ${dup.filename} (IDs: ${dup.ids.join(', ')})`, 'warning');
                        });
                    }
                }
                
                // Load image grid
                await loadAllImages();
                
            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        async function loadAllImages() {
            addResult('🖼️ Loading image preview grid...', 'info');
            
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';
            
            for (const file of MediaManager.mediaFiles) {
                const item = document.createElement('div');
                item.className = 'image-item';
                
                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = file.url;
                    img.alt = file.filename;
                    
                    img.onload = () => {
                        addResult(`✅ ${file.filename} loaded successfully`, 'success');
                    };
                    
                    img.onerror = () => {
                        addResult(`❌ ${file.filename} failed to load`, 'error');
                        img.style.display = 'none';
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'image-error';
                        errorDiv.textContent = '❌ Failed to load';
                        item.appendChild(errorDiv);
                    };
                    
                    item.appendChild(img);
                } else {
                    const placeholder = document.createElement('div');
                    placeholder.className = 'image-error';
                    placeholder.textContent = `📄 ${file.type}`;
                    item.appendChild(placeholder);
                }
                
                const filename = document.createElement('div');
                filename.className = 'image-filename';
                filename.textContent = file.filename;
                item.appendChild(filename);
                
                grid.appendChild(item);
            }
        }
        
        async function checkForDuplicates() {
            addResult('🔍 Checking for duplicate filenames...', 'info');
            
            const filenameMap = new Map();
            const duplicates = [];
            
            for (const file of MediaManager.mediaFiles) {
                if (filenameMap.has(file.filename)) {
                    duplicates.push({
                        filename: file.filename,
                        ids: [filenameMap.get(file.filename), file.id]
                    });
                } else {
                    filenameMap.set(file.filename, file.id);
                }
            }
            
            if (duplicates.length === 0) {
                addResult('✅ No duplicate filenames found', 'success');
            } else {
                addResult(`⚠️ Found ${duplicates.length} duplicate filename(s)`, 'warning');
                duplicates.forEach(dup => {
                    addResult(`   - ${dup.filename} appears ${dup.ids.length} times`, 'warning');
                });
            }
        }
        
        async function validateFilePaths() {
            addResult('🔍 Validating file paths...', 'info');

            let validPaths = 0;
            let invalidPaths = 0;

            for (const file of MediaManager.mediaFiles) {
                try {
                    const response = await fetch(file.url, { method: 'HEAD' });
                    if (response.ok) {
                        validPaths++;
                        addResult(`✅ ${file.filename} - path valid`, 'success');
                    } else {
                        invalidPaths++;
                        addResult(`❌ ${file.filename} - path invalid (${response.status})`, 'error');
                    }
                } catch (error) {
                    invalidPaths++;
                    addResult(`❌ ${file.filename} - path error: ${error.message}`, 'error');
                }
            }

            addResult(`📊 Path validation complete: ${validPaths} valid, ${invalidPaths} invalid`,
                     invalidPaths === 0 ? 'success' : 'warning');
        }

        async function testUploadPersistence() {
            addResult('🔄 Testing upload persistence...', 'info');

            // Check localStorage
            const uploadedFiles = JSON.parse(localStorage.getItem('nplabs_uploaded_files') || '[]');
            addResult(`📁 Found ${uploadedFiles.length} files in localStorage`, 'info');

            // Check IndexedDB
            try {
                const dbFiles = await checkIndexedDBFiles();
                addResult(`💾 Found ${dbFiles.length} files in IndexedDB`, 'info');

                // Check if uploaded files persist after refresh simulation
                const uploadedInMemory = MediaManager.mediaFiles.filter(f => f.isUploaded);
                addResult(`🧠 Found ${uploadedInMemory.length} uploaded files in memory`, 'info');

                if (uploadedFiles.length > 0 && uploadedInMemory.length > 0) {
                    addResult('✅ Upload persistence is working correctly!', 'success');
                } else if (uploadedFiles.length === 0 && uploadedInMemory.length === 0) {
                    addResult('ℹ️ No uploaded files to test persistence', 'info');
                } else {
                    addResult('⚠️ Persistence mismatch detected', 'warning');
                }

            } catch (error) {
                addResult(`❌ IndexedDB check failed: ${error.message}`, 'error');
            }
        }

        async function checkIndexedDBFiles() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('NPLabsMediaDB', 1);

                request.onerror = () => reject(new Error('Failed to open IndexedDB'));

                request.onsuccess = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('files')) {
                        resolve([]);
                        return;
                    }

                    const transaction = db.transaction(['files'], 'readonly');
                    const store = transaction.objectStore('files');
                    const getAllRequest = store.getAll();

                    getAllRequest.onsuccess = () => resolve(getAllRequest.result);
                    getAllRequest.onerror = () => reject(new Error('Failed to get files from IndexedDB'));
                };
            });
        }

        async function clearUploadedFiles() {
            if (!confirm('Are you sure you want to clear all uploaded files? This will remove them from storage.')) {
                return;
            }

            addResult('🗑️ Clearing uploaded files...', 'info');

            try {
                // Clear localStorage
                localStorage.removeItem('nplabs_uploaded_files');
                localStorage.removeItem('nplabs_media_list');

                // Clear IndexedDB
                await clearIndexedDB();

                // Reload media library
                await MediaManager.loadMediaLibrary();
                MediaManager.renderMediaLibrary();
                MediaManager.updateStats();

                addResult('✅ All uploaded files cleared successfully', 'success');

                // Update stats
                document.getElementById('totalCount').textContent = MediaManager.mediaFiles.length;
                document.getElementById('loadedCount').textContent = '-';
                document.getElementById('failedCount').textContent = '-';
                document.getElementById('duplicateCount').textContent = '-';

            } catch (error) {
                addResult(`❌ Failed to clear files: ${error.message}`, 'error');
            }
        }

        async function clearIndexedDB() {
            return new Promise((resolve, reject) => {
                const deleteRequest = indexedDB.deleteDatabase('NPLabsMediaDB');
                deleteRequest.onsuccess = () => resolve();
                deleteRequest.onerror = () => reject(new Error('Failed to clear IndexedDB'));
            });
        }
        
        // Auto-run test on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runComprehensiveTest, 1000);
        });
    </script>
</body>
</html>

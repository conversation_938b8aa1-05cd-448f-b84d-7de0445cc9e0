<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .test-item { border: 1px solid #ddd; padding: 10px; text-align: center; }
        .test-item img { max-width: 100%; height: 150px; object-fit: cover; }
        .error { color: red; }
        .success { color: green; }
        .status { margin-top: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>NP Labs Media Library Test</h1>
    <p>Testing image loading from the media manager...</p>
    
    <div id="testResults"></div>
    
    <script>
        // Test images from media manager
        const testImages = [
            {
                name: 'Pediatric Compounding',
                url: '../images/blog/Pediatric-Compounding-1.png'
            },
            {
                name: 'Hormone Balance',
                url: '../images/blog/hormonebalance.png'
            },
            {
                name: 'LDN Therapy',
                url: '../images/blog/ldn-therapy.png'
            },
            {
                name: 'Personalized Medication',
                url: '../images/blog/personalized-medication.png'
            },
            {
                name: 'Perimenopause Transition',
                url: '../images/blog/perimenopause-transition.png'
            },
            {
                name: 'Bioidentical Hormones',
                url: '../images/blog/bioidentical-hormones.png'
            },
            {
                name: 'Gut Brain Connection',
                url: '../images/blog/gutbrainconnection.png'
            },
            {
                name: 'LDN Therapy Alt',
                url: '../images/blog/Low Dose Naltrexone Therapy.png'
            },
            {
                name: 'Author Elena',
                url: '../images/blog/author-elena.jpg'
            },
            {
                name: 'Author Thomas',
                url: '../images/blog/author-thomas.jpg'
            },
            {
                name: 'Default Author',
                url: '../images/blog/default-author.jpg'
            },
            {
                name: 'Default Post',
                url: '../images/blog/default-post.svg'
            }
        ];
        
        function testImageLoading() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<div class="test-grid"></div>';
            const grid = container.querySelector('.test-grid');
            
            let loadedCount = 0;
            let errorCount = 0;
            
            testImages.forEach((imageData, index) => {
                const testItem = document.createElement('div');
                testItem.className = 'test-item';
                testItem.innerHTML = `
                    <h3>${imageData.name}</h3>
                    <img src="${imageData.url}" alt="${imageData.name}">
                    <div class="status" id="status-${index}">Loading...</div>
                    <div><small>${imageData.url}</small></div>
                `;
                
                const img = testItem.querySelector('img');
                const status = testItem.querySelector('.status');
                
                img.onload = () => {
                    status.textContent = 'Loaded Successfully';
                    status.className = 'status success';
                    loadedCount++;
                    updateSummary();
                };
                
                img.onerror = () => {
                    status.textContent = 'Failed to Load';
                    status.className = 'status error';
                    errorCount++;
                    updateSummary();
                };
                
                grid.appendChild(testItem);
            });
            
            function updateSummary() {
                if (loadedCount + errorCount === testImages.length) {
                    const summary = document.createElement('div');
                    summary.innerHTML = `
                        <h2>Test Results Summary</h2>
                        <p><strong>Total Images:</strong> ${testImages.length}</p>
                        <p class="success"><strong>Successfully Loaded:</strong> ${loadedCount}</p>
                        <p class="error"><strong>Failed to Load:</strong> ${errorCount}</p>
                        <p><strong>Success Rate:</strong> ${Math.round((loadedCount / testImages.length) * 100)}%</p>
                    `;
                    container.insertBefore(summary, grid);
                }
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testImageLoading);
    </script>
</body>
</html>

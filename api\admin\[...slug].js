/**
 * Vercel API Routes for NP Labs Blog Admin
 * 
 * This API handler provides the same functionality as the Netlify function
 * but adapted for Vercel's API routes system.
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Configuration
const CONFIG = {
    adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    blogPath: path.join(process.cwd(), 'blog'),
    postsPath: path.join(process.cwd(), 'blog', 'posts'),
    imagesPath: path.join(process.cwd(), 'blog', 'assets', 'images')
};

// Simple JWT implementation (same as Netlify)
const JWT = {
    sign(payload, secret, expiresIn = '24h') {
        const header = { alg: 'HS256', typ: 'JWT' };
        const now = Math.floor(Date.now() / 1000);
        const exp = now + (24 * 60 * 60); // 24 hours
        
        const jwtPayload = { ...payload, iat: now, exp };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
        const encodedPayload = Buffer.from(JSON.stringify(jwtPayload)).toString('base64url');
        
        const signature = crypto
            .createHmac('sha256', secret)
            .update(`${encodedHeader}.${encodedPayload}`)
            .digest('base64url');
        
        return `${encodedHeader}.${encodedPayload}.${signature}`;
    },
    
    verify(token, secret) {
        try {
            const [header, payload, signature] = token.split('.');
            
            const expectedSignature = crypto
                .createHmac('sha256', secret)
                .update(`${header}.${payload}`)
                .digest('base64url');
            
            if (signature !== expectedSignature) {
                throw new Error('Invalid signature');
            }
            
            const decodedPayload = JSON.parse(Buffer.from(payload, 'base64url').toString());
            
            if (decodedPayload.exp < Math.floor(Date.now() / 1000)) {
                throw new Error('Token expired');
            }
            
            return decodedPayload;
        } catch (error) {
            throw new Error('Invalid token');
        }
    }
};

// Middleware for authentication
function requireAuth(req) {
    const token = req.headers.authorization;
    if (!token) {
        throw new Error('No token provided');
    }
    
    const bearerToken = token.startsWith('Bearer ') ? token.slice(7) : token;
    return JWT.verify(bearerToken, CONFIG.jwtSecret);
}

// Helper functions (same as Netlify)
async function getBlogStats() {
    try {
        const indexPath = path.join(CONFIG.blogPath, 'index.html');
        const indexContent = await fs.readFile(indexPath, 'utf8');
        
        const postMatches = indexContent.match(/class="post-card"/g) || [];
        const featuredMatches = indexContent.match(/class="featured-post-card"/g) || [];
        
        return {
            totalPosts: postMatches.length + featuredMatches.length,
            featuredPosts: featuredMatches.length,
            totalImages: 15,
            categories: {
                health: 4,
                wellness: 3,
                medicine: 5,
                research: 3
            }
        };
    } catch (error) {
        throw new Error('Failed to get blog stats');
    }
}

async function getAllPosts() {
    try {
        const postsDir = await fs.readdir(CONFIG.postsPath);
        const posts = [];
        
        for (const file of postsDir) {
            if (file.endsWith('.html') && !file.startsWith('_')) {
                const postPath = path.join(CONFIG.postsPath, file);
                const content = await fs.readFile(postPath, 'utf8');
                
                const titleMatch = content.match(/<title>(.*?)<\/title>/);
                const title = titleMatch ? titleMatch[1].split(' | ')[0] : 'Untitled';
                
                posts.push({
                    slug: file.replace('.html', ''),
                    title,
                    filename: file,
                    lastModified: (await fs.stat(postPath)).mtime
                });
            }
        }
        
        return posts.sort((a, b) => b.lastModified - a.lastModified);
    } catch (error) {
        throw new Error('Failed to get posts list');
    }
}

async function createPost(postData) {
    const slug = postData.slug || generateSlug(postData.title);
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    try {
        await fs.access(postPath);
        throw new Error('Post with this slug already exists');
    } catch (error) {
        if (error.code !== 'ENOENT') {
            throw error;
        }
    }
    
    const postHTML = generatePostHTML(postData, slug);
    await fs.writeFile(postPath, postHTML);
    await updateBlogIndex(postData, slug);
    
    return { slug, filename, success: true };
}

async function updatePost(slug, postData) {
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    try {
        await fs.access(postPath);
    } catch (error) {
        throw new Error('Post not found');
    }
    
    const postHTML = generatePostHTML(postData, slug);
    await fs.writeFile(postPath, postHTML);
    
    return { slug, filename, success: true };
}

async function deletePost(slug) {
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    await fs.unlink(postPath);
    return { success: true };
}

function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

function generatePostHTML(postData, slug) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${postData.title} | NP Labs Blog</title>
    <meta name="description" content="${postData.metaDescription || ''}">
</head>
<body>
    <h1>${postData.title}</h1>
    <div class="post-content">
        ${postData.content}
    </div>
</body>
</html>`;
}

async function updateBlogIndex(postData, slug) {
    console.log('Updating blog index for:', slug);
}

// Main API handler
export default async function handler(req, res) {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    try {
        const { slug } = req.query;
        const method = req.method;
        const route = slug ? slug.join('/') : '';

        // Route handling
        if (method === 'POST' && route === 'auth/login') {
            const { password } = req.body;
            
            if (password === CONFIG.adminPassword) {
                const token = JWT.sign({ role: 'admin' }, CONFIG.jwtSecret);
                return res.status(200).json({ success: true, token });
            }
            
            return res.status(401).json({ success: false, message: 'Invalid password' });
        }

        if (method === 'GET' && route === 'stats') {
            requireAuth(req);
            const stats = await getBlogStats();
            return res.status(200).json(stats);
        }

        if (method === 'GET' && route === 'posts') {
            requireAuth(req);
            const posts = await getAllPosts();
            return res.status(200).json(posts);
        }

        if (method === 'POST' && route === 'posts') {
            requireAuth(req);
            const result = await createPost(req.body);
            return res.status(201).json(result);
        }

        if (method === 'PUT' && route.startsWith('posts/')) {
            requireAuth(req);
            const postSlug = route.split('/')[1];
            const result = await updatePost(postSlug, req.body);
            return res.status(200).json(result);
        }

        if (method === 'DELETE' && route.startsWith('posts/')) {
            requireAuth(req);
            const postSlug = route.split('/')[1];
            await deletePost(postSlug);
            return res.status(200).json({ success: true });
        }

        return res.status(404).json({ error: 'Route not found' });

    } catch (error) {
        console.error('API Error:', error);
        
        if (error.message.includes('token') || error.message.includes('auth')) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        
        return res.status(500).json({ error: error.message || 'Internal server error' });
    }
}

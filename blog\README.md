# NP Labs Health & Wellness Blog

This document provides instructions for maintaining and updating the NP Labs Health & Wellness Blog. We've added automation tools to make blog management easier and more consistent.

## Blog Structure

The blog is structured as follows:

```
blog/
├── index.html             # Blog homepage with listing of all posts
├── category-wellness.html # Category page for wellness posts
├── category-health.html   # Category page for health posts
├── posts/
│   ├── post-1.html        # Individual blog post
│   ├── post-2.html        # Individual blog post
│   └── ...
└── assets/
    └── images/            # Blog-specific images
```

## Adding a New Blog Post

### Option 1: Using the Automated Tools (Recommended)

We've added automation tools to simplify blog post creation and management:

1. **Use the blog-manager.js script** in the browser console:

```javascript
addNewBlogPost({
    title: "Your Blog Post Title",
    description: "A brief description of your blog post",
    category: "Health", // Primary category: Health, Wellness, Personalized Medicine, or Research
    filename: "your-blog-post-title.html",
    imagePath: "../images/blog/your-image.png",
    imageAlt: "Descriptive alt text for your image",
    tags: ["tag1", "tag2", "tag3"],
    featured: false // Set to true if this should be the featured post
});
```

This will:
- Add your post to the blog index
- Update category counts
- Redistribute posts across pages if needed
- Maintain consistent date spacing (one week apart)

2. **Or use the Node.js script** (requires Node.js):

```
node blog/create-blog-post.js
```

Follow the prompts to create your blog post.

### Option 2: Manual Process

If you prefer to add posts manually:

1. **Create a new HTML file** in the `blog/posts/` directory with a descriptive filename (e.g., `new-treatment-approach.html`)

2. **Copy the structure** from the template file `blog-post-template.html` or an existing post to maintain consistency

3. **Update the content** including:
   - Title and meta description (important for SEO)
   - Post header information (title, category, date, author)
   - Main content
   - Author information
   - Related posts
   - Tags and social sharing links

4. **Add images** to the `blog/assets/images/` directory and update image references in your post

5. **Update the blog index** to include your new post (either in the featured section or the posts grid)

6. **Update category counts** in both HTML files and the JavaScript file

For detailed instructions, see `blog-post-creation-guide.md` and `blog-post-template-guide.md`.

## Creating Category Pages

Category pages follow the same structure as the main blog index but filter posts by category. To create a new category:

1. **Copy the structure** from an existing category page (e.g., `category-wellness.html`)

2. **Update the category title** and description

3. **Include only posts** that belong to that category

## Blog Maintenance Best Practices

1. **Consistent Formatting**:
   - Use the same HTML structure for all posts
   - Maintain consistent heading levels (H1, H2, H3)
   - Use the provided CSS classes for callouts, blockquotes, etc.

2. **Image Optimization**:
   - Compress images before uploading (aim for <200KB per image)
   - Use descriptive filenames and alt text
   - Maintain consistent image dimensions (featured images: 1200x800px, post thumbnails: 600x400px)

3. **SEO Considerations**:
   - Include relevant keywords in titles, headings, and content
   - Write compelling meta descriptions (150-160 characters)
   - Use descriptive URLs

4. **Content Guidelines**:
   - Focus on educational, informative content related to health, wellness, and personalized medicine
   - Include references where appropriate
   - Aim for 1000-2000 words per post for comprehensive coverage

## Netlify Deployment

The blog is automatically deployed when changes are pushed to the GitHub repository. To ensure smooth deployment:

1. **Test locally** before pushing changes
2. **Validate HTML** to catch any syntax errors
3. **Check all links** to ensure they work correctly

## Newsletter Integration

The newsletter form currently doesn't have backend functionality. To implement this:

1. **Option 1: Netlify Forms**
   - Add the `netlify` attribute to the form tag
   - Configure form handling in the Netlify dashboard

2. **Option 2: Third-party Service**
   - Sign up for a service like Mailchimp or ConvertKit
   - Replace the form with their embed code

## Blog Automation Tools

We've added several tools to automate blog management:

1. **blog-post-template.html** - A standardized template for all new blog posts
2. **blog-post-template-guide.md** - A guide explaining how to use the template
3. **blog-post-creation-guide.md** - Instructions for adding new posts
4. **blog-manager.js** - JavaScript utility for automating blog tasks
5. **create-blog-post.js** - Node.js script for command-line blog post creation

These tools help ensure consistency across blog posts, maintain proper category counts, and simplify the process of adding new content.

## Future Enhancements

Consider these enhancements for future development:

1. **Search functionality** using a client-side search library like Lunr.js
2. **Comment system** using a service like Disqus
3. **Social sharing buttons** for each post (already implemented in the new template)
4. **Related posts algorithm** based on categories and tags
5. **RSS feed** for subscribers
6. **Full automation** of blog post creation and index updates

## Need Help?

If you need assistance with the blog, contact the web development team at [<EMAIL>](mailto:<EMAIL>).

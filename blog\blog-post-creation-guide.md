# NP Labs Blog Post Creation Guide

This guide explains how to add new blog posts to the NP Labs website using our automated system. Following this process will ensure all blog posts are properly added to the index, category counts are updated, and posts are distributed correctly across pages.

## Automated Blog Post Creation Process

### 1. Create the Blog Post Content

First, create your blog post content following the [Blog Post Template Guide](blog-post-template-guide.md). Make sure your post includes:

- A clear title
- Meta description
- Featured image
- Content sections with headings
- Conclusion (without using "Conclusion:" in the heading)
- Tags
- Social sharing links

### 2. Save the HTML File

Save your blog post HTML file in the `blog/posts/` directory with a descriptive filename using hyphens between words (e.g., `new-blog-post-title.html`).

### 3. Add the Post to the Blog Index

There are two ways to add your post to the blog index:

#### Option A: Using the Blog Manager Script (Recommended)

1. Open the browser console on the blog index page
2. Use the `addNewBlogPost()` function with your post details:

```javascript
addNewBlogPost({
    title: "Your Blog Post Title",
    description: "A brief description of your blog post",
    category: "Health", // Primary category: Health, Wellness, Personalized Medicine, or Research
    filename: "your-blog-post-title.html",
    imagePath: "../images/blog/your-image.png",
    imageAlt: "Descriptive alt text for your image",
    tags: ["tag1", "tag2", "tag3"],
    featured: false // Set to true if this should be the featured post
});
```

This will:
- Add your post to the blog index
- Update category counts
- Redistribute posts across pages if needed
- Maintain consistent date spacing (one week apart)

#### Option B: Manual Addition

If you prefer to add the post manually:

1. Determine if your post should be on page 1 or page 2
   - Page 1 should have 7 posts (including the featured post)
   - Page 2 should have the remaining posts
2. Add your post to the appropriate page using the post card template
3. Update the dates to maintain one-week spacing
4. Update category counts in both HTML files and the JavaScript file

### 4. Update Category Counts

If using the automated script, category counts will be updated automatically. If adding manually, update:

1. The counts in `blog/index.html`
2. The counts in `blog/page-2.html`
3. The hard-coded counts in `js/blog-filter.js`

### 5. Test Your Changes

After adding your post:

1. Open the blog index page in your browser
2. Verify your post appears in the correct location
3. Check that category filtering works correctly
4. Verify pagination works correctly
5. Test on both desktop and mobile devices

## Automated Script Reference

The `blog-manager.js` script provides several functions to help manage the blog:

- `addNewBlogPost(postData)`: Adds a new blog post to the index
- `getAllBlogPosts()`: Gets all current blog posts
- `updateCategoryCounts(posts)`: Updates category counts based on all posts
- `redistributePosts(posts)`: Redistributes posts across pages
- `generateBlogPostHTML(postData)`: Generates HTML for a new blog post

## Configuration

The blog configuration is stored in the `BLOG_CONFIG` object in `blog-manager.js`:

```javascript
const BLOG_CONFIG = {
    postsPerPage: 6,
    featuredPostOnFirstPage: true,
    weeksBetweenPosts: 1,
    categories: {
        health: "Health",
        wellness: "Wellness",
        medicine: "Personalized Medicine",
        research: "Research"
    }
};
```

You can modify these settings to change how the blog behaves.

## Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify all file paths are correct
3. Ensure your HTML is properly formatted
4. Check that all required fields are included in your post data

For additional help, contact the website administrator.

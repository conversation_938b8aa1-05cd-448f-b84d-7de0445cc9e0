# NP Labs Blog Post Template Guide

This guide provides a standardized template and instructions for creating factual, engaging blog posts for NP Labs that correctly position the company as a compounding pharmacy working with healthcare providers. All blog posts should follow this template to ensure consistency across the site.

## Key Principles

1. **Accurate Positioning**: NP Labs is a compounding pharmacy that works with healthcare providers. NP Labs does NOT provide medical consultations or direct treatment plans.

2. **Educational Content**: Posts should be informative, factual, and evidence-based, building trust with potential customers.

3. **Compounding Focus**: Always tie content back to compounding services and how they support healthcare providers' treatment plans.

4. **Clear CTAs**: End posts with bold calls-to-action focused on compounding medication services, not consultations.

## Blog Post Structure

### 1. Header Section
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[TITLE] | NP Labs Blog</title>
    <meta name="description" content="[META DESCRIPTION]">
    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="../../core.css">
    <link rel="stylesheet" href="../../components.css">
    <link rel="stylesheet" href="../../sections.css">
    <link rel="stylesheet" href="../../footer.css">
    <link rel="stylesheet" href="../../css/blog.css">
    <link rel="icon" href="../../favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
```

### 2. Blog Post Header
```html
<body>
    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Blog Post Header -->
        <section class="blog-post-header">
            <div class="container">
                <h1 class="blog-post-title" data-aos="fade-up">[BLOG POST TITLE]</h1>
                <div class="blog-post-meta" data-aos="fade-up" data-aos-delay="100">
                    <span class="post-category">[CATEGORY]</span>
                    <span class="post-date">[DATE]</span>
                    <span class="post-author">By [AUTHOR NAME]</span>
                </div>

                <!-- Blog Categories -->
                <div class="blog-categories" data-aos="fade-up" data-aos-delay="200">
                    <a href="../index.html" class="category-link">All Posts</a>
                    <a href="../category-health.html" class="category-link">Health</a>
                    <a href="../category-wellness.html" class="category-link">Wellness</a>
                    <a href="../category-medicine.html" class="category-link">Personalized Medicine</a>
                    <a href="../category-research.html" class="category-link">Research</a>
                </div>
            </div>
        </section>
```

### 3. Featured Image
```html
        <!-- Featured Image -->
        <div class="blog-post-image" data-aos="fade-up">
            <img src="[IMAGE PATH]" alt="[IMAGE ALT TEXT]">
        </div>
```

### 4. Introduction
When writing the introduction:
- Introduce the topic and its relevance
- Position NP Labs as supporting healthcare providers, not offering direct consultations
- Use language like "At NP Labs, we're committed to supporting healthcare providers who..."

Example:
```html
        <!-- Blog Post Content -->
        <article class="blog-post-content">
            <p>
                [Introduce the topic and its relevance to health/wellness]
            </p>

            <p>
                At NP Labs, we're committed to supporting healthcare providers who explore [topic area]. Our compounding services help healthcare professionals provide personalized solutions for their patients.
            </p>
```

### 5. Main Content
- Organize with clear headings (h2, h3)
- Include factual, evidence-based information
- Use lists, callouts, and other formatting for readability
- Avoid language suggesting NP Labs provides medical advice or consultations
- Use phrases like "healthcare providers may recommend" or "when prescribed by your doctor"

Example callout box:
```html
            <div class="callout">
                <h4>Important Note</h4>
                <p>Quality matters significantly. At NP Labs, we work with healthcare providers to compound high-quality, evidence-based formulations when prescribed. Always consult with your healthcare provider before starting any new treatment regimen.</p>
            </div>
```

### 6. Conclusion and Post Metadata
Always end with a conclusion that:
- Summarizes key points
- Positions NP Labs as supporting healthcare providers through compounding services
- Includes a bold CTA focused on compounding medication services
- **IMPORTANT**: Do not use "Conclusion:" in the heading - simply use a descriptive heading
- Include tags and social sharing links after the conclusion

Example:
```html
            <h2>The Future of [Topic]</h2>

            <p>
                [Summary of key points about the topic]
            </p>

            <p>
                At NP Labs, we're committed to providing compounding services that support healthcare providers in their treatment plans. Our specialized compounding capabilities allow us to create customized formulations that [benefit related to topic] when prescribed by your healthcare provider.
            </p>

            <p>
                <strong>Ask your healthcare provider about how customized compounded formulations from NP Labs might support [topic benefit]. We work closely with doctors and wellness practitioners to create personalized solutions that align with your treatment plan.</strong>
            </p>

            <div class="blog-post-tags">
                <span class="tag-label">Tags:</span>
                <a href="#" class="tag">[TAG 1]</a>
                <a href="#" class="tag">[TAG 2]</a>
                <a href="#" class="tag">[TAG 3]</a>
                <a href="#" class="tag">[TAG 4]</a>
                <a href="#" class="tag">[TAG 5]</a>
            </div>

            <div class="blog-post-share">
                <span>Share this article:</span>
                <a href="#" class="share-link"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="share-link"><i class="fab fa-twitter"></i></a>
                <a href="#" class="share-link"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" class="share-link"><i class="fab fa-pinterest-p"></i></a>
            </div>
        </article>
```

### 7. Related Posts Section
```html
        <!-- Related Posts Section -->
        <section class="related-posts">
            <div class="container">
                <h2 class="text-center" data-aos="fade-up">Related Articles</h2>
                <div class="section-divider"><span><i class="fas fa-link"></i></span></div>
                <div class="related-posts-grid">
                    <!-- Related Post 1 -->
                    <article class="post-card" data-aos="fade-up">
                        [RELATED POST CONTENT]
                    </article>
                    <!-- Additional related posts -->
                </div>
            </div>
        </section>
```

### 8. Final CTA Section
```html
        <!-- Call To Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content" data-aos="fade-up">
                    <h3>Supporting [Topic] Through Compounding</h3>
                    <p><strong>NP Labs works with healthcare providers to create customized compounded formulations that support [topic benefit]. Ask your doctor or wellness practitioner about how our specialized compounding services can complement your treatment plan.</strong></p>
                    <div class="cta-buttons">
                        <a href="../../services.html" class="btn btn-primary">Learn About Our Compounding Services</a>
                        <a href="../../contact.html" class="btn btn-secondary">Contact Us</a>
                    </div>
                </div>
            </div>
        </section>
    </main>
```

### 9. Footer and Scripts
```html
    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <!-- Core Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>

    <!-- Animation Libraries -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/ScrollTrigger.min.js"></script>

    <!-- Initialize AOS -->
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>

    <!-- Application Scripts -->
    <script src="../../js/navigation.js"></script>
    <script>
        // Load header and footer for blog post pages
        document.addEventListener('DOMContentLoaded', function() {
            // Function to load HTML content
            function loadHTML(elementId, filePath) {
                fetch(filePath)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(data => {
                        document.getElementById(elementId).innerHTML = data;

                        // Initialize navigation after header is loaded
                        if (elementId === 'header-placeholder' && typeof initializeNavigation === 'function') {
                            initializeNavigation();
                        }
                    })
                    .catch(error => {
                        console.error('Error loading HTML:', error);
                    });
            }

            // Load blog post-specific header and footer
            loadHTML('header-placeholder', '_header.html');
            loadHTML('footer-placeholder', '_footer.html');
        });
    </script>
</body>
</html>
```

## Language Guidelines

### DO Use:
- "NP Labs works with healthcare providers to..."
- "When prescribed by your doctor or healthcare provider..."
- "Ask your healthcare provider about customized compounded formulations..."
- "Our compounding services support healthcare providers in..."
- "Healthcare providers may recommend..."
- "Compounded medications can be formulated to..."

### DON'T Use:
- "NP Labs offers consultations..."
- "We can help you develop a treatment plan..."
- "Our team can diagnose..."
- "Schedule a consultation with us..."
- "We recommend..."
- "Our specialists can advise you on..."

## Image Guidelines

1. Store blog post images in `blog/assets/images/` with descriptive filenames
2. Use PNG format for images
3. Ensure image filenames match or relate to the blog post title
4. Include proper alt text for accessibility

## Blog Post Checklist

Before finalizing a blog post, ensure:

- [ ] Content is factual and evidence-based
- [ ] NP Labs is correctly positioned as a compounding pharmacy supporting healthcare providers
- [ ] No language suggests NP Labs provides medical consultations
- [ ] Bold CTA focuses on compounding medication services
- [ ] All links work correctly
- [ ] Images have proper paths and alt text
- [ ] Content builds trust with potential customers
- [ ] Post follows the established blog template structure
- [ ] Conclusion heading does NOT include the word "Conclusion:"
- [ ] Tags are included (4-6 relevant tags)
- [ ] Social sharing links are present
- [ ] Related articles section includes 3 relevant posts
- [ ] Author information is complete and accurate

## Updating Blog Index

After creating a new blog post, update:
1. `blog/index.html` - Add the post to the main blog index
2. Relevant category page (e.g., `blog/category-wellness.html`) - Add the post to the appropriate category page

## Final Note

Remember that NP Labs' primary role is to provide compounding medication services based on healthcare providers' prescriptions. All blog content should educate readers about health topics while directing them to work with their healthcare providers for medical advice and treatment plans that NP Labs can then support through compounding services.

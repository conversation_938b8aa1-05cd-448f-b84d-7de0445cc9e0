<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[POST TITLE] | NP Labs Blog</title>
    <meta name="description" content="[META DESCRIPTION]">
    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="../../core.css">
    <link rel="stylesheet" href="../../components.css">
    <link rel="stylesheet" href="../../sections.css">
    <link rel="stylesheet" href="../../footer.css">
    <link rel="stylesheet" href="../../css/blog.css">
    <link rel="stylesheet" href="../../css/newsletter.css">
    <link rel="icon" href="../../favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>

    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Blog Post Header -->
        <section class="blog-post-header">
            <div class="container">
                <h1 class="blog-post-title" data-aos="fade-up">[POST TITLE]</h1>
                <div class="blog-post-meta" data-aos="fade-up" data-aos-delay="100">
                    <span class="post-category">[CATEGORY]</span>
                    <span class="post-date">[DATE]</span>
                    <span class="post-author">By [AUTHOR NAME]</span>
                </div>

                <!-- Blog Categories -->
                <div class="blog-categories" data-aos="fade-up" data-aos-delay="200">
                    <a href="../index.html" class="category-link">All Posts</a>
                    <a href="../category-health.html" class="category-link">Health</a>
                    <a href="../category-wellness.html" class="category-link">Wellness</a>
                    <a href="../category-medicine.html" class="category-link">Personalized Medicine</a>
                    <a href="../category-research.html" class="category-link">Research</a>
                    <!-- Add active class to the relevant category -->
                </div>
            </div>
        </section>

        <!-- Featured Image -->
        <div class="blog-post-image" data-aos="fade-up">
            <div class="container">
                <img src="../../images/blog/[IMAGE-FILENAME]" alt="[IMAGE ALT TEXT]">
            </div>
        </div>

        <!-- Blog Post Content -->
        <section class="blog-post-content">
            <div class="container">
                <div class="blog-post-body" data-aos="fade-up">
                    <p class="lead-paragraph">
                        [INTRODUCTION PARAGRAPH]
                    </p>

                    <h2>[FIRST SECTION HEADING]</h2>
                    <p>
                        [SECTION CONTENT]
                    </p>

                    <!-- Add more sections as needed -->

                    <h2>[FINAL SECTION HEADING - WITHOUT "CONCLUSION:"]</h2>
                    <p>
                        [CONCLUSION PARAGRAPH 1]
                    </p>

                    <p>
                        [CONCLUSION PARAGRAPH 2]
                    </p>

                    <p>
                        [CALL TO ACTION PARAGRAPH]
                    </p>

                    <div class="blog-post-tags">
                        <span class="tag-label">Tags:</span>
                        <a href="#" class="tag">[TAG 1]</a>
                        <a href="#" class="tag">[TAG 2]</a>
                        <a href="#" class="tag">[TAG 3]</a>
                        <a href="#" class="tag">[TAG 4]</a>
                        <a href="#" class="tag">[TAG 5]</a>
                    </div>

                    <div class="blog-post-share">
                        <span>Share this article:</span>
                        <a href="#" class="share-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="share-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="share-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="share-link"><i class="fab fa-pinterest-p"></i></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Author Section -->
        <section class="author-section">
            <div class="container">
                <div class="author-card" data-aos="fade-up">
                    <div class="author-image">
                        <img src="../../images/blog/[AUTHOR-IMAGE]" alt="[AUTHOR NAME]">
                    </div>
                    <div class="author-info">
                        <h3>[AUTHOR NAME]</h3>
                        <span class="author-title">[AUTHOR TITLE], NP Labs</span>
                        <p>
                            [AUTHOR BIO]
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Posts -->
        <section class="related-posts">
            <div class="container">
                <h2 data-aos="fade-up">Related Articles</h2>
                <div class="related-posts-grid">
                    <!-- Related Post 1 -->
                    <article class="post-card" data-aos="fade-up">
                        <div class="post-image">
                            <img src="../../images/blog/[RELATED-POST-1-IMAGE]" alt="[RELATED POST 1 TITLE]">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">[RELATED POST 1 CATEGORY]</span>
                                <span class="post-date">[RELATED POST 1 DATE]</span>
                            </div>
                            <h3>[RELATED POST 1 TITLE]</h3>
                            <p>
                                [RELATED POST 1 DESCRIPTION]
                            </p>
                            <a href="[RELATED-POST-1-FILENAME].html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <!-- Related Post 2 -->
                    <article class="post-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="post-image">
                            <img src="../../images/blog/[RELATED-POST-2-IMAGE]" alt="[RELATED POST 2 TITLE]">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">[RELATED POST 2 CATEGORY]</span>
                                <span class="post-date">[RELATED POST 2 DATE]</span>
                            </div>
                            <h3>[RELATED POST 2 TITLE]</h3>
                            <p>
                                [RELATED POST 2 DESCRIPTION]
                            </p>
                            <a href="[RELATED-POST-2-FILENAME].html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <!-- Related Post 3 -->
                    <article class="post-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="post-image">
                            <img src="../../images/blog/[RELATED-POST-3-IMAGE]" alt="[RELATED POST 3 TITLE]">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">[RELATED POST 3 CATEGORY]</span>
                                <span class="post-date">[RELATED POST 3 DATE]</span>
                            </div>
                            <h3>[RELATED POST 3 TITLE]</h3>
                            <p>
                                [RELATED POST 3 DESCRIPTION]
                            </p>
                            <a href="[RELATED-POST-3-FILENAME].html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Call To Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content" data-aos="fade-up">
                    <h3>Stay Updated with Health & Wellness Insights</h3>
                    <p>Subscribe to our newsletter to receive the latest research, tips, and expert advice directly to your inbox.</p>
                    <form class="newsletter-form">
                        <div class="form-group">
                            <input type="email" placeholder="Your email address" required>
                            <button type="submit" class="btn btn-primary btn-3d">Subscribe</button>
                        </div>
                        <div class="form-consent">
                            <input type="checkbox" id="consent" required>
                            <label for="consent">I agree to receive emails from NP Labs. You can unsubscribe at any time.</label>
                        </div>
                    </form>
                    <div class="cta-buttons">
                        <a href="../index.html" class="btn btn-primary">Back to Blog</a>
                        <a href="../../contact.html" class="btn btn-secondary">Contact Our Experts</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="../../js/bootstrap.min.js"></script>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>
    <script src="../../scripts.js"></script>
    <script src="../../js/include-html.js" defer></script>

</body>
</html>

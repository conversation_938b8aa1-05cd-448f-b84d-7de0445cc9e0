<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Research Articles | NP Labs Blog</title>
    <meta name="description" content="Explore our collection of research-focused articles from the experts at NP Labs.">
    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="../sections.css">
    <link rel="stylesheet" href="../footer.css">
    <link rel="stylesheet" href="../css/blog.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>

    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Hero Section with Background -->
        <section class="page-header about-hero blog-hero">
            <div class="page-header-overlay"></div>
            <div class="container">
                <h1 data-aos="fade-up">Research Articles</h1>
                <p data-aos="fade-up" data-aos-delay="200">Expert insights on research topics from NP Labs</p>
            </div>
        </section>

        <!-- Main Content -->
        <section class="content-section about-section blog-section">
            <div class="container">
                <div class="about-intro text-center" data-aos="fade-up">
                    <h2>Explore Our <span class="highlight">Research</span> Resources</h2>
                    <div class="section-divider"><span><i class="fas fa-microscope"></i></span></div>
                    <p class="lead-text">Discover expert advice and insights on research topics from our team of specialists.</p>
                </div>

                <!-- Blog Categories -->
                <div class="blog-categories" data-aos="fade-up" data-aos-delay="200">
                    <a href="index.html" class="category-link">All Posts</a>
                    <a href="category-health.html" class="category-link">Health</a>
                    <a href="category-wellness.html" class="category-link">Wellness</a>
                    <a href="category-medicine.html" class="category-link">Personalized Medicine</a>
                    <a href="category-research.html" class="category-link active">Research</a>
                </div>
            </div>
        </section>

        <!-- Blog Posts Grid -->
        <section class="blog-posts capabilities-section">
            <div class="container">
                <h3 class="text-center" data-aos="fade-up">Research Articles</h3>
                <div class="section-divider"><span><i class="fas fa-newspaper"></i></span></div>
                <div class="posts-grid capabilities-grid">
                    <!-- Post 1 -->
                    <article class="post-card" data-aos="fade-up">
                        <div class="post-image">
                            <img src="assets/images/supplementdifferences.png" alt="Supplement Quality Differences: Not All Supplements Are Created Equal">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">Research</span>
                                <span class="post-date">May 8, 2023</span>
                            </div>
                            <h3>Not All Supplements Are Created Equal: Understanding Quality Differences</h3>
                            <p>
                                Discover what makes high-quality supplements different and how to choose products that deliver results.
                            </p>
                            <a href="posts/supplement-quality.html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>
                </div>

                <!-- Pagination -->
                <div class="pagination" data-aos="fade-up">
                    <span class="current-page">1</span>
                    <a href="#" class="page-link next">Next <i class="fas fa-chevron-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Call To Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content" data-aos="fade-up">
                    <h3>Stay Updated with Health & Wellness Insights</h3>
                    <p>Subscribe to our newsletter to receive the latest research, tips, and expert advice directly to your inbox.</p>
                    <form class="newsletter-form">
                        <div class="form-group">
                            <input type="email" placeholder="Your email address" required>
                            <button type="submit" class="btn btn-primary">Subscribe</button>
                        </div>
                        <div class="form-consent">
                            <input type="checkbox" id="consent" required>
                            <label for="consent">I agree to receive emails from NP Labs. You can unsubscribe at any time.</label>
                        </div>
                    </form>
                    <div class="cta-buttons">
                        <a href="index.html" class="btn btn-primary">View All Articles</a>
                        <a href="#" class="btn btn-secondary">Contact Our Experts</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <!-- Core Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>

    <!-- Animation Libraries -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/ScrollTrigger.min.js"></script>

    <!-- Initialize AOS -->
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>

    <!-- Application Scripts -->
    <script src="../js/navigation.js"></script>
    <script src="../js/blog-card-click.js"></script>
    <script>
        // Load header and footer for blog pages
        document.addEventListener('DOMContentLoaded', function() {
            // Function to load HTML content
            function loadHTML(elementId, filePath) {
                fetch(filePath)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(data => {
                        document.getElementById(elementId).innerHTML = data;

                        // Initialize navigation after header is loaded
                        if (elementId === 'header-placeholder' && typeof initializeNavigation === 'function') {
                            initializeNavigation();
                        }
                    })
                    .catch(error => {
                        console.error('Error loading HTML:', error);
                    });
            }

            // Load blog-specific header and footer
            loadHTML('header-placeholder', '_header.html');
            loadHTML('footer-placeholder', '_footer.html');
        });
    </script>
</body>
</html>

#!/usr/bin/env node

/**
 * NP Labs Blog Post Creator
 * 
 * This script automates the creation of new blog posts and updates the blog index.
 * Run it from the command line with: node create-blog-post.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { JSDOM } = require('jsdom');

// Configuration
const BLOG_CONFIG = {
    postsPerPage: 6,
    featuredPostOnFirstPage: true,
    weeksBetweenPosts: 1,
    categories: {
        health: "Health",
        wellness: "Wellness",
        medicine: "Personalized Medicine",
        research: "Research"
    },
    paths: {
        blogDir: path.join(__dirname),
        postsDir: path.join(__dirname, 'posts'),
        indexFile: path.join(__dirname, 'index.html'),
        page2File: path.join(__dirname, 'page-2.html'),
        templateFile: path.join(__dirname, 'blog-post-template.html')
    }
};

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Main function to create a new blog post
 */
async function createBlogPost() {
    console.log('\n=== NP Labs Blog Post Creator ===\n');
    
    try {
        // 1. Collect post information
        const postData = await collectPostInfo();
        
        // 2. Generate the blog post HTML
        await generateBlogPost(postData);
        
        // 3. Update the blog index
        await updateBlogIndex(postData);
        
        console.log('\n✅ Blog post created successfully!');
        console.log(`Post created at: ${path.join(BLOG_CONFIG.paths.postsDir, postData.filename)}`);
        console.log('Blog index has been updated.');
    } catch (error) {
        console.error('\n❌ Error creating blog post:', error.message);
    } finally {
        rl.close();
    }
}

/**
 * Collect blog post information from user input
 * @returns {Promise<Object>} - Post data
 */
async function collectPostInfo() {
    const postData = {};
    
    postData.title = await askQuestion('Enter post title: ');
    postData.description = await askQuestion('Enter post description: ');
    
    // Generate filename from title
    postData.filename = postData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '') + '.html';
    
    console.log(`\nAvailable categories:`);
    Object.entries(BLOG_CONFIG.categories).forEach(([key, value]) => {
        console.log(`- ${value}`);
    });
    
    postData.category = await askQuestion('Enter primary category: ');
    postData.imagePath = await askQuestion('Enter image path (or leave empty): ');
    postData.imageAlt = await askQuestion('Enter image alt text: ');
    
    const tagsInput = await askQuestion('Enter tags (comma-separated): ');
    postData.tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag);
    
    const featuredInput = await askQuestion('Make this the featured post? (y/n): ');
    postData.featured = featuredInput.toLowerCase() === 'y';
    
    // Confirm information
    console.log('\nPost Information:');
    console.log(`- Title: ${postData.title}`);
    console.log(`- Filename: ${postData.filename}`);
    console.log(`- Category: ${postData.category}`);
    console.log(`- Featured: ${postData.featured ? 'Yes' : 'No'}`);
    
    const confirmInput = await askQuestion('\nIs this information correct? (y/n): ');
    if (confirmInput.toLowerCase() !== 'y') {
        throw new Error('Post creation cancelled by user');
    }
    
    return postData;
}

/**
 * Generate the blog post HTML file
 * @param {Object} postData - Post information
 */
async function generateBlogPost(postData) {
    console.log('\nGenerating blog post HTML...');
    
    // Read the template file
    let templateContent;
    try {
        templateContent = fs.readFileSync(BLOG_CONFIG.paths.templateFile, 'utf8');
    } catch (error) {
        throw new Error(`Could not read template file: ${error.message}`);
    }
    
    // Replace placeholders with actual content
    const postContent = templateContent
        .replace(/\\[POST TITLE\\]/g, postData.title)
        .replace(/\\[META DESCRIPTION\\]/g, postData.description)
        .replace(/\\[CATEGORY\\]/g, postData.category)
        .replace(/\\[DATE\\]/g, calculateNewPostDate())
        .replace(/\\[AUTHOR NAME\\]/g, 'NP Labs Team')
        .replace(/\\[IMAGE-FILENAME\\]/g, postData.imagePath || '')
        .replace(/\\[IMAGE ALT TEXT\\]/g, postData.imageAlt || postData.title);
    
    // Create tags HTML
    const tagsHtml = postData.tags.map(tag => `<a href="#" class="tag">${tag}</a>`).join('\n                        ');
    
    // Write the file
    const outputPath = path.join(BLOG_CONFIG.paths.postsDir, postData.filename);
    try {
        fs.writeFileSync(outputPath, postContent);
        console.log(`Blog post HTML generated at: ${outputPath}`);
    } catch (error) {
        throw new Error(`Could not write blog post file: ${error.message}`);
    }
}

/**
 * Update the blog index with the new post
 * @param {Object} postData - Post information
 */
async function updateBlogIndex(postData) {
    console.log('\nUpdating blog index...');
    
    // 1. Get all current posts
    const currentPosts = getAllBlogPosts();
    
    // 2. Add the new post
    const newPost = {
        title: postData.title,
        description: postData.description,
        category: postData.category,
        filename: postData.filename,
        imagePath: postData.imagePath || "",
        imageAlt: postData.imageAlt || postData.title,
        tags: postData.tags || [],
        featured: postData.featured,
        date: calculateNewPostDate()
    };
    
    // 3. If this is featured, un-feature the current featured post
    if (newPost.featured) {
        currentPosts.forEach(post => {
            if (post.featured) post.featured = false;
        });
    }
    
    // 4. Add the new post to the array
    currentPosts.unshift(newPost);
    
    // 5. Redistribute posts across pages
    redistributePosts(currentPosts);
    
    // 6. Update category counts
    updateCategoryCounts(currentPosts);
}

/**
 * Get all blog posts from the index files
 * @returns {Array} - Array of post objects
 */
function getAllBlogPosts() {
    console.log('Getting all current blog posts...');
    
    const posts = [];
    
    // Read index.html
    try {
        const indexContent = fs.readFileSync(BLOG_CONFIG.paths.indexFile, 'utf8');
        const indexDom = new JSDOM(indexContent);
        const indexDoc = indexDom.window.document;
        
        // Get featured post
        const featuredCard = indexDoc.querySelector('.featured-post-card');
        if (featuredCard) {
            const title = featuredCard.querySelector('h2').textContent;
            const description = featuredCard.querySelector('p').textContent.trim();
            const category = featuredCard.querySelector('.post-category').textContent;
            const dateStr = featuredCard.querySelector('.post-date').textContent;
            const link = featuredCard.querySelector('.btn').getAttribute('href');
            const filename = link.split('/').pop();
            
            // Get image info
            let imagePath = "";
            let imageAlt = "";
            const imgElement = featuredCard.querySelector('img');
            if (imgElement) {
                imagePath = imgElement.getAttribute('src');
                imageAlt = imgElement.getAttribute('alt');
            }
            
            posts.push({
                title,
                description,
                category,
                date: dateStr,
                filename,
                imagePath,
                imageAlt,
                featured: true
            });
        }
        
        // Get regular posts
        const postCards = indexDoc.querySelectorAll('.post-card');
        postCards.forEach(card => {
            const title = card.querySelector('h3').textContent;
            const description = card.querySelector('p').textContent.trim();
            const category = card.querySelector('.post-category').textContent;
            const dateStr = card.querySelector('.post-date').textContent;
            const link = card.querySelector('.read-more').getAttribute('href');
            const filename = link.split('/').pop();
            
            // Get image info
            let imagePath = "";
            let imageAlt = "";
            const imgElement = card.querySelector('img');
            if (imgElement) {
                imagePath = imgElement.getAttribute('src');
                imageAlt = imgElement.getAttribute('alt');
            }
            
            posts.push({
                title,
                description,
                category,
                date: dateStr,
                filename,
                imagePath,
                imageAlt,
                featured: false
            });
        });
        
        // Read page-2.html
        const page2Content = fs.readFileSync(BLOG_CONFIG.paths.page2File, 'utf8');
        const page2Dom = new JSDOM(page2Content);
        const page2Doc = page2Dom.window.document;
        
        // Get posts from page 2
        const page2Cards = page2Doc.querySelectorAll('.post-card');
        page2Cards.forEach(card => {
            const title = card.querySelector('h3').textContent;
            const description = card.querySelector('p').textContent.trim();
            const category = card.querySelector('.post-category').textContent;
            const dateStr = card.querySelector('.post-date').textContent;
            const link = card.querySelector('.read-more').getAttribute('href');
            const filename = link.split('/').pop();
            
            // Get image info
            let imagePath = "";
            let imageAlt = "";
            const imgElement = card.querySelector('img');
            if (imgElement) {
                imagePath = imgElement.getAttribute('src');
                imageAlt = imgElement.getAttribute('alt');
            }
            
            posts.push({
                title,
                description,
                category,
                date: dateStr,
                filename,
                imagePath,
                imageAlt,
                featured: false
            });
        });
        
        console.log(`Found ${posts.length} existing blog posts`);
        return posts;
    } catch (error) {
        console.error('Error reading blog index:', error);
        return [];
    }
}

/**
 * Calculate the date for a new post (most recent date + 1 week)
 * @returns {string} - Formatted date string
 */
function calculateNewPostDate() {
    // Find the most recent post date
    let mostRecentDate = new Date();
    
    // Add one week to the current date
    mostRecentDate.setDate(mostRecentDate.getDate() + (7 * BLOG_CONFIG.weeksBetweenPosts));
    
    // Format the date as Month Day, Year
    return mostRecentDate.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

/**
 * Redistribute posts across pages based on configuration
 * @param {Array} posts - Array of all posts
 */
function redistributePosts(posts) {
    console.log('Redistributing posts across pages...');
    
    // This is a placeholder for the actual implementation
    // In a real implementation, this would update the HTML files
    
    // Calculate how many pages we need
    const postsPerPage = BLOG_CONFIG.postsPerPage;
    const firstPageCount = BLOG_CONFIG.featuredPostOnFirstPage ? postsPerPage + 1 : postsPerPage;
    const totalPosts = posts.length;
    
    // Calculate number of pages needed
    let pagesNeeded = 1;
    let remainingPosts = totalPosts - firstPageCount;
    
    while (remainingPosts > 0) {
        pagesNeeded++;
        remainingPosts -= postsPerPage;
    }
    
    console.log(`Need ${pagesNeeded} pages for ${totalPosts} posts`);
    console.log('Page 1: Featured post + first ' + (firstPageCount - 1) + ' regular posts');
    
    for (let i = 2; i <= pagesNeeded; i++) {
        const startIndex = firstPageCount + (i - 2) * postsPerPage;
        const endIndex = Math.min(startIndex + postsPerPage, totalPosts);
        console.log(`Page ${i}: Posts ${startIndex} to ${endIndex - 1}`);
    }
}

/**
 * Update category counts based on all posts
 * @param {Array} posts - Array of all posts
 */
function updateCategoryCounts(posts) {
    console.log('Updating category counts...');
    
    // Count posts in each category
    const counts = {
        all: posts.length
    };
    
    // Initialize all category counts to 0
    Object.keys(BLOG_CONFIG.categories).forEach(cat => {
        counts[cat] = 0;
    });
    
    // Count posts in each category
    posts.forEach(post => {
        // Get category key from display name
        let categoryKey = null;
        for (const [key, value] of Object.entries(BLOG_CONFIG.categories)) {
            if (value === post.category) {
                categoryKey = key;
                break;
            }
        }
        
        if (categoryKey && counts[categoryKey] !== undefined) {
            counts[categoryKey]++;
        }
    });
    
    console.log('Category counts:', counts);
    
    // In a real implementation, this would update the HTML files
    // For now, we'll just log the counts
    Object.entries(counts).forEach(([category, count]) => {
        console.log(`${category}: ${count}`);
    });
}

/**
 * Helper function to ask a question and get user input
 * @param {string} question - The question to ask
 * @returns {Promise<string>} - User's answer
 */
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

// Run the script
createBlogPost();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health & Wellness Blog | Page 2 | NP Labs</title>
    <meta name="description" content="Explore the latest insights on health, wellness, and personalized medicine from the experts at NP Labs.">
    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="../core.css">
    <link rel="stylesheet" href="../components.css">
    <link rel="stylesheet" href="../sections.css">
    <link rel="stylesheet" href="../footer.css">
    <link rel="stylesheet" href="../css/blog.css">
    <link rel="stylesheet" href="../css/newsletter.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>

    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Hero Section with Background -->
        <section class="page-header about-hero blog-hero">
            <div class="page-header-overlay"></div>
            <div class="container">
                <h1 data-aos="fade-up">Health & Wellness Blog</h1>
                <p data-aos="fade-up" data-aos-delay="200">Insights and expertise from NP Labs</p>
            </div>
        </section>

        <!-- Main Content -->
        <section class="content-section about-section blog-section">
            <div class="container">
                <div class="about-intro text-center" data-aos="fade-up">
                    <h2>Explore Our <span class="highlight">Health & Wellness</span> Resources</h2>
                    <div class="section-divider"><span><i class="fas fa-heartbeat"></i></span></div>
                    <p class="lead-text">Insights, research, and expert advice on personalized medicine, compounding, and holistic health approaches.</p>
                </div>

                <!-- Blog Categories -->
                <div class="blog-categories" data-aos="fade-up" data-aos-delay="200" role="toolbar" aria-label="Blog category filters">
                    <a href="#" class="category-link active" data-category="all" role="button" aria-pressed="true" aria-controls="blog-posts-container">All Posts <span class="post-count">(10)</span></a>
                    <a href="#" class="category-link" data-category="health" role="button" aria-pressed="false" aria-controls="blog-posts-container">Health <span class="post-count">(4)</span></a>
                    <a href="#" class="category-link" data-category="wellness" role="button" aria-pressed="false" aria-controls="blog-posts-container">Wellness <span class="post-count">(2)</span></a>
                    <a href="#" class="category-link" data-category="medicine" role="button" aria-pressed="false" aria-controls="blog-posts-container">Personalized Medicine <span class="post-count">(5)</span></a>
                    <a href="#" class="category-link" data-category="research" role="button" aria-pressed="false" aria-controls="blog-posts-container">Research <span class="post-count">(2)</span></a>
                </div>
            </div>
        </section>

        <!-- Blog Posts Grid -->
        <section class="blog-posts capabilities-section">
            <div class="container">
                <h3 class="text-center" data-aos="fade-up">More Articles</h3>
                <div class="section-divider"><span><i class="fas fa-newspaper"></i></span></div>
                <div id="blog-posts-container" class="posts-grid capabilities-grid" aria-live="polite">
                    <!-- Post 1: Understanding LDN Therapy -->
                    <article class="post-card" data-aos="fade-up" data-categories="health,medicine">
                        <div class="post-image">
                            <img src="assets/images/Low Dose Naltrexone Therapy.png" alt="Low Dose Naltrexone (LDN) Therapy">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">Health</span>
                                <span class="post-date">February 17, 2025</span>
                            </div>
                            <h3>Understanding Low Dose Naltrexone (LDN) Therapy</h3>
                            <p>
                                Learn about the benefits, applications, and science behind Low Dose Naltrexone therapy for autoimmune conditions.
                            </p>
                            <a href="posts/understanding-ldn-therapy.html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <!-- Post 2: Hormone Optimization -->
                    <article class="post-card" data-aos="fade-up" data-aos-delay="100" data-categories="health,medicine">
                        <div class="post-image">
                            <img src="../images/blog/hormonebalance.png" alt="Hormone Optimization: Finding Your Balance">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">Health</span>
                                <span class="post-date">February 10, 2025</span>
                            </div>
                            <h3>Hormone Optimization: Finding Your Balance</h3>
                            <p>
                                Discover the importance of hormone balance and how personalized hormone therapy can improve quality of life.
                            </p>
                            <a href="posts/hormone-balance.html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <!-- Post 3: Bioidentical Hormones -->
                    <article class="post-card" data-aos="fade-up" data-aos-delay="200" data-categories="research">
                        <div class="post-image">
                            <img src="assets/images/mythsandfacts.png" alt="Bioidentical Hormones: Separating Myths from Facts">
                        </div>
                        <div class="post-content">
                            <div class="post-meta">
                                <span class="post-category">Research</span>
                                <span class="post-date">February 3, 2025</span>
                            </div>
                            <h3>Bioidentical Hormones: Separating Myths from Facts</h3>
                            <p>
                                An evidence-based look at bioidentical hormone therapy, its benefits, risks, and how it differs from conventional hormone replacement.
                            </p>
                            <a href="posts/bioidentical-hormones.html" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>
                </div>

                <!-- Pagination -->
                <div class="pagination" data-aos="fade-up">
                    <a href="index.html" class="page-link prev"><i class="fas fa-chevron-left"></i> Previous</a>
                    <a href="index.html" class="page-link">1</a>
                    <span class="current-page">2</span>
                    <span class="page-link next disabled">Next <i class="fas fa-chevron-right"></i></span>
                </div>
            </div>
        </section>

        <!-- Call To Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content" data-aos="fade-up">
                    <h3>Stay Updated with Health & Wellness Insights</h3>
                    <p>Subscribe to our newsletter to receive the latest research, tips, and expert advice directly to your inbox.</p>
                    <form id="newsletter-form" class="newsletter-form">
                        <div class="form-group">
                            <input type="email" id="newsletter-email" name="email" placeholder="Your email address" required>
                            <button type="submit" class="btn btn-primary btn-3d">Subscribe</button>
                        </div>
                        <div class="form-consent">
                            <input type="checkbox" id="newsletter-consent" name="consent" required>
                            <label for="newsletter-consent">I agree to receive emails from NP Labs. You can unsubscribe at any time.</label>
                        </div>
                    </form>
                    <div id="newsletter-response" class="newsletter-response"></div>
                    <div class="cta-buttons">
                        <a href="posts/understanding-ldn-therapy.html" class="btn btn-primary">Read Latest Article</a>
                        <a href="../contact.html" class="btn btn-secondary">Contact Our Experts</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>
    <script src="../scripts.js"></script>
    <script src="../js/include-html.js" defer></script>
    <script src="../js/newsletter.js" defer></script>
    <script>
        // Category filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            const categoryLinks = document.querySelectorAll('.category-link');
            const blogPosts = document.querySelectorAll('.post-card');

            categoryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Update active state
                    categoryLinks.forEach(l => {
                        l.classList.remove('active');
                        l.setAttribute('aria-pressed', 'false');
                    });
                    this.classList.add('active');
                    this.setAttribute('aria-pressed', 'true');

                    const category = this.getAttribute('data-category');

                    // Filter posts
                    blogPosts.forEach(post => {
                        if (category === 'all') {
                            post.style.display = 'block';
                        } else {
                            const postCategories = post.getAttribute('data-categories').split(',');
                            if (postCategories.includes(category)) {
                                post.style.display = 'block';
                            } else {
                                post.style.display = 'none';
                            }
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>

# PowerShell script to check meta tags in HTML files
$htmlFiles = Get-ChildItem -Path . -Filter *.html -Recurse | Where-Object { $_.FullName -notlike "*\_*" }

$results = @()

foreach ($file in $htmlFiles) {
    $content = Get-Content -Path $file.FullName -Raw
    $relativePath = $file.FullName.Replace("C:\Users\<USER>\Downloads\NPLabtest2\nplabs\", "")
    
    # Extract title
    $titleMatch = [regex]::Match($content, '<title>(.*?)</title>')
    $title = if ($titleMatch.Success) { $titleMatch.Groups[1].Value } else { "Missing" }
    
    # Extract description
    $descMatch = [regex]::Match($content, '<meta name="description" content="(.*?)"')
    $description = if ($descMatch.Success) { $descMatch.Groups[1].Value } else { "Missing" }
    
    # Check for keywords
    $keywordsMatch = [regex]::Match($content, '<meta name="keywords" content="(.*?)"')
    $keywords = if ($keywordsMatch.Success) { $keywordsMatch.Groups[1].Value } else { "Missing" }
    
    # Check for canonical URL
    $canonicalMatch = [regex]::Match($content, '<link rel="canonical" href="(.*?)"')
    $canonical = if ($canonicalMatch.Success) { $canonicalMatch.Groups[1].Value } else { "Missing" }
    
    # Check for Open Graph tags
    $ogTitleMatch = [regex]::Match($content, '<meta property="og:title" content="(.*?)"')
    $ogTitle = if ($ogTitleMatch.Success) { $ogTitleMatch.Groups[1].Value } else { "Missing" }
    
    $ogDescMatch = [regex]::Match($content, '<meta property="og:description" content="(.*?)"')
    $ogDesc = if ($ogDescMatch.Success) { $ogDescMatch.Groups[1].Value } else { "Missing" }
    
    $ogImageMatch = [regex]::Match($content, '<meta property="og:image" content="(.*?)"')
    $ogImage = if ($ogImageMatch.Success) { $ogImageMatch.Groups[1].Value } else { "Missing" }
    
    # Check for Twitter Card tags
    $twitterCardMatch = [regex]::Match($content, '<meta name="twitter:card" content="(.*?)"')
    $twitterCard = if ($twitterCardMatch.Success) { $twitterCardMatch.Groups[1].Value } else { "Missing" }
    
    $result = [PSCustomObject]@{
        FilePath = $relativePath
        Title = $title
        Description = $description
        Keywords = $keywords
        Canonical = $canonical
        OGTitle = $ogTitle
        OGDescription = $ogDesc
        OGImage = $ogImage
        TwitterCard = $twitterCard
    }
    
    $results += $result
}

# Export to CSV
$results | Export-Csv -Path "meta_tags_analysis.csv" -NoTypeInformation

# Display summary
Write-Host "Analysis complete. Results saved to meta_tags_analysis.csv"
Write-Host "Summary:"
Write-Host "Total files analyzed: $($results.Count)"
Write-Host "Files missing description: $(($results | Where-Object { $_.Description -eq "Missing" }).Count)"
Write-Host "Files missing keywords: $(($results | Where-Object { $_.Keywords -eq "Missing" }).Count)"
Write-Host "Files missing canonical: $(($results | Where-Object { $_.Canonical -eq "Missing" }).Count)"
Write-Host "Files missing Open Graph tags: $(($results | Where-Object { $_.OGTitle -eq "Missing" }).Count)"
Write-Host "Files missing Twitter Card tags: $(($results | Where-Object { $_.TwitterCard -eq "Missing" }).Count)"

/* START OF FILE components.css */

/* Buttons - Mobile First */
.btn {
    display: inline-block;
    padding: 14px 20px; /* Larger padding for mobile touch */
    border-radius: var(--border-radius);
    font-weight: 700;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-speed);
    border: 2px solid transparent;
    font-size: 0.9rem; /* Mobile-first font size */
    min-height: 44px; /* Minimum touch target */
    min-width: 120px; /* Minimum width for touch */
    line-height: 1.2;
}

/* Responsive button adjustments */
@media (min-width: 576px) {
    .btn {
        padding: 12px 22px;
        font-size: 0.95rem;
    }
}

@media (min-width: 768px) {
    .btn {
        padding: 10px 20px;
        font-size: 0.95rem;
    }
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    color: white;
    border-color: var(--primary-blue-dark);
}

.btn-secondary {
    background-color: var(--secondary-teal);
    color: white;
    border-color: var(--secondary-teal);
}

.btn-secondary:hover {
    background-color: var(--secondary-teal-dark);
    color: white;
    border-color: var(--secondary-teal-dark);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline:hover {
    background-color: var(--primary-blue);
    color: white;
}

.btn-large {
    padding: 14px 28px;
    font-size: 1.1rem;
}

/* Header & Navigation */
.site-header {
    background: white;
    padding: 0.5rem 0; /* Reduced from 1rem to 0.5rem (50% reduction) */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky; /* Revert back to sticky */
    top: 0; /* Ensure it sticks to the very top */
    left: 0; /* Ensure it spans full width when sticking */
    width: 100%; /* Ensure it spans full width */
    z-index: 999;
    margin: 0; /* Explicitly remove any margin */
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0; /* Reduced from 15px to 10px (33% reduction) */
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    flex-shrink: 0;
}

.logo img {
    height: 35px; /* Reduced from 40px to 35px */
    width: auto;
}

.main-nav {
    flex: 1;
    margin: 0 15px;
}

.main-nav ul { /* Applied to .nav-items directly later */
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.main-nav .submenu { /* General submenu styles, refined later */
    display: block;
    align-items: stretch;
    gap: 0;
}

.main-nav a { /* Applied to .nav-link later */
    color: var(--dark-grey);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    position: relative;
    padding-bottom: 5px;
}

.main-nav .submenu a { /* Applied to .submenu a later */
    padding-bottom: 0;
}

.main-nav a::after { /* Applied to .nav-link::after later */
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color, #00509e);
    transition: width 0.3s ease;
}

.main-nav .submenu a::after { /* Applied to .submenu a::after later */
    display: none;
}

.main-nav a:hover { /* Applied to .nav-link:hover later */
    color: var(--primary-color);
}

.main-nav .submenu a:hover { /* Applied to .submenu a:hover later */
    color: var(--primary-blue);
}

.main-nav a:hover::after { /* Applied to .nav-link:hover::after later */
    width: 100%;
}

.main-nav a i { /* Applied to .nav-link i later */
    font-size: 0.7rem;
    margin-top: 2px;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 12px; /* Reduced from 15px */
}

.mobile-nav-close {
    display: none !important; /* Kept as important */
}

.mobile-only-nav-item {
    display: none;
}

.mobile-nav-toggle {
    display: none;
}

/* Responsive Header & Nav */
@media (max-width: 992px) {
    .main-nav {
        display: none; /* Hide by default on mobile */
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background: white;
        padding: 60px 30px 30px;
        transition: all 0.3s ease;
        margin: 0;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        overflow-y: auto; /* Allow scrolling */
    }

    .main-nav.active {
        display: block; /* Show only when active */
        right: 0;
        z-index: 1000; /* Ensure it's on top when active */
        max-height: 100vh; /* Limit to viewport height */
        overflow-y: auto; /* Enable scrolling when content overflows */
    }

    .main-nav ul { /* Applies to .nav-items */
        flex-direction: column;
        align-items: flex-start;
        gap: 15px; /* Smaller gap for mobile */
        padding: 20px; /* Add padding for mobile */
    }

    /* Mobile Mega Menu Styles (applies from 992px down) */
    .mega-menu {
        position: static; /* Override absolute positioning */
        width: 100%; /* Take full width within the nav item */
        max-width: none; /* Override any desktop max-width */
        opacity: 1; /* Ensure visible (JS handles display) */
        visibility: visible; /* Ensure visible */
        transform: none; /* Reset any desktop transform */
        box-shadow: none; /* Remove desktop shadow */
        border-top: none; /* Remove desktop border */
        margin: 0; /* Reset desktop margin */
        padding: 1rem 0 0.5rem 1rem; /* Indent mobile submenus */
        background-color: transparent; /* Match background of nav panel */
        display: none; /* Hide by default (JS toggles to block) */
        height: auto; /* Allow height to adjust to content */
        overflow: visible; /* Ensure content isn't clipped */
    }

    .nav-item.has-mega-menu.active .mega-menu {
        display: block; /* Show when parent is active */
    }

    .mega-menu-content {
        display: flex;
        flex-direction: column; /* Stack columns vertically */
        gap: 1rem;
        padding: 0;
        width: 100%; /* Ensure content takes full width */
        height: auto; /* Allow height to adjust */
        overflow: visible; /* Ensure content isn't clipped */
    }

    .mega-menu-column {
        flex: none; /* Prevent shrinking */
        width: 100%; /* Take full width */
        height: auto; /* Allow height to adjust */
        overflow: visible; /* Ensure column content isn't clipped */
    }

    .mega-menu-column h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        border-bottom: none;
        padding-bottom: 0;
        color: var(--dark-grey) !important; /* Keep important if necessary */
    }

    .mega-menu-column ul {
        display: block !important; /* Keep important if necessary */
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        list-style: none !important;
    }

    .mega-menu-column li {
        display: block !important; /* Keep important if necessary */
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        margin-bottom: 0.5rem !important;
    }

    .mega-menu-column a {
        display: block !important; /* Keep important if necessary */
        visibility: visible !important;
        opacity: 1 !important;
        color: var(--dark-grey) !important;
        text-decoration: none !important;
        padding: 0.25rem 0 !important;
        width: 100%;
        box-sizing: border-box;
        white-space: normal;
    }

    .mega-menu-column a:hover {
        background-color: #f5f5f5;
    }

    /* Show submenu toggle buttons on mobile */
    .mobile-submenu-toggle {
        display: inline-block;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        color: var(--dark-grey);
        z-index: 2; /* Ensure it's above other elements */
        width: 40px; /* Adjust width as needed */
        height: 40px; /* Adjust height as needed */
        text-align: center; /* Center the icon */
    }

    /* Style for nav items with submenus */
    .nav-item.has-mega-menu > .nav-link {
        display: block;
        width: calc(100% - 40px); /* Full width minus toggle button width */
        padding-right: 0; /* Reset padding */
        position: relative; /* Ensure proper stacking context */
        z-index: 1; /* Ensure it's below the toggle button */
    }

    /* Style for active submenu */
    .nav-item.has-mega-menu.active .mobile-submenu-toggle i {
        transform: rotate(180deg);
    }

    .mobile-nav-close {
        display: block !important; /* Keep important */
        position: absolute;
        top: 20px;
        right: 20px;
        background: none;
        border: none;
        font-size: 24px;
        color: var(--dark-grey);
        cursor: pointer;
        padding: 0;
    }

    .mobile-only-nav-item {
        display: block;
        width: 100%;
    }

    .user-actions {
        display: none;
    }

    .mobile-nav-toggle {
        display: block;
        background: none;
        border: none;
        font-size: 24px;
        color: var(--dark-grey);
        cursor: pointer;
        padding: 0;
    }

    /* Mobile-only navigation items inside mobile nav panel */
    .mobile-only-nav-item .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
}

/* Responsive Header & Nav */
@media (max-width: 992px) {
    .nav-items {
        flex-direction: column;
        align-items: flex-start;
        gap: 25px;
    }
    .nav-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    .nav-link {
        padding: 15px 0; /* Larger padding for mobile touch targets */
        font-size: 1rem; /* Slightly larger font for mobile */
        min-height: 44px; /* Ensure minimum touch target */
        display: flex;
        align-items: center;
    }
    .mega-menu {
        position: static;
        width: 100%;
        max-width: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border-top: none;
        margin: 0;
        padding: 1rem 0 0.5rem 1rem;
        background-color: transparent;
        display: none;
        height: auto;
        overflow: visible;
    }
    .nav-item.has-mega-menu > .nav-link {
        display: block;
        width: calc(100% - 40px);
        padding-right: 0;
        position: relative;
        z-index: 1;
    }
    .mobile-submenu-toggle {
        display: inline-block;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        color: var(--dark-grey);
        z-index: 2;
        width: 40px;
        height: 40px;
        text-align: center;
    }
    .nav-item.has-mega-menu.active .mobile-submenu-toggle i {
        transform: rotate(180deg);
    }
    .mega-menu-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 0;
        width: 100%;
        height: auto;
        overflow: visible;
    }
    .mega-menu-column {
        flex: none;
        width: 100%;
        height: auto;
        overflow: visible;
    }
    .mega-menu-column h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        border-bottom: none;
        padding-bottom: 0;
        color: var(--dark-grey) !important;
    }
    .mega-menu-column ul {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        list-style: none !important;
    }
    .mega-menu-column li {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        margin-bottom: 0.5rem !important;
    }
    .mega-menu-column a {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: var(--dark-grey) !important;
        text-decoration: none !important;
        padding: 0.25rem 0 !important;
    }
}

/* Navigation styles */
.nav-items {
    display: flex;
    align-items: center;
    gap: 25px; /* Reduced from 30px */
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    padding: 6px 0; /* Reduced from 8px to 6px */
    cursor: pointer;
}

/* Mega menu styles */
.mega-menu-wrapper {
    position: relative;
}

.mega-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 4px;
    z-index: 1000;
    padding: 15px;
    width: 500px;
}

.menu-items-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    row-gap: 8px;
    background: #ffffff;
}

.menu-item {
    padding: 8px 15px;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    width: 100%;
    box-sizing: border-box;
    display: block;
    white-space: normal;
}

.menu-item:hover {
    background-color: #f5f5f5;
}

/* Show mega menu on wrapper hover */
.mega-menu-wrapper:hover .mega-menu {
    display: block;
}

/* Remove any backgrounds */
.nav-item,
.nav-link,
.nav-item:hover,
.nav-link:hover {
    background: none !important;
}

/* Mobile styles */
@media (max-width: 992px) {
    .mega-menu {
        position: static;
        width: 100%;
        transform: none;
        left: 0;
        box-shadow: none;
        padding: 10px;
    }

    .menu-items-grid {
        grid-template-columns: 1fr;
        gap: 5px;
        column-gap: 0;
    }

    .menu-item {
        padding: 8px 0;
    }
}
<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
$to_email = '<EMAIL>';
$subject = 'New Contact Form Submission from NP Labs Website';

// Get the form data
$raw_input = file_get_contents('php://input');

// Log the raw input for debugging
file_put_contents('contact_form_log.txt', date('Y-m-d H:i:s') . " - Raw input: " . $raw_input . "\n", FILE_APPEND);

$data = json_decode($raw_input, true);

// Log the decoded data
file_put_contents('contact_form_log.txt', date('Y-m-d H:i:s') . " - Decoded data: " . print_r($data, true) . "\n", FILE_APPEND);

// Validate required fields
$required_fields = ['name', 'email', 'subject', 'message'];
$missing_fields = array_filter($required_fields, function($field) use ($data) {
    return empty($data[$field]);
});

// Log validation results
file_put_contents('contact_form_log.txt', date('Y-m-d H:i:s') . " - Missing fields: " . print_r($missing_fields, true) . "\n", FILE_APPEND);

if (!empty($missing_fields)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Please fill in all required fields.']);
    exit;
}

// Validate email format
if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Please enter a valid email address.']);
    exit;
}

// Prepare the email message
$message = "New contact form submission:\n\n";
$message .= "Name: " . htmlspecialchars($data['name']) . "\n";
$message .= "Email: " . htmlspecialchars($data['email']) . "\n";
$message .= "Phone: " . (isset($data['phone']) ? htmlspecialchars($data['phone']) : 'Not provided') . "\n";
$message .= "User Type: " . (isset($data['userType']) ? htmlspecialchars($data['userType']) : 'Not specified') . "\n";
$message .= "Subject: " . htmlspecialchars($data['subject']) . "\n\n";
$message .= "Message: " . htmlspecialchars($data['message']);

// Send the email
$headers = array(
    'From: ' . $data['email'],
    'Reply-To: ' . $data['email'],
    'Cc: <EMAIL>',
    'X-Mailer: PHP/' . phpversion()
);

if (mail($to_email, $subject, $message, implode("\r\n", $headers))) {
    echo json_encode(['success' => true, 'message' => 'Thank you for your message. We will get back to you shortly.']);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Sorry, there was an error sending your message. Please try again later.']);
}
?>

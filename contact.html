<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/contact.html">
    <title>Contact NP Labs: Expert Compounding Pharmacy Services</title>
    <meta name="description" content="Get in touch with NP Labs' compounding pharmacy team in Athens, Greece. Questions about custom medications? Contact us for personalized pharmaceutical solutions.">
    <meta name="keywords" content="contact compounding pharmacy, NP Labs contact, custom medication questions, pharmacy consultation">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Contact NP Labs: Expert Compounding Pharmacy Services">
    <meta property="og:description" content="Get in touch with NP Labs' compounding pharmacy team in Athens, Greece. Questions about custom medications? Contact us for personalized pharmaceutical solutions.">
    <meta property="og:image" content="https://www.nplabs.com/images/og-image.jpg">
    <meta property="og:url" content="https://www.nplabs.com/contact.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact NP Labs: Expert Compounding Pharmacy Services">
    <meta name="twitter:description" content="Get in touch with NP Labs' compounding pharmacy team in Athens, Greece. Questions about custom medications? Contact us for personalized pharmaceutical solutions.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/twitter-image.jpg">

    <!-- External Libraries First -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles for Contact Page - Aligned with About page */
        :root {
            /* Assuming these are defined in core.css or similar, but redefining for clarity if needed */
            --primary-color: #2c3e50;
            --primary-color-dark: #1d2a35;
            --accent-color: #3498db;
            --accent-color-dark: #2980b9;
            --text-color: #34495e;
            --text-color-light: #7f8c8d;
            --bg-color: #ffffff;
            --bg-color-light: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #2ecc71;
            --error-color: #e74c3c;
        }

        /* Hero section - Adopt from about.html */
        .contact-hero {
            /* Inherit base from .page-header in sections.css if possible */
            background: url('images/contact-hero.webp') no-repeat center center/cover; /* Same or new hero image */
            position: relative;
            text-align: center;
        }

        .contact-hero .page-header-overlay {
            /* Inherit from sections.css or define here */
            background-color: rgba(44, 62, 80, 0.7); /* Darker overlay like about.css potentially */
            /* Ensure padding is sufficient */
            padding: 100px 0;
        }

        .contact-hero h1,
        .contact-hero p {
             /* Ensure text is white and styled like about.html hero */
            color: #ffffff;
        }
        .contact-hero h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 15px;
        }
         .contact-hero p {
            font-size: 1.2em;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Main content section - Use content-section base */
        .content-section.contact-body-section {
            padding: 80px 0;
            background-color: var(--bg-color-light);
        }

        /* Wrapper for form + info - Style like content cards/boxes on about.html */
        .contact-wrapper {
            background-color: var(--bg-color);
            border-radius: 10px; /* Match about page card radius */
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08); /* Match about page card shadow */
            overflow: hidden;
        }

        /* Columns within the wrapper */
        .contact-form-column {
            padding: 40px; /* Adjust padding */
        }

        .contact-info-column {
            /* Revert dark background - match about page's content areas */
            background-color: var(--bg-color); /* White or light grey bg */
            color: var(--text-color); /* Standard text color */
            padding: 40px;
            /* Remove border radius if it's inside the main wrapper */
            border-left: 1px solid var(--border-color); /* Add a subtle separator */
        }

        .contact-info-column h3 {
            color: var(--primary-color); /* Heading color */
            margin-bottom: 30px;
            font-weight: 600;
        }

        .contact-info-list {
            list-style: none;
            padding: 0;
        }

        .contact-info-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
            font-size: 1.05em;
            color: var(--text-color); /* Ensure text color is readable */
        }

        .contact-info-list i {
            font-size: 1.2em; /* Adjust icon size */
            margin-right: 15px;
            color: var(--accent-color); /* Use accent color for icons */
            width: 22px; /* Adjust width */
            text-align: center;
            margin-top: 4px;
        }

        .contact-info-list a {
            color: var(--accent-color); /* Link color */
            text-decoration: none;
            transition: color 0.3s ease;
            font-weight: 500;
        }

        .contact-info-list a:hover {
            color: var(--accent-color-dark);
            text-decoration: underline;
        }

        .operating-hours-info {
            margin-top: 30px;
            border-top: 1px solid var(--border-color);
            padding-top: 25px;
            color: var(--text-color);
        }

        .operating-hours-info h4 {
            display: flex;
            align-items: center;
            font-size: 1.1em;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .operating-hours-info h4 i {
            margin-right: 10px;
            color: var(--accent-color);
            font-size: 1.2em;
        }

        .operating-hours-info p {
            margin-bottom: 5px;
            font-size: 1em;
            padding-left: 32px; /* Align with text, accounting for icon width+margin */
            color: var(--text-color-light);
        }

        /* Form Styling - Align with general site components */
        .contact-form-column h3 {
             color: var(--primary-color);
             margin-bottom: 30px;
             font-weight: 600;
        }

        .form-label {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 5px; /* Match site's input radius */
            padding: 10px 15px;
            transition: all 0.3s ease;
            font-size: 1em;
            color: var(--text-color);
            box-shadow: none; /* Remove inner shadow if any */
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15); /* Standard focus ring */
            outline: none;
        }

        textarea.form-control {
            min-height: 120px;
        }

        /* Submit Button - Match btn-primary or btn-accent style */
        .btn-submit {
            background-color: var(--accent-color);
            border: none;
            color: #ffffff !important;
            padding: 12px 35px;
            font-size: 1em; /* Match standard button size */
            font-weight: 600;
            border-radius: 5px; /* Match standard button radius */
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            width: auto;
            display: inline-block;
            margin-top: 15px;
            box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2); /* Subtle shadow */
        }

        .btn-submit:hover {
            background-color: var(--accent-color-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-submit i {
            margin-right: 8px;
            font-size: 0.95em;
        }

        /* Map Section Styling */
        .map-section {
            padding: 0; /* Remove padding if map is edge-to-edge */
            background-color: var(--bg-color); /* Or light grey */
        }

        .map-container {
            height: 450px; /* Adjust height as needed */
            width: 100%;
            border-radius: 0;
            overflow: hidden;
            box-shadow: none;
            margin-top: 0;
        }

        .map-container iframe {
            width: 100%;
            height: 100%;
            border: 0;
        }

        /* Adjustments for feedback messages */
        #formResponse {
             font-weight: 500;
        }
        .alert-success {
            color: var(--success-color);
        }
         .alert-danger {
            color: var(--error-color);
        }

    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header contact-hero">
        <div class="page-header-overlay"></div>
        <div class="container position-relative" style="z-index: 2;">
            <h1 data-aos="fade-up">Contact Us</h1>
            <p data-aos="fade-up" data-aos-delay="200">We're here to help. Reach out any time for inquiries or support.</p>
        </div>
    </section>

    <!-- Main Contact Section (Form + Info) -->
    <section class="content-section contact-body-section">
        <div class="container">
            <div class="contact-wrapper" data-aos="fade-up" data-aos-delay="100">
                <div class="row g-0">
                    <!-- Form Column -->
                    <div class="col-lg-7">
                        <div class="contact-form-column">
                            <h3>Send Us a Message</h3>
                            <form id="contactForm" class="needs-validation mt-4" novalidate>
                                <div class="row">
                                    <!-- Name -->
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                        <div class="invalid-feedback">Please enter your name.</div>
                                    </div>
                                    <!-- Email -->
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                        <div class="invalid-feedback">Please enter a valid email address.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Phone (Optional) -->
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>
                                    <!-- Subject -->
                                    <div class="col-md-6 mb-3">
                                        <label for="subject" class="form-label">Subject *</label>
                                        <input type="text" class="form-control" id="subject" name="subject" required>
                                        <div class="invalid-feedback">Please enter a subject.</div>
                                    </div>
                                </div>

                                <!-- User Type -->
                                <div class="mb-3">
                                    <label class="form-label">I am a *</label>
                                    <div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="userType" id="userTypePatient" value="Patient" required>
                                            <label class="form-check-label" for="userTypePatient">Patient</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="userType" id="userTypePrescriber" value="Prescriber" required>
                                            <label class="form-check-label" for="userTypePrescriber">Prescriber</label>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback">Please select one.</div>
                                </div>
                                <!-- Message -->
                                <div class="mb-4">
                                    <label for="message" class="form-label">Your Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                                    <div class="invalid-feedback">Please enter your message.</div>
                                </div>
                                <!-- Privacy Policy -->
                                <div class="mb-4 form-check">
                                    <input type="checkbox" class="form-check-input" id="privacyCheck" required>
                                    <label class="form-check-label privacy-text" for="privacyCheck">
                                        I agree to the <a href="privacy-policy.html" target="_blank">Privacy Policy</a>.
                                    </label>
                                    <div class="invalid-feedback">You must agree to the Privacy Policy.</div>
                                </div>
                                <!-- Submit Button (EmailJS only) -->
                                <div class="text-center">
                                    <button type="button" class="btn btn-submit" id="emailJSBtn">
                                        <i class="fas fa-paper-plane"></i>
                                        <span class="button-text">Send Message</span>
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                    </button>
                                </div>
                                <!-- Response Message -->
                                <div id="formResponse" class="mt-4 text-center"></div>
                            </form>
                        </div>
                    </div>
                    <!-- Info Column -->
                    <div class="col-lg-5 d-flex align-items-stretch">
                        <div class="contact-info-column w-100">
                            <h3>Contact Information</h3>
                            <ul class="contact-info-list">
                                <li>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>Papanikoli Street No.40,<br>Chalandri, Attica, 15232<br>Greece</div>
                                </li>
                                <li>
                                    <i class="fas fa-phone-alt"></i>
                                    <div><a href="tel:+302111110900">+30 21 1111 0900</a></div>
                                </li>
                                <li>
                                    <i class="fas fa-envelope"></i>
                                    <div><a href="mailto:<EMAIL>"><EMAIL></a></div>
                                </li>
                            </ul>
                            <div class="operating-hours-info">
                                <h4><i class="fas fa-clock"></i> Operating Hours</h4>
                                <p>Mon - Fri: 9:00 AM - 6:00 PM</p>
                                <p>Saturday: Closed</p>
                                <p>Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section (Now separate) -->
    <section class="map-section">
        <div class="map-container" data-aos="fade-up">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3143.918351099078!2d23.79711891532319!3d38.00111197971938!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14a1981b188809a1%3A0x1f4b683a935d0f47!2sPapanikoli%2040%2C%20Chalandri%20152%2032%2C%20Greece!5e0!3m2!1sen!2sus!4v1678886400000!5m2!1sen!2sus"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade">
            </iframe>
        </div>
    </section>

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<!-- Core Libraries -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>

<!-- Animation Libraries -->
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/ScrollTrigger.min.js"></script>

<!-- EmailJS for client-side email sending -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>

<!-- Initialize AOS -->
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>

<!-- Application Scripts -->
<script src="scripts.js"></script>
<!-- Note: We're using scripts.js for header/footer loading instead of include-html.js -->

<script>
    console.log('Contact page script loaded');

    // Initialize EmailJS
    (function() {
        // EmailJS public key
        emailjs.init("zV2H0z16RX_HcuWVV");
        console.log('EmailJS initialized with key: zV2H0z16RX_HcuWVV');
    })();

    // We're using the handleFormSubmission function inside the DOMContentLoaded event handler
    // No global handler function needed

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM fully loaded');
        const form = document.getElementById('contactForm');
        const emailJSBtn = document.getElementById('emailJSBtn');
        const responseDiv = document.getElementById('formResponse');

        console.log('Form element:', form);
        console.log('Button element:', emailJSBtn);

        if (!emailJSBtn) {
            console.error('ERROR: Button with ID "emailJSBtn" not found!');
            // Try to find the button by class instead
            const altButton = document.querySelector('.btn-submit');
            console.log('Alternative button found by class:', altButton);
            if (altButton) {
                altButton.addEventListener('click', handleFormSubmission);
                console.log('Added click handler to alternative button');
            }
        }

        // Define the form submission handler function
        function handleFormSubmission() {
            console.log('Form submission handler called');

            // Basic validation
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                console.log('Form validation failed');
                return;
            }

            console.log('Form is valid, proceeding with submission');

            // Show loading state
            const button = this;
            const buttonText = button.querySelector('.button-text');
            const spinner = button.querySelector('.spinner-border');

            if (buttonText && spinner) {
                buttonText.classList.add('d-none');
                spinner.classList.remove('d-none');
            }
            button.disabled = true;

            // Get form data
            const formData = new FormData(form);
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            console.log('Form data collected:', data);

            // Send with EmailJS
            const templateParams = {
                to_email: '<EMAIL>',
                cc_email: '<EMAIL>',
                from_name: data.name,
                from_email: data.email,
                phone: data.phone || 'Not provided',
                user_type: data.userType || 'Not specified',
                subject: data.subject,
                message: data.message,
                formatDate: new Date().toLocaleString() // Add current date/time
            };

            console.log('EmailJS - template params:', templateParams);

            emailjs.send('service_o1sfm9l', 'template_vpxoh8d', templateParams)
                .then(function(response) {
                    console.log('EmailJS SUCCESS!', response.status, response.text);
                    responseDiv.innerHTML = `
                        <div class="alert alert-success custom-success-message">
                            <div class="success-icon"><i class="fas fa-check-circle"></i></div>
                            <h4 class="success-title">Thank you, ${data.name}!</h4>
                            <p class="success-text">Your message has been sent successfully. We appreciate you reaching out to us.</p>
                            <p class="success-subtext">Our team will review your inquiry and get back to you shortly.</p>
                        </div>
                    `;

                    // Add custom styles for the success message
                    const style = document.createElement('style');
                    style.textContent = `
                        .custom-success-message {
                            background-color: #f0f9f4;
                            border-left: 4px solid #28a745;
                            border-radius: 4px;
                            padding: 20px;
                            text-align: center;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                        }
                        .success-icon {
                            font-size: 48px;
                            color: #28a745;
                            margin-bottom: 15px;
                        }
                        .success-title {
                            color: #28a745;
                            font-weight: 700;
                            margin-bottom: 10px;
                        }
                        .success-text {
                            font-size: 16px;
                            margin-bottom: 10px;
                            color: #333;
                        }
                        .success-subtext {
                            font-size: 14px;
                            color: #666;
                            font-style: italic;
                        }
                    `;
                    document.head.appendChild(style);
                    form.reset();
                    form.classList.remove('was-validated');
                })
                .catch(function(error) {
                    console.error('EmailJS FAILED...', error);
                    responseDiv.innerHTML = `
                        <div class="alert alert-danger custom-error-message">
                            <div class="error-icon"><i class="fas fa-exclamation-circle"></i></div>
                            <h4 class="error-title">Message Not Sent</h4>
                            <p class="error-text">We encountered an issue while sending your message.</p>
                            <p class="error-subtext">Please try again or contact us directly at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    `;

                    // Add custom styles for the error message
                    const errorStyle = document.createElement('style');
                    errorStyle.textContent = `
                        .custom-error-message {
                            background-color: #fff8f8;
                            border-left: 4px solid #dc3545;
                            border-radius: 4px;
                            padding: 20px;
                            text-align: center;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                        }
                        .error-icon {
                            font-size: 48px;
                            color: #dc3545;
                            margin-bottom: 15px;
                        }
                        .error-title {
                            color: #dc3545;
                            font-weight: 700;
                            margin-bottom: 10px;
                        }
                        .error-text {
                            font-size: 16px;
                            margin-bottom: 10px;
                            color: #333;
                        }
                        .error-subtext {
                            font-size: 14px;
                            color: #666;
                        }
                        .error-subtext a {
                            color: #dc3545;
                            text-decoration: underline;
                        }
                    `;
                    document.head.appendChild(errorStyle);
                })
                .finally(() => {
                    // Reset button
                    if (buttonText && spinner) {
                        buttonText.classList.remove('d-none');
                        spinner.classList.add('d-none');
                    }
                    button.disabled = false;
                });
        }

        // Add event listener to the EmailJS button if it exists
        if (emailJSBtn) {
            console.log('Adding click handler to emailJSBtn');

            // Check if the button already has event listeners
            const oldClone = emailJSBtn.cloneNode(true);
            emailJSBtn.replaceWith(oldClone);
            const newBtn = document.getElementById('emailJSBtn');
            console.log('Replaced button to clear any existing event listeners');

            // Add our event listener
            newBtn.addEventListener('click', handleFormSubmission);
            console.log('Added new click handler to emailJSBtn');
        }

        // Prevent form submission (we're using the button click instead)
        form.addEventListener('submit', function(e) {
            e.preventDefault();
        });

        /* PHP SUBMISSION CODE - KEPT FOR LATER USE
        // Combined form validation and submission handler
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Bootstrap validation
            if (!form.checkValidity()) {
                e.stopPropagation();
                form.classList.add('was-validated');
                return; // Stop here if form is invalid
            }

            // Form is valid, proceed with submission
            form.classList.add('was-validated');

            // Show loading state
            const button = e.target.querySelector('.btn-submit');
            const buttonText = button.querySelector('.button-text');
            const spinner = button.querySelector('.spinner-border');

            buttonText.classList.add('d-none');
            spinner.classList.remove('d-none');
            button.disabled = true;

            // Get form data
            const formData = new FormData(form);
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // Try PHP first, fall back to EmailJS if PHP fails
            sendFormDataWithPHP(data)
                .catch(error => {
                    console.log('PHP submission failed, trying EmailJS...', error);
                    return sendFormDataWithEmailJS(data);
                })
                .then(result => {
                    // Show response message
                    responseDiv.innerHTML = `
                        <div class="alert alert-${result.success ? 'success' : 'danger'}">
                            ${result.message}
                        </div>
                    `;

                    // Reset form if successful
                    if (result.success) {
                        form.reset();
                    }
                })
                .catch(error => {
                    console.error('Both submission methods failed', error);
                    responseDiv.innerHTML = `
                        <div class="alert alert-danger">
                            An error occurred. Please try again later.
                        </div>
                    `;
                })
                .finally(() => {
                    // Reset button state
                    buttonText.classList.remove('d-none');
                    spinner.classList.add('d-none');
                    button.disabled = false;
                });
        });
        */

        /* PHP FUNCTIONS - KEPT FOR LATER USE
        // Function to send data using PHP
        function sendFormDataWithPHP(data) {
            console.log('Attempting PHP submission with data:', data);
            return fetch('contact-handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('PHP response status:', response.status);
                if (!response.ok) {
                    throw new Error(`PHP submission failed with status ${response.status}`);
                }
                return response.json();
            })
            .then(responseData => {
                console.log('PHP submission successful:', responseData);
                return responseData;
            });
        }

        // Function to send data using EmailJS (for PHP fallback)
        function sendFormDataWithEmailJS(data) {
            console.log('Attempting EmailJS submission with data:', data);

            // Prepare EmailJS template parameters
            const templateParams = {
                to_email: '<EMAIL>',
                cc_email: '<EMAIL>',
                from_name: data.name,
                from_email: data.email,
                phone: data.phone || 'Not provided',
                user_type: data.userType || 'Not specified', // Handle case where userType might be missing
                subject: data.subject,
                message: data.message
            };

            console.log('EmailJS template parameters:', templateParams);

            // EmailJS service ID and template ID
            return emailjs.send('service_o1sfm9l', 'template_vpxoh8d', templateParams)
                .then(function(response) {
                    console.log('EmailJS SUCCESS!', response.status, response.text);
                    return { success: true, message: 'Thank you for your message. We will get back to you shortly.' };
                })
                .catch(function(error) {
                    console.error('EmailJS FAILED...', error);
                    // Return a formatted error for better debugging
                    return { success: false, message: 'Error sending message. Please try again or contact us <NAME_EMAIL>' };
                });
        }
        */
    });
</script>

</body>
</html>

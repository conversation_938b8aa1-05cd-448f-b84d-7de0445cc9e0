/* --- START OF FILE css/core.css --- */

/* --- CSS Variables --- */
:root {
    /* Colors */
    --primary-blue: #00509e;
    --primary-blue-dark: #003d7a;
    --primary-blue-light: #1a68b0;
    --secondary-teal: #00a896;
    --secondary-teal-dark: #008577;
    --secondary-teal-light: #1ab8a8;
    --light-grey: #f5f7fa;
    --medium-grey: #e0e5ec;
    --dark-grey: #4a4a4a;
    --light-white: #ffffff;
    --text-color: #333333;
    --border-radius: 4px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition-speed: 0.3s;
    --header-height: 70px; /* Note: This might also be relevant for components, keep here for now */
}

/* Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    overflow-x: hidden;
    padding: 0; /* Remove padding for mobile-first */
    margin: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem; /* Mobile-first padding */
}

/* Responsive container adjustments */
@media (min-width: 576px) {
    .container {
        width: 95%;
        padding: 0 1.5rem;
    }
}

@media (min-width: 768px) {
    .container {
        width: 90%;
        padding: 0 2rem;
    }
}

@media (min-width: 992px) {
    .container {
        width: 90%;
        padding: 0 15px;
    }
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--dark-grey);
}

/* Mobile-first typography */
h1 {
    font-size: 1.8rem; /* Mobile first */
    font-weight: 900;
}

h2 {
    font-size: 1.6rem; /* Mobile first */
    margin-bottom: 1.5rem;
    text-align: center;
}

h3 {
    font-size: 1.3rem; /* Mobile first */
}

p {
    margin-bottom: 1.5rem;
    font-size: 1rem; /* Mobile first */
}

/* Responsive typography */
@media (min-width: 576px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.8rem; }
    h3 { font-size: 1.4rem; }
    p { font-size: 1.05rem; }
}

@media (min-width: 768px) {
    h1 { font-size: 2.2rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
    p { font-size: 1.1rem; }
}

@media (min-width: 992px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2.2rem; }
    h3 { font-size: 1.5rem; }
    p { font-size: 1.1rem; }
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--primary-blue-dark);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* --- Basic Utility Classes --- */

.highlight {
    color: var(--secondary-teal);
    font-weight: 700;
}

.section-tag {
    display: inline-block;
    background-color: var(--secondary-teal);
    color: white;
    padding: 5px 12px;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 168, 150, 0.3);
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    color: var(--dark-grey);
}

.text-center {
    text-align: center;
}

/* Content Sections Base - Mobile First */
.content-section {
    padding: 40px 0; /* Mobile first */
}

/* Responsive section padding */
@media (min-width: 576px) {
    .content-section {
        padding: 50px 0;
    }
}

@media (min-width: 768px) {
    .content-section {
        padding: 60px 0;
    }
}

@media (min-width: 992px) {
    .content-section {
        padding: 80px 0;
    }
}

.light-grey-bg {
    background-color: var(--light-grey);
}

/* --- Responsive Base Styles --- */

@media (max-width: 992px) {
    h1 {
        font-size: 2.2rem;
    }

    h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    :root {
        --header-height: 60px; /* Adjusting variable based on screen size */
    }

    .content-section {
        padding: 60px 0;
    }

    /* Note: nav ul, nav li styles from this breakpoint belong in components.css */
}

@media (max-width: 576px) {
    .container {
        width: 95%;
    }

    h1 {
       /* font-size: 1.8rem; */ /* Conflict with hero section styles, keeping in sections/components */
    }

    .section-tag {
        /* font-size: 0.8rem; */ /* Conflict with hero section styles, keeping in sections/components */
        /* padding: 6px 12px; */ /* Conflict with hero section styles, keeping in sections/components */
    }
}

/* Style for screen-reader only legend - Utility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Basic margin utilities found in About Page Styles */
.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

/* Lead text utility from About Page Styles */
.lead-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--dark-grey);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Highlight utility from About Page Styles (duplicate, but ensure it's here) */
.highlight { /* Already defined, but confirming */
    color: var(--primary-blue); /* Note: Overrides earlier definition. Decide which one is primary */
    font-weight: 700;
}


/* --- END OF FILE css/core.css --- */
/* Blog Styles */

/* Blog Hero Section */
.blog-hero {
    background-image: url('../images/blog-hero.jpg');
    background-size: cover;
    background-position: center;
    height: auto; /* Remove fixed height */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 4rem 0; /* Add padding to match about.html hero section */
}

.blog-hero h1 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.blog-hero p {
    color: white;
    font-size: 1rem;
    position: relative;
    z-index: 2;
    opacity: 0.9;
}

.blog-section {
    padding-top: 3rem;
}

.blog-section .highlight {
    color: var(--secondary-teal);
    font-weight: 700;
}

.lead-text {
    font-size: 1.2rem;
    color: var(--dark-grey);
    max-width: 800px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.blog-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px; /* Smaller gap for mobile */
    margin-top: 20px; /* Smaller margin for mobile */
    padding: 0 10px; /* Add padding for mobile */
}

/* Responsive blog categories */
@media (min-width: 576px) {
    .blog-categories {
        gap: 12px;
        margin-top: 25px;
    }
}

@media (min-width: 768px) {
    .blog-categories {
        gap: 15px;
        margin-top: 30px;
        padding: 0;
    }
}

.category-link {
    padding: 6px 16px; /* Smaller padding for mobile */
    border-radius: 50px;
    background-color: white;
    color: var(--dark-grey);
    font-weight: 600;
    font-size: 0.8rem; /* Smaller font for mobile */
    transition: all var(--transition-speed);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-width: 80px; /* Ensure minimum touch target */
    min-height: 44px; /* Ensure minimum touch target height */
}

/* Responsive category links */
@media (min-width: 576px) {
    .category-link {
        padding: 7px 18px;
        font-size: 0.85rem;
    }
}

@media (min-width: 768px) {
    .category-link {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
}

.category-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-blue);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: -1;
    opacity: 0.1;
    border-radius: 50px;
}

.category-link:hover::before {
    transform: translateY(0);
}

.post-count {
    margin-left: 6px;
    font-size: 0.8em;
    opacity: 0.7;
    font-weight: normal;
    transition: all var(--transition-speed);
}

.category-link:hover, .category-link.active {
    background-color: var(--primary-blue);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 80, 158, 0.2);
    transform: translateY(-2px);
}

.category-link.active {
    position: relative;
}

.category-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--primary-blue);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.category-link:hover .post-count,
.category-link.active .post-count {
    opacity: 0.9;
    color: white;
}

@keyframes pulse {
    0% {
        transform: translateX(-50%) scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.4;
    }
    100% {
        transform: translateX(-50%) scale(0.8);
        opacity: 0.8;
    }
}

/* NoScript Message */
.noscript-message {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    text-align: left;
}

.noscript-message p {
    margin-bottom: 15px;
    font-weight: 600;
}

.noscript-message ul {
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 0;
}

.noscript-message li {
    margin-bottom: 5px;
}

.noscript-message a {
    color: #856404;
    text-decoration: underline;
    font-weight: 600;
}

.noscript-message a:hover {
    text-decoration: none;
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .post-card,
    .category-link,
    .category-link::before,
    .category-link.active::after,
    .no-results-message,
    .filter-loading,
    .spinner {
        transition: none !important;
        animation: none !important;
    }

    .post-card:hover {
        transform: none !important;
    }

    .post-card:hover .post-image img {
        transform: none !important;
    }
}

.category-description {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--dark-grey);
    font-style: italic;
}

/* Category Filtering */
.post-card {
    transition: opacity 0.3s ease, transform var(--transition-speed);
}

/* Accessibility - Focus Styles */
.category-link:focus-visible {
    outline: 3px solid var(--primary-blue);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.3);
}

.post-card:focus-within {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); /* Using the default box-shadow instead of the blue one */
}

.post-card h3:focus,
.post-card a:focus {
    outline: none; /* Removed visible outline */
}

.reset-filter:focus-visible {
    outline: 3px solid var(--primary-blue);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.3);
}

/* Loading Indicator */
.filter-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 20px 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.filter-loading p {
    margin-top: 15px;
    color: var(--primary-blue);
    font-weight: 600;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 80, 158, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-blue);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.no-results-message {
    text-align: center;
    padding: 60px 40px;
    background-color: #f9f9f9;
    border-radius: 12px;
    margin: 30px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: opacity 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.no-results-message p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 20px;
}

.no-results-message::before {
    content: '\f002';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 3rem;
    color: var(--primary-blue);
    opacity: 0.2;
    display: block;
    margin-bottom: 20px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Featured Post */
.featured-post {
    padding: 60px 0;
    background-color: var(--light-grey);
    margin-top: 2rem;
    border-radius: 15px;
}

.featured-post h3 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.featured-post-card {
    display: flex;
    flex-direction: column; /* Mobile first - stack vertically */
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
}

/* Responsive featured post layout */
@media (min-width: 768px) {
    .featured-post-card {
        flex-direction: row; /* Side by side on tablets and up */
    }
}

.featured-post-image {
    flex: none; /* Mobile first - no flex constraints */
    position: relative;
    height: 200px; /* Fixed height for mobile */
}

.featured-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-tag {
    position: absolute;
    bottom: 10px; /* Smaller spacing for mobile */
    right: 10px;
    background-color: var(--secondary-teal);
    color: white;
    padding: 8px 12px; /* Smaller padding for mobile */
    border-radius: 50px;
    font-weight: 700;
    font-size: 0.8rem; /* Smaller font for mobile */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.featured-post-content {
    flex: 1; /* Take remaining space */
    padding: 20px; /* Smaller padding for mobile */
}

/* Responsive featured post adjustments */
@media (min-width: 768px) {
    .featured-post-image {
        flex: 0 0 50%;
        height: auto; /* Auto height for larger screens */
    }

    .featured-tag {
        bottom: 20px;
        right: 20px;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .featured-post-content {
        flex: 0 0 50%;
        padding: 40px;
    }
}

.featured-post-content h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    text-align: left;
    color: var(--primary-blue);
}

.post-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.post-category {
    color: var(--primary-blue);
    font-weight: 700;
}

.post-date {
    color: #777;
}

/* Blog Posts Grid */
.blog-posts {
    padding: 60px 0;
    margin: 4rem 0;
}

.blog-posts h3 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.posts-grid {
    display: grid;
    grid-template-columns: 1fr; /* Mobile first - single column */
    gap: 20px; /* Smaller gap for mobile */
    margin-bottom: 30px; /* Smaller margin for mobile */
    margin-top: 1.5rem;
}

/* Responsive grid for blog posts */
@media (min-width: 576px) {
    .posts-grid {
        gap: 25px;
        margin-bottom: 40px;
    }
}

@media (min-width: 768px) {
    .posts-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns for tablets */
        gap: 25px;
        margin-bottom: 45px;
    }
}

@media (min-width: 992px) {
    .posts-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 columns for desktop */
        gap: 30px;
        margin-bottom: 50px;
        margin-top: 2rem;
    }
}

.post-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all var(--transition-speed);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    cursor: pointer;
    border-bottom: 3px solid transparent;
}

.post-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 80, 158, 0.15);
    border-bottom: 3px solid var(--primary-blue);
}

.post-image {
    height: 200px;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.post-card:hover .post-image img {
    transform: scale(1.05);
}

.post-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.post-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
    line-height: 1.4;
    color: var(--primary-blue);
    transition: color var(--transition-speed);
}

.post-card:hover .post-content h3 {
    color: var(--primary-blue-dark);
}

.post-content p {
    color: #666;
    margin-bottom: 1.2rem;
    font-size: 1rem;
    flex-grow: 1;
}

.read-more {
    color: var(--primary-blue);
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
    margin-top: auto;
}

.read-more:hover {
    color: var(--primary-blue-dark);
    gap: 12px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.page-link, .current-page {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.page-link {
    background-color: white;
    color: var(--dark-grey);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.page-link:hover {
    background-color: var(--primary-blue-light);
    color: white;
}

.current-page {
    background-color: var(--primary-blue);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 80, 158, 0.2);
}

.page-link.next {
    width: auto;
    padding: 0 20px;
    border-radius: 20px;
    gap: 8px;
}

.page-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    background-color: #f5f5f5;
}

/* CTA Section Override */
.blog-posts + .cta-section {
    margin-top: 0;
}

/* Newsletter Form in CTA Section */
.newsletter-form {
    max-width: 500px;
    margin: 2rem auto;
}

.newsletter-form .form-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 12px 20px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.newsletter-form input[type="email"]::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .btn {
    padding: 12px 25px;
}

.form-consent {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.form-consent input[type="checkbox"] {
    margin-top: 5px;
    accent-color: var(--secondary-teal);
}

/* Blog Post Page */
.blog-post-header {
    padding: 4rem 0;
    background-color: var(--light-grey);
    text-align: center;
}

.blog-post-title {
    font-size: 3rem;
    max-width: 900px;
    margin: 0 auto 1.5rem;
}

.blog-post-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.blog-post-image {
    max-width: 900px;
    margin: 0 auto 60px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-post-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px 60px;
}

.blog-post-content p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.blog-post-content h2 {
    font-size: 2rem;
    margin: 2.5rem 0 1.5rem;
    text-align: left;
}

.blog-post-content h3 {
    font-size: 1.5rem;
    margin: 2rem 0 1rem;
}

.blog-post-content ul, .blog-post-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.blog-post-content li {
    margin-bottom: 0.5rem;
}

.blog-post-content blockquote {
    border-left: 4px solid var(--primary-blue);
    padding: 20px;
    background-color: var(--light-grey);
    margin: 2rem 0;
    font-style: italic;
}

.blog-post-content img {
    max-width: 100%;
    border-radius: 8px;
    margin: 2rem auto;
}

.blog-post-content .callout {
    background-color: rgba(0, 80, 158, 0.05);
    border-radius: 8px;
    padding: 25px;
    margin: 2rem 0;
}

.blog-post-content .callout h4 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

/* Author Section */
.author-section {
    background-color: var(--light-grey);
    padding: 60px 0;
}

.author-card {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    display: flex;
    gap: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.author-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h3 {
    margin-bottom: 0.5rem;
}

.author-title {
    color: var(--primary-blue);
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
}

/* Related Posts */
.related-posts {
    padding: 60px 0;
}

.related-posts h2 {
    margin-bottom: 2rem;
}

.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .related-posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .featured-post-card {
        flex-direction: column;
    }

    .featured-post-image, .featured-post-content {
        flex: 0 0 100%;
    }

    .featured-post-image {
        height: 300px;
    }

    .newsletter-container {
        flex-direction: column;
        text-align: center;
    }

    .newsletter-content, .newsletter-form {
        flex: 0 0 100%;
    }

    .newsletter-content h2 {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .blog-header h1 {
        font-size: 2.5rem;
    }

    .blog-post-title {
        font-size: 2.2rem;
    }

    .featured-post-content {
        padding: 30px;
    }

    .featured-post-content h2 {
        font-size: 1.8rem;
    }

    .author-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .posts-grid, .related-posts-grid {
        grid-template-columns: 1fr;
    }

    .blog-header h1 {
        font-size: 2rem;
    }

    .blog-post-title {
        font-size: 1.8rem;
    }

    .blog-categories {
        flex-direction: column;
        gap: 10px;
    }

    .category-link {
        width: 100%;
        display: block;
    }

    .newsletter-form .form-group {
        flex-direction: column;
    }

    .newsletter-container {
        padding: 30px 20px;
    }
}

/* Form Field Improvements
   This file contains improvements to form fields across the site,
   particularly fixing the issue with icons inside input fields */

/* When input is focused or has content, fade out the icon */
.input-with-icon input:focus + i,
.input-with-icon input:not(:placeholder-shown) + i {
    opacity: 0;
    transition: opacity 0.2s ease;
}

/* Ensure the icon is above the input for proper stacking */
.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 18px; /* Slightly more space for mobile */
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-grey);
    font-size: 1.2rem; /* Slightly larger for mobile */
    transition: opacity 0.2s ease;
    pointer-events: none; /* Ensure the icon doesn't interfere with input interaction */
    z-index: 1;
}

/* Responsive icon adjustments */
@media (min-width: 768px) {
    .input-with-icon i {
        left: 15px;
        font-size: 1.1rem;
    }
}

.input-with-icon input {
    position: relative;
    z-index: 2;
    background-color: transparent;
}

/* Ensure password toggle button remains visible */
.toggle-password {
    z-index: 3;
}

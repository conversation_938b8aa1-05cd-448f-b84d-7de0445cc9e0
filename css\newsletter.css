/* Newsletter Styles */

/* Form styling improvements */
.newsletter-form .form-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 14px 20px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1.05rem;
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    min-width: 280px;
    transition: all 0.3s ease;
}

.newsletter-form input[type="email"]:focus {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.newsletter-form input[type="email"]::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

/* 3D Button Effect */
.newsletter-form .btn-3d {
    padding: 14px 25px;
    font-size: 1.05rem;
    font-weight: 600;
    position: relative;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 4px;
    box-shadow: 0 6px 0 #003a78, 0 8px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(0);
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.newsletter-form .btn-3d:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 0 #003a78, 0 10px 15px rgba(0, 0, 0, 0.25);
}

.newsletter-form .btn-3d:active {
    transform: translateY(4px);
    box-shadow: 0 2px 0 #003a78, 0 4px 6px rgba(0, 0, 0, 0.15);
}

/* Response message styling */
.newsletter-response {
    margin: 1.5rem auto;
    max-width: 600px;
}

.newsletter-response .alert {
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.newsletter-response .alert-success {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #00508e;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.newsletter-response .alert-danger {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #d9534f;
}

.newsletter-response .success-icon {
    text-align: center;
    font-size: 2.5rem;
    color: #0070c9;
    margin-bottom: 1rem;
}

.newsletter-response .success-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-align: center;
    color: #00508e;
}

.newsletter-response .success-text {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
    color: #333;
}

.newsletter-response .success-subtext {
    font-size: 0.95rem;
    color: #666;
    text-align: center;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.newsletter-response .success-note {
    font-size: 0.9rem;
    color: #777;
    text-align: center;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .newsletter-form .form-group {
        flex-direction: column;
        gap: 15px;
    }

    .newsletter-form input[type="email"] {
        width: 100%;
        min-width: 100%;
    }

    .newsletter-form .btn {
        width: 100%;
    }

    .newsletter-form .btn-3d {
        width: 100%;
        /* Maintain 3D effect on mobile */
        box-shadow: 0 6px 0 #003a78, 0 8px 10px rgba(0, 0, 0, 0.2);
    }

    .newsletter-response .success-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .newsletter-response .success-title {
        font-size: 1.25rem;
    }

    .newsletter-response .success-text {
        font-size: 1rem;
    }

    .form-consent {
        text-align: left;
    }
}

/* Animation for success message */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.newsletter-response .alert-success {
    animation: fadeInUp 0.5s ease-out;
}

/* Subtle highlight animation for the email input */
@keyframes gentlePulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(255, 255, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

.newsletter-form input[type="email"]:focus {
    animation: gentlePulse 2s infinite;
}

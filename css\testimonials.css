/* Testimonials Page Styles */

:root {
    --testimonial-primary: #00509e;
    --testimonial-secondary: #00a896;
    --testimonial-gradient: linear-gradient(135deg, var(--testimonial-primary), var(--testimonial-secondary));
    --testimonial-card-bg: #ffffff;
    --testimonial-card-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    --testimonial-card-radius: 12px;
    --testimonial-transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --testimonial-text-dark: #333333;
    --testimonial-text-medium: #555555;
    --testimonial-text-light: #777777;
}

/* Hero Section */
.testimonials-hero {
    position: relative;
    height: 50vh;
    min-height: 400px;
    max-height: 500px;
    display: flex;
    align-items: center;
    overflow: hidden;
    color: white;
}

.hero-canvas-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

#hero-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    transform: translateY(-10px);
}

.hero-content h1 {
    font-size: 3.2rem;
    font-weight: 900;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    line-height: 1.4;
}

.highlight {
    position: relative;
    display: inline-block;
}

/* White text for dark backgrounds (hero section) */
.testimonials-hero .highlight {
    color: #ffffff;
}

/* Dark text for light backgrounds (all other sections) */
section:not(.testimonials-hero):not(.cta-section) .highlight {
    color: var(--testimonial-primary);
}

/* White text for CTA section */
.cta-section .highlight {
    color: #ffffff;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background-color: rgba(0, 168, 150, 0.4);
    z-index: -1;
    transform: skewX(-15deg);
    transition: all 0.3s ease;
}

/* Different highlight colors for different sections */
.testimonials-hero .highlight::after {
    background-color: rgba(0, 168, 150, 0.4);
}

section:not(.testimonials-hero):not(.cta-section) .highlight::after {
    background-color: rgba(0, 168, 150, 0.3);
}

.cta-section .highlight::after {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Hover effect for highlights */
.highlight:hover::after {
    height: 12px;
    bottom: 3px;
    background-color: rgba(0, 168, 150, 0.6);
}

/* Introduction Section */
.intro-section {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.intro-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--testimonial-primary);
}

.lead-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #555;
}

.section-divider {
    position: relative;
    height: 2px;
    background-color: #e0e0e0;
    margin: 2rem auto;
    width: 80px;
}

.section-divider span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 0 15px;
    color: var(--testimonial-secondary);
}

.section-divider i {
    font-size: 1.5rem;
}

/* Featured Testimonials with 3D Cards */
.featured-testimonials {
    padding: 80px 0;
    background-color: white;
}

.testimonial-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card-3d {
    width: 350px;
    height: 450px;
    perspective: 1000px;
    margin-bottom: 30px;
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
    cursor: pointer;
}

.testimonial-card-3d:hover .card-inner {
    transform: rotateY(15deg);
}

.testimonial-card-3d.flipped .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: var(--testimonial-card-radius);
    overflow: hidden;
    box-shadow: var(--testimonial-card-shadow);
}

.card-front {
    background-color: var(--testimonial-card-bg);
    display: flex;
    flex-direction: column;
}

.card-back {
    background-color: var(--testimonial-primary);
    color: white;
    transform: rotateY(180deg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 30px;
}

.testimonial-image {
    height: 60%;
    overflow: hidden;
}

.testimonial-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.testimonial-card-3d:hover .testimonial-image img {
    transform: scale(1.05);
}

.testimonial-info {
    padding: 20px;
    text-align: center;
}

.testimonial-info h3 {
    margin: 0 0 5px;
    color: var(--testimonial-primary);
    font-size: 1.5rem;
}

.testimonial-condition {
    display: block;
    color: #777;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.rating {
    color: #ffc107;
    margin-bottom: 10px;
}

.flip-prompt {
    text-align: center;
    font-size: 0.8rem;
    color: #999;
    margin-top: auto;
    padding: 10px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.flip-prompt i {
    margin-left: 5px;
    font-size: 0.7rem;
}

.testimonial-card-3d:hover .flip-prompt {
    color: var(--testimonial-secondary);
}

.testimonial-content {
    font-size: 1rem;
    line-height: 1.6;
}

.testimonial-content p {
    margin-bottom: 20px;
}

.card-back .flip-prompt {
    color: rgba(255, 255, 255, 0.7);
}

.card-back .flip-prompt:hover {
    color: white;
}

/* Interactive Testimonial Gallery */
.testimonial-gallery {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.gallery-controls {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;
}

.gallery-filter {
    padding: 10px 20px;
    background-color: white;
    border: 2px solid #e0e0e0;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #555;
}

.gallery-filter:hover {
    border-color: var(--testimonial-secondary);
    color: var(--testimonial-secondary);
}

.gallery-filter.active {
    background-color: var(--testimonial-primary);
    border-color: var(--testimonial-primary);
    color: white;
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.gallery-item {
    background-color: white;
    border-radius: var(--testimonial-card-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--testimonial-transition);
    transform: translateY(0);
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.gallery-content {
    padding: 25px;
}

.testimonial-quote {
    position: relative;
    padding-left: 25px;
}

.testimonial-quote::before {
    content: '"';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 3rem;
    line-height: 1;
    color: var(--testimonial-secondary);
    opacity: 0.3;
}

.testimonial-quote p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 15px;
    color: #333;
}

.testimonial-author {
    display: flex;
    flex-direction: column;
    margin-top: 15px;
}

.testimonial-author .name {
    font-weight: 700;
    color: var(--testimonial-primary);
}

.testimonial-author .age {
    font-size: 0.9rem;
    color: #777;
}

/* 3D Testimonial Sphere */
.testimonial-sphere-section {
    padding: 80px 0;
    background-color: white;
    position: relative;
    overflow: hidden;
}

.sphere-container {
    position: relative;
    height: 450px;
    margin: 30px auto 100px;
    max-width: 800px;
    background: radial-gradient(circle, rgba(0,80,158,0.03) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
}

#testimonial-sphere {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
}

#testimonial-sphere::after {
    content: 'Click to explore';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 80, 158, 0.8);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 5;
}

#testimonial-sphere:hover::after {
    opacity: 1;
}

.sphere-overlay {
    position: absolute;
    bottom: -80px;
    left: 0;
    width: 100%;
    padding: 25px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: var(--testimonial-card-radius);
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 10;
}

.current-testimonial {
    text-align: center;
}

.current-testimonial .quote {
    font-size: 1.15rem;
    font-style: italic;
    color: var(--testimonial-text-dark);
    margin-bottom: 15px;
    line-height: 1.7;
    max-height: 150px;
    overflow-y: auto;
}

.current-testimonial .author {
    font-weight: 700;
    color: var(--testimonial-primary);
    position: relative;
    display: inline-block;
    padding-top: 10px;
}

.current-testimonial .author::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background-color: var(--testimonial-secondary);
}

/* Video Testimonials */
.video-testimonials {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.video-item {
    background-color: white;
    border-radius: var(--testimonial-card-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--testimonial-transition);
}

.video-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.video-item:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(0, 80, 158, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-thumbnail:hover .play-button {
    background-color: var(--testimonial-secondary);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-item h3 {
    padding: 20px 20px 10px;
    margin: 0;
    color: var(--testimonial-primary);
    font-size: 1.3rem;
}

.video-item p {
    padding: 0 20px 20px;
    margin: 0;
    color: #666;
    font-size: 0.95rem;
}

/* Video Modal */
.video-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    position: relative;
    width: 80%;
    max-width: 800px;
    background-color: white;
    border-radius: var(--testimonial-card-radius);
    overflow: hidden;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    z-index: 10;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Statistics Section */
.stats-section {
    padding: 80px 0;
    background-color: white;
}

.stats-section h2 {
    color: var(--testimonial-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.stat-item {
    text-align: center;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: var(--testimonial-card-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--testimonial-transition);
}

.stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--testimonial-secondary);
    margin-bottom: 20px;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
    background: linear-gradient(135deg, var(--testimonial-primary), var(--testimonial-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.stat-number::after {
    content: '%';
    font-size: 1.8rem;
    position: absolute;
    top: 5px;
    right: -25px;
    -webkit-text-fill-color: var(--testimonial-primary);
    color: var(--testimonial-primary);
    opacity: 0.8;
}

.stat-item:nth-child(2) .stat-number::after {
    content: '';
}

.stat-label {
    font-size: 1rem;
    color: #555;
    line-height: 1.5;
}

/* Call to Action Section */
.cta-section {
    padding: 80px 0;
    background: var(--testimonial-gradient);
    color: white;
}

.cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h3 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-3d {
    position: relative;
    background-color: white;
    color: var(--testimonial-primary);
    border: none;
    border-radius: 4px;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 6px 0 #003a78, 0 8px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(0);
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn-3d:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 0 #003a78, 0 10px 15px rgba(0, 0, 0, 0.25);
}

.btn-3d:active {
    transform: translateY(4px);
    box-shadow: 0 2px 0 #003a78, 0 4px 6px rgba(0, 0, 0, 0.15);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .testimonials-hero {
        height: 45vh;
        min-height: 350px;
    }

    .hero-content h1 {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .testimonial-card-3d {
        width: 320px;
        height: 420px;
    }

    .sphere-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .testimonials-hero {
        height: 40vh;
        min-height: 300px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .intro-content h2,
    .featured-testimonials h2,
    .testimonial-gallery h2,
    .testimonial-sphere-section h2,
    .video-testimonials h2,
    .stats-section h2 {
        font-size: 2rem;
    }

    .gallery-controls {
        flex-direction: column;
        align-items: center;
    }

    .gallery-filter {
        width: 100%;
        max-width: 300px;
    }

    .cta-content h3 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .btn-3d {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .testimonials-hero {
        height: 35vh;
        min-height: 250px;
    }

    .hero-content h1 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 0.3rem;
    }

    .testimonial-card-3d {
        width: 100%;
        max-width: 320px;
        height: 400px;
    }

    .sphere-container {
        height: 300px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }
}

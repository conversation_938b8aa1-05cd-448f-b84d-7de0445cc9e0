<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customized Multi-Vitamins & Functional Treatments - NP Labs</title>
    <meta name="description" content="Tailored vitamin and amino acid compounds for mitochondrial support, malabsorption issues, and various health conditions.">
    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles - Adapted */
        .custom-vitamins-hero { /* Updated class name */
            background: url('images/custom-vitamins-hero.jpg') no-repeat center center/cover; /* Placeholder Image */
            padding: 0rem 0; 
            text-align: center;
            position: relative;
        }
        .custom-vitamins-hero .page-header-overlay {
            background-color: rgba(0, 80, 158, 0.6); /* Dark blue overlay */
        }
        .custom-vitamins-hero h1,
        .custom-vitamins-hero p.lead-text {
            color: #ffffff !important; 
        }
        .content-section {
            padding: 60px 0;
        }
        .condition-list {
            list-style: none;
            padding-left: 0;
            column-count: 2; /* Display list in two columns on wider screens */
            column-gap: 40px;
            margin-top: 20px;
        }
        .condition-list li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
            break-inside: avoid-column; /* Prevent items breaking across columns */
        }
        .condition-list li::before {
            content: '\f058'; /* Font Awesome check-circle icon */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--secondary-color);
            position: absolute;
            left: 0;
            top: 1px;
        }
         .content-image {
             text-align: center;
             margin: 30px 0;
         }
        .content-image img {
            max-width: 60%; /* Adjust size as needed */
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        @media (max-width: 768px) {
            .condition-list {
                column-count: 1; /* Single column on smaller screens */
            }
             .content-image img {
                max-width: 90%; 
             }
        }

        /* --- Visual Appeal Enhancements --- */
        .content-block { /* Reused style */
             background-color: #f9f9f9;
             padding: 25px;
             border-radius: 8px;
             margin-bottom: 30px;
             border-left: 5px solid var(--secondary-color);
             box-shadow: 0 4px 8px rgba(0,0,0,0.08); /* Added consistent shadow */
         }

        /* Adjust existing list padding if needed within content-block */
        .content-block .condition-list {
             margin-top: 15px; /* Adjust top margin slightly */
        }
        /* --- End Visual Appeal Enhancements --- */

         /* Add CTA style */
        .vitamin-cta { /* NEW */
            background-color: var(--primary-color-light);
            color: var(--primary-color-dark);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 10px; /* Reduced top margin as it's after a content block */
            margin-bottom: 20px;
            border: 1px solid var(--primary-color);
        }
        .vitamin-cta strong {
             font-weight: 700;
        }

    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header custom-vitamins-hero"> <!-- Updated class -->
        <div class="page-header-overlay"></div>
        <div class="container position-relative">
            <h1 data-aos="fade-up">Customized Multi-Vitamins & Functional Treatments</h1> <!-- Updated Title -->
            <p class="lead-text" data-aos="fade-up" data-aos-delay="200">Targeted Nutrient Solutions for Optimal Health</p> <!-- Updated Subtitle -->
        </div>
    </section>

    <!-- Main Content Section -->
    <section class="content-section">
        <div class="container">
            <div class="content-block" data-aos="fade-up"> <!-- Added class -->
                 <h2>Tailored <span class="highlight">Vitamins and Amino Acids</span></h2>
                 <p>Our pharmacists and technicians have extensive knowledge of vitamins and amino acid compounds designed for mitochondrial support and malabsorption, or the inability to absorb certain vital nutrients via the gastrointestinal tract.</p>
                 <hr class="section-divider">
            </div>

             <div class="content-image" data-aos="fade-up" data-aos-delay="100">
                 <img src="images/custom-vitamins-main.jpg" alt="Customized Vitamins and Nutrients"> <!-- Optional Placeholder Image -->
             </div>

            <div class="content-block" data-aos="fade-up" data-aos-delay="200"> <!-- Added class -->
                <h3>Medical conditions related to this category of compounding include:</h3>
                <ul class="condition-list"> 
                    <li>Immunity | Cold | Flu</li>
                    <li>Autism</li>
                    <li>Brain Power / Memory</li>
                    <li>Depression / Mood Stabilization</li>
                    <li>Diabetes</li>
                    <li>Energy / Weight Loss</li>
                    <li>Erectile Dysfunction</li>
                    <li>Hair Growth</li>
                    <li>Hypertension</li>
                    <li>Insomnia / Anxiety</li>
                    <li>Metabolic Syndrome</li>
                    <li>Migraines</li>
                    <li>Neuropathy</li>
                    <li>Wound Care</li>
                </ul>
            </div>

            <!-- CTA Section -->
            <div class="vitamin-cta text-center" data-aos="fade-up"> <!-- Added CTA -->
                <p><strong>Ready to optimize your health with personalized nutrition? Contact NP Labs to explore our custom vitamin and functional treatment options.</strong></p>
            </div>

        </div> <!-- End Container -->
    </section> <!-- End Content Section -->

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000, 
        once: true, 
    });
</script>
<script src="js/include-html.js" defer></script>

</body>
</html>

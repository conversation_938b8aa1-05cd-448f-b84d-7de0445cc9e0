# NP Labs Blog Admin - User Guide

## Getting Started

Welcome to the NP Labs Blog Administration System! This guide will help you create, edit, and manage blog posts with ease.

## Accessing the Admin System

### Login
1. Navigate to `/admin/` on your website
2. Enter your admin password
3. Click "Sign In"

**Note**: Your session will remain active for 24 hours or until you log out.

## Dashboard Overview

After logging in, you'll see the main dashboard with:

- **Quick Actions**: Create new posts, upload media, manage existing posts
- **Statistics**: Total posts, featured posts, media files, and categories
- **Recent Posts**: Your latest blog articles with quick edit/view options
- **Category Distribution**: Posts organized by category
- **System Status**: Health check of the blog system

## Creating a New Blog Post

### Step 1: Basic Information

1. Click **"Create New Post"** from the dashboard
2. Enter a compelling **Post Title** (5-100 characters)
3. The **URL Slug** will auto-generate, but you can customize it
4. Select a **Category**:
   - **Health**: General health topics
   - **Wellness**: Lifestyle and wellness content
   - **Personalized Medicine**: Custom medication topics
   - **Research**: Scientific research and studies
5. Toggle **"Featured Post"** if you want this to appear prominently on the homepage

### Step 2: Featured Image

1. **Upload an Image**:
   - Drag and drop an image onto the upload area, or
   - Click to browse and select a file
   - Supported formats: JPG, PNG, WebP
   - Recommended size: 1200x630 pixels
   - Maximum file size: 5MB

2. **Add Alt Text** (Required):
   - Describe the image for accessibility
   - Keep it descriptive but concise (under 125 characters)
   - Example: "Pharmacist preparing personalized medication in modern lab"

### Step 3: Content Creation

Use the rich text editor to write your blog post:

#### Formatting Options
- **Headings**: Use H2-H6 for section headers
- **Text Formatting**: Bold, italic, underline, strikethrough
- **Lists**: Numbered and bulleted lists
- **Links**: Add internal and external links
- **Quotes**: Use blockquotes for emphasis
- **Code**: Insert code blocks for technical content

#### Writing Tips
- **Minimum Length**: 100 characters (aim for 300+ words for SEO)
- **Structure**: Use headings to organize content
- **Readability**: Write in clear, simple language
- **Engagement**: Include actionable advice and insights

### Step 4: SEO Settings (Optional)

Click to expand the SEO Settings section:

1. **Meta Description** (120-160 characters):
   - Brief summary that appears in search results
   - Include your main keyword
   - Make it compelling to encourage clicks

2. **Keywords**:
   - Add relevant keywords separated by commas
   - Focus on terms your audience searches for
   - Example: "compounding pharmacy, personalized medicine, custom medications"

### Step 5: Tags

Add relevant tags to help organize and categorize your content:
- Type a tag and press Enter or comma to add it
- Use existing tags when possible for consistency
- Examples: "pharmacy", "health tips", "medication safety"

### Step 6: Publishing

#### Save as Draft
- Click **"Save Draft"** to save your work without publishing
- Drafts are automatically saved every 30 seconds
- You can return to edit drafts anytime

#### Preview
- Click **"Preview"** to see how your post will look on the blog
- Check formatting, images, and overall appearance
- Make adjustments as needed

#### Publish
- Click **"Publish"** when your post is ready
- The post will immediately appear on your blog
- You'll be redirected to the posts management page

## Managing Existing Posts

### Viewing All Posts

1. Go to **"Posts"** from the navigation menu
2. See all your posts with:
   - Title and category
   - Publication date
   - Current status (Published/Draft)
   - Quick action buttons

### Editing Posts

1. Click **"Edit"** next to any post
2. Make your changes using the same interface as creating new posts
3. Click **"Update"** to save changes
4. Changes appear immediately on your blog

### Deleting Posts

1. Click **"Delete"** next to the post you want to remove
2. Confirm the deletion in the popup
3. **Warning**: This action cannot be undone

## Media Management

### Uploading Images

1. Go to **"Media"** from the navigation menu
2. Click **"Upload Files"** or drag and drop images
3. Images are automatically optimized for web use
4. Use uploaded images in your blog posts

### Image Best Practices

- **Size**: 1200x630 pixels for featured images
- **Format**: JPG for photos, PNG for graphics with transparency
- **File Size**: Keep under 1MB for faster loading
- **Alt Text**: Always include descriptive alt text
- **Naming**: Use descriptive filenames (e.g., "pharmacy-lab-equipment.jpg")

## Tips for Success

### Content Strategy

1. **Consistency**: Publish regularly (2-3 posts per week recommended)
2. **Value**: Provide helpful, actionable information
3. **Expertise**: Leverage your pharmacy and health knowledge
4. **Engagement**: Write in a conversational, approachable tone

### SEO Best Practices

1. **Keywords**: Research and use relevant health/pharmacy keywords
2. **Headlines**: Write compelling, keyword-rich titles
3. **Structure**: Use headings to organize content
4. **Links**: Include relevant internal and external links
5. **Images**: Optimize images with descriptive alt text

### Technical Tips

1. **Auto-Save**: Your work is automatically saved every 30 seconds
2. **Session**: You'll stay logged in for 24 hours
3. **Browser**: Use modern browsers (Chrome, Firefox, Safari, Edge)
4. **Mobile**: The admin interface works on tablets and phones
5. **Backup**: Your posts are automatically backed up

## Troubleshooting

### Common Issues

**Can't Log In**
- Check your password
- Clear browser cache and cookies
- Try a different browser

**Images Won't Upload**
- Check file size (must be under 5MB)
- Ensure file format is JPG, PNG, or WebP
- Try a different image

**Content Not Saving**
- Check your internet connection
- Try refreshing the page
- Contact your administrator

**Post Not Appearing on Blog**
- Ensure you clicked "Publish" (not just "Save Draft")
- Check if the post is set as featured (may appear in different section)
- Clear your browser cache

### Getting Help

If you encounter issues:

1. **Check System Status** on the dashboard
2. **Try Refreshing** the page
3. **Log Out and Back In** to reset your session
4. **Contact Support** if problems persist

## Security Notes

- **Never Share** your admin password
- **Log Out** when finished, especially on shared computers
- **Use Strong Passwords** with a mix of letters, numbers, and symbols
- **Keep Sessions Active** only when needed

## Best Practices Summary

✅ **Do:**
- Write engaging, helpful content
- Use descriptive titles and alt text
- Optimize images before uploading
- Preview posts before publishing
- Save drafts frequently
- Use consistent categorization

❌ **Don't:**
- Share admin credentials
- Upload copyrighted images without permission
- Publish without proofreading
- Use excessive keywords (keyword stuffing)
- Ignore SEO best practices
- Delete posts without backing up content

---

**Need Help?** Contact your website administrator or refer to the technical documentation for advanced features and troubleshooting.

# NP Labs Blog System

## Blog Architecture Overview

The NP Labs blog system is a static, file-based content management system designed for educational content about compounding pharmacy, personalized medicine, and health topics. It features automated post creation, category management, and responsive design.

## Directory Structure

```
blog/
├── index.html                     # Blog homepage
├── page-2.html                    # Pagination page
├── _header.html                   # Blog-specific header
├── _footer.html                   # Blog-specific footer
├── assets/
│   └── images/                    # Blog-specific images
├── posts/                         # Individual blog posts
│   ├── _header.html               # Post-specific header
│   ├── _footer.html               # Post-specific footer
│   ├── bioidentical-hormones.html
│   ├── future-of-personalized-medicine.html
│   ├── gut-brain-connection.html
│   ├── hormone-balance.html
│   ├── hormone-optimization.html
│   ├── pediatric-compounding.html
│   ├── peptide-therapies.html
│   ├── perimenopause-transition.html
│   ├── supplement-quality.html
│   ├── taurine-glycine-l-citrulline-aging-explorer.html
│   └── understanding-ldn-therapy.html
├── category-health.html           # Health category page
├── category-medicine.html         # Medicine category page
├── category-research.html         # Research category page
├── category-wellness.html         # Wellness category page
├── create-blog-post.js           # Automated post creation tool
├── blog-post-creation-guide.md   # Creation guide
├── blog-post-template-guide.md   # Template guide
└── blog-post-template.html       # Post template
```

## Blog Homepage Features

### Header Section
- Featured article spotlight
- Navigation breadcrumbs
- Search functionality (future enhancement)

### Category Filtering
```html
<div class="blog-filters">
    <button class="filter-btn active" data-filter="all">All Posts</button>
    <button class="filter-btn" data-filter="health">Health</button>
    <button class="filter-btn" data-filter="wellness">Wellness</button>
    <button class="filter-btn" data-filter="medicine">Personalized Medicine</button>
    <button class="filter-btn" data-filter="research">Research</button>
</div>
```

### Post Grid Layout
```html
<div class="posts-grid capabilities-grid">
    <article class="post-card" data-categories="research wellness">
        <a href="posts/post-slug.html" class="post-card-image-link">
            <img src="images/post-image.jpg" alt="Post title" class="post-card-image">
        </a>
        <div class="post-card-content">
            <div class="post-meta">
                <span class="post-category">Research</span>
                <span class="post-date">December 15, 2024</span>
            </div>
            <h3 class="post-title">
                <a href="posts/post-slug.html">Post Title</a>
            </h3>
            <p class="post-excerpt">Post excerpt...</p>
            <a href="posts/post-slug.html" class="read-more-link">Read More</a>
        </div>
    </article>
</div>
```

## Blog Post Structure

### Standard Post Template
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/blog/posts/post-slug.html">
    <title>Post Title - NP Labs Blog</title>
    <meta name="description" content="Post meta description">
    <meta name="keywords" content="relevant, keywords, for, post">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="Post Title - NP Labs Blog">
    <meta property="og:description" content="Post meta description">
    <meta property="og:image" content="https://www.nplabs.com/blog/assets/images/post-image.jpg">
    <meta property="og:url" content="https://www.nplabs.com/blog/posts/post-slug.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../core.css">
    <link rel="stylesheet" href="../../components.css">
    <link rel="stylesheet" href="../../sections.css">
    <link rel="stylesheet" href="../../footer.css">
    <link rel="stylesheet" href="../css/blog.css">
</head>
<body>
    <div id="header-placeholder"></div>
    
    <main id="main-content">
        <article class="blog-post">
            <!-- Post content -->
        </article>
    </main>
    
    <div id="footer-placeholder"></div>
    
    <!-- Scripts -->
    <script src="../../scripts.js"></script>
    <script src="../js/include-html.js" defer></script>
</body>
</html>
```

### Post Content Structure
```html
<article class="blog-post">
    <!-- Hero Section -->
    <header class="post-header">
        <div class="container">
            <div class="post-meta">
                <span class="post-category">Category</span>
                <span class="post-date">Publication Date</span>
                <span class="post-author">By Author Name</span>
            </div>
            <h1 class="post-title">Post Title</h1>
            <p class="post-excerpt">Post introduction/excerpt</p>
        </div>
    </header>
    
    <!-- Featured Image -->
    <div class="post-featured-image">
        <img src="../assets/images/post-image.jpg" alt="Post title">
    </div>
    
    <!-- Post Content -->
    <div class="post-content">
        <div class="container">
            <!-- Content sections -->
            <section class="content-section">
                <h2>Section Heading</h2>
                <p>Content paragraph...</p>
            </section>
        </div>
    </div>
    
    <!-- Call to Action -->
    <section class="post-cta">
        <div class="container">
            <h3>Ready to Explore Personalized Medicine?</h3>
            <p>Contact NP Labs today to learn more about our compounding services.</p>
            <a href="../../contact.html" class="btn btn-primary">Contact Us</a>
        </div>
    </section>
    
    <!-- Social Sharing -->
    <div class="post-sharing">
        <h4>Share this article:</h4>
        <div class="sharing-buttons">
            <a href="#" class="share-btn facebook">Facebook</a>
            <a href="#" class="share-btn twitter">Twitter</a>
            <a href="#" class="share-btn linkedin">LinkedIn</a>
        </div>
    </div>
</article>
```

## Automated Post Creation

### Blog Post Creation Tool (create-blog-post.js)
```javascript
const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

const BLOG_CONFIG = {
    paths: {
        postsDir: path.join(__dirname, 'posts'),
        blogIndex: path.join(__dirname, 'index.html'),
        template: path.join(__dirname, 'blog-post-template.html')
    },
    categories: ['health', 'wellness', 'medicine', 'research'],
    postsPerPage: 6
};

async function createBlogPost() {
    console.log('\n=== NP Labs Blog Post Creator ===\n');
    
    try {
        const postData = await collectPostInfo();
        await generateBlogPost(postData);
        await updateBlogIndex(postData);
        
        console.log('\n✅ Blog post created successfully!');
    } catch (error) {
        console.error('\n❌ Error creating blog post:', error.message);
    }
}

async function collectPostInfo() {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));
    
    const postData = {
        title: await question('Post title: '),
        slug: await question('URL slug (or press Enter for auto-generation): '),
        excerpt: await question('Post excerpt: '),
        category: await question(`Category (${BLOG_CONFIG.categories.join(', ')}): `),
        author: await question('Author name: '),
        keywords: await question('Keywords (comma-separated): '),
        imageName: await question('Featured image filename: ')
    };
    
    // Auto-generate slug if not provided
    if (!postData.slug) {
        postData.slug = postData.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '');
    }
    
    postData.filename = `${postData.slug}.html`;
    postData.date = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    rl.close();
    return postData;
}
```

### Template Processing
```javascript
async function generateBlogPost(postData) {
    try {
        const template = await fs.readFile(BLOG_CONFIG.paths.template, 'utf8');
        
        const processedContent = template
            .replace(/{{TITLE}}/g, postData.title)
            .replace(/{{SLUG}}/g, postData.slug)
            .replace(/{{EXCERPT}}/g, postData.excerpt)
            .replace(/{{CATEGORY}}/g, postData.category)
            .replace(/{{AUTHOR}}/g, postData.author)
            .replace(/{{DATE}}/g, postData.date)
            .replace(/{{KEYWORDS}}/g, postData.keywords)
            .replace(/{{IMAGE}}/g, postData.imageName);
        
        const outputPath = path.join(BLOG_CONFIG.paths.postsDir, postData.filename);
        await fs.writeFile(outputPath, processedContent, 'utf8');
        
        console.log(`✅ Blog post file created: ${outputPath}`);
    } catch (error) {
        throw new Error(`Failed to generate blog post: ${error.message}`);
    }
}
```

## Category Management

### Category Pages
Each category has a dedicated page that filters posts by category:

```html
<!-- category-health.html -->
<div class="category-header">
    <h1>Health Articles</h1>
    <p>Explore our latest insights on health and wellness topics.</p>
</div>

<div class="posts-grid" id="health-posts">
    <!-- Health category posts -->
</div>
```

### Category Filtering JavaScript
```javascript
// blog-filter.js
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const postCards = document.querySelectorAll('.post-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.dataset.filter;
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Filter posts
            postCards.forEach(card => {
                const categories = card.dataset.categories;
                
                if (filter === 'all' || categories.includes(filter)) {
                    card.style.display = 'block';
                    card.classList.add('fade-in');
                } else {
                    card.style.display = 'none';
                    card.classList.remove('fade-in');
                }
            });
        });
    });
});
```

## SEO Optimization

### Blog-Specific SEO
```html
<!-- Structured data for blog posts -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": "{{TITLE}}",
    "description": "{{EXCERPT}}",
    "author": {
        "@type": "Person",
        "name": "{{AUTHOR}}"
    },
    "datePublished": "{{DATE_ISO}}",
    "image": "https://www.nplabs.com/blog/assets/images/{{IMAGE}}",
    "publisher": {
        "@type": "Organization",
        "name": "NP Labs",
        "logo": "https://www.nplabs.com/nplabslogo.svg"
    }
}
</script>
```

### URL Structure
- Blog homepage: `/blog/`
- Blog posts: `/blog/posts/post-slug.html`
- Categories: `/blog/category-name.html`
- Pagination: `/blog/page-2.html`

## Content Guidelines

### Writing Standards
1. **Tone**: Professional yet accessible
2. **Length**: 800-1500 words per post
3. **Structure**: Clear headings, short paragraphs
4. **Keywords**: Natural integration, avoid stuffing
5. **Links**: Internal links to relevant services/pages

### Image Requirements
- **Format**: JPG or WebP
- **Size**: 1200x630px for featured images
- **Alt text**: Descriptive and keyword-relevant
- **Compression**: Optimized for web

### Category Guidelines
- **Health**: General health topics, wellness advice
- **Wellness**: Lifestyle, nutrition, preventive care
- **Medicine**: Personalized medicine, compounding topics
- **Research**: Scientific studies, industry research

## Performance Optimization

### Image Optimization
```html
<!-- Responsive images for blog posts -->
<picture>
    <source media="(max-width: 576px)" 
            srcset="../assets/images/post-image-mobile.jpg">
    <source media="(max-width: 992px)" 
            srcset="../assets/images/post-image-tablet.jpg">
    <img src="../assets/images/post-image-desktop.jpg" 
         alt="Post title" 
         loading="lazy">
</picture>
```

### Lazy Loading
```javascript
// Intersection Observer for lazy loading
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
        }
    });
});

document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
});
```

## Future Enhancements

### Planned Features
1. **Search functionality**: Full-text search across posts
2. **Comment system**: User engagement and discussion
3. **Related posts**: Algorithm-based recommendations
4. **Newsletter integration**: Automatic post notifications
5. **Social sharing analytics**: Track sharing performance
6. **Content management**: Admin interface for post management

### Technical Improvements
1. **Static site generator**: Migration to Jekyll or Hugo
2. **Headless CMS**: Integration with Strapi or Contentful
3. **Build optimization**: Automated image processing
4. **CDN integration**: Global content delivery
5. **Analytics**: Detailed content performance tracking

This blog system provides a solid foundation for content marketing while maintaining the flexibility to evolve with future requirements and technological advances.

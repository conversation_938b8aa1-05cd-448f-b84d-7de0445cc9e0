# NP Labs Component Library

## Component Library Overview

The NP Labs component library provides a comprehensive set of reusable UI components designed for consistency, accessibility, and maintainability. All components follow the established design system and are optimized for performance and user experience.

## Button Components

### Primary Button
```html
<button class="btn btn-primary">Primary Action</button>
<a href="#" class="btn btn-primary">Primary Link</a>
```

```css
.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 80, 158, 0.3);
}
```

### Secondary Button
```html
<button class="btn btn-secondary">Secondary Action</button>
```

```css
.btn-secondary {
    background-color: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 10px 22px;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-blue);
    color: white;
}
```

### Button Sizes
```html
<!-- Small button -->
<button class="btn btn-primary btn-small">Small</button>

<!-- Large button -->
<button class="btn btn-primary btn-large">Large</button>

<!-- Block button -->
<button class="btn btn-primary btn-block">Full Width</button>
```

## Card Components

### Basic Card
```html
<div class="card">
    <div class="card-content">
        <h3 class="card-title">Card Title</h3>
        <p class="card-description">Card description content goes here.</p>
    </div>
</div>
```

```css
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 80, 158, 0.15);
}
```

### Service Card
```html
<article class="card service-card" data-aos="fade-up">
    <div class="service-icon">
        <i class="fas fa-flask"></i>
    </div>
    <div class="card-content">
        <h3 class="card-title">Service Title</h3>
        <p class="card-description">Service description and benefits.</p>
    </div>
    <div class="card-footer">
        <a href="#" class="btn btn-primary">Learn More</a>
    </div>
</article>
```

### Team Member Card
```html
<div class="card team-card">
    <div class="team-image">
        <img src="images/team/member.jpg" alt="Team Member Name">
    </div>
    <div class="team-info">
        <h3 class="team-name">Dr. John Doe</h3>
        <p class="team-title">Chief Pharmacist</p>
        <p class="team-description">Brief bio and expertise.</p>
    </div>
    <div class="team-social">
        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
    </div>
</div>
```

## Form Components

### Form Group
```html
<div class="form-group">
    <label for="field-id" class="form-label">Field Label *</label>
    <input type="text" id="field-id" name="field-name" class="form-input" required>
    <span class="error-message" id="field-error"></span>
</div>
```

```css
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-grey);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--medium-grey);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}
```

### Select Dropdown
```html
<div class="form-group">
    <label for="select-field" class="form-label">Select Option</label>
    <select id="select-field" name="select-name" class="form-select">
        <option value="">Choose an option</option>
        <option value="option1">Option 1</option>
        <option value="option2">Option 2</option>
    </select>
</div>
```

### Checkbox and Radio
```html
<!-- Checkbox -->
<div class="form-check">
    <input type="checkbox" id="checkbox-id" name="checkbox-name" class="form-check-input">
    <label for="checkbox-id" class="form-check-label">Checkbox Label</label>
</div>

<!-- Radio buttons -->
<div class="form-radio-group">
    <div class="form-check">
        <input type="radio" id="radio1" name="radio-group" value="option1" class="form-check-input">
        <label for="radio1" class="form-check-label">Option 1</label>
    </div>
    <div class="form-check">
        <input type="radio" id="radio2" name="radio-group" value="option2" class="form-check-input">
        <label for="radio2" class="form-check-label">Option 2</label>
    </div>
</div>
```

## Navigation Components

### Main Navigation
```html
<nav class="main-nav" id="main-nav">
    <div class="nav-container">
        <div class="nav-brand">
            <a href="index.html" class="nav-brand-link">
                <img src="nplabslogo.svg" alt="NP Labs" class="nav-logo">
            </a>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="index.html" class="nav-link">Home</a>
            </li>
            <li class="nav-item has-mega-menu">
                <a href="#" class="nav-link">Services <i class="fas fa-chevron-down"></i></a>
                <div class="mega-menu">
                    <!-- Mega menu content -->
                </div>
            </li>
        </ul>
        
        <div class="nav-actions">
            <a href="register.html" class="btn btn-primary">Register</a>
        </div>
        
        <button class="mobile-nav-toggle" id="mobile-nav-toggle" aria-label="Toggle navigation">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>
```

### Breadcrumb Navigation
```html
<nav class="breadcrumb" aria-label="Breadcrumb">
    <ol class="breadcrumb-list">
        <li class="breadcrumb-item">
            <a href="index.html">Home</a>
        </li>
        <li class="breadcrumb-item">
            <a href="our-services.html">Services</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            Men's Health
        </li>
    </ol>
</nav>
```

## Content Components

### Hero Section
```html
<section class="hero-section">
    <div class="hero-background">
        <img src="hero-background.jpg" alt="Healthcare background" class="hero-bg-img">
    </div>
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="hero-content">
            <span class="hero-badge">Personalized Medicine</span>
            <h1 class="hero-title">Hero Title</h1>
            <p class="hero-description">Hero description text.</p>
            <div class="hero-actions">
                <a href="#" class="btn btn-primary btn-large">Primary CTA</a>
                <a href="#" class="btn btn-secondary btn-large">Secondary CTA</a>
            </div>
        </div>
    </div>
</section>
```

### Content Section
```html
<section class="content-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Section Title</h2>
            <p class="section-subtitle">Section description or subtitle.</p>
        </div>
        <div class="section-content">
            <!-- Section content -->
        </div>
    </div>
</section>
```

### Stats Grid
```html
<div class="stats-grid">
    <div class="stat-item" data-aos="fade-up">
        <div class="stat-icon">
            <i class="fas fa-calendar-check"></i>
        </div>
        <h3 class="stat-number">19+</h3>
        <p class="stat-label">Years of Experience</p>
    </div>
    <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3 class="stat-number">10,000+</h3>
        <p class="stat-label">Satisfied Customers</p>
    </div>
</div>
```

## Blog Components

### Blog Post Card
```html
<article class="post-card" data-categories="health wellness">
    <a href="posts/post-slug.html" class="post-card-image-link">
        <img src="images/blog/post-image.jpg" alt="Post title" class="post-card-image">
    </a>
    <div class="post-card-content">
        <div class="post-meta">
            <span class="post-category">Health</span>
            <span class="post-date">December 15, 2024</span>
        </div>
        <h3 class="post-title">
            <a href="posts/post-slug.html">Blog Post Title</a>
        </h3>
        <p class="post-excerpt">Brief excerpt of the blog post content...</p>
        <a href="posts/post-slug.html" class="read-more-link">Read More</a>
    </div>
</article>
```

### Blog Filter Buttons
```html
<div class="blog-filters">
    <button class="filter-btn active" data-filter="all">All Posts</button>
    <button class="filter-btn" data-filter="health">Health</button>
    <button class="filter-btn" data-filter="wellness">Wellness</button>
    <button class="filter-btn" data-filter="medicine">Medicine</button>
    <button class="filter-btn" data-filter="research">Research</button>
</div>
```

## Utility Components

### Loading Spinner
```html
<div class="loading-spinner">
    <div class="spinner"></div>
    <span class="loading-text">Loading...</span>
</div>
```

```css
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-grey);
    border-top: 4px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

### Alert Messages
```html
<!-- Success alert -->
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <span>Success message here</span>
</div>

<!-- Error alert -->
<div class="alert alert-error">
    <i class="fas fa-exclamation-circle"></i>
    <span>Error message here</span>
</div>

<!-- Warning alert -->
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle"></i>
    <span>Warning message here</span>
</div>
```

### Modal Component
```html
<div class="modal" id="modal-id">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Modal Title</h3>
            <button class="modal-close" aria-label="Close modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>Modal content goes here.</p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" data-modal-close>Cancel</button>
            <button class="btn btn-primary">Confirm</button>
        </div>
    </div>
</div>
```

## Animation Components

### AOS (Animate On Scroll) Usage
```html
<!-- Fade animations -->
<div data-aos="fade-up">Fade up animation</div>
<div data-aos="fade-down">Fade down animation</div>
<div data-aos="fade-left">Fade left animation</div>
<div data-aos="fade-right">Fade right animation</div>

<!-- Zoom animations -->
<div data-aos="zoom-in">Zoom in animation</div>
<div data-aos="zoom-out">Zoom out animation</div>

<!-- With delays -->
<div data-aos="fade-up" data-aos-delay="100">Delayed animation</div>
<div data-aos="fade-up" data-aos-delay="200">More delayed animation</div>
```

## Component Usage Guidelines

### Accessibility Requirements
- All interactive elements must be keyboard accessible
- Proper ARIA labels and roles
- Sufficient color contrast ratios
- Screen reader compatible markup

### Performance Considerations
- Use CSS transforms for animations
- Implement lazy loading for images
- Minimize DOM manipulation
- Use event delegation for dynamic content

### Responsive Design
- Mobile-first approach
- Flexible grid systems
- Scalable typography
- Touch-friendly interactions

This component library ensures consistency across the NP Labs website while providing flexibility for future enhancements and maintaining high standards for accessibility and performance.

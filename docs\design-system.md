# NP Labs Design System

## Brand Identity

### Brand Values
- **Trust**: Professional, reliable, and credible
- **Innovation**: Cutting-edge personalized medicine
- **Care**: Patient-focused and compassionate
- **Quality**: Excellence in pharmaceutical compounding
- **Accessibility**: Inclusive and user-friendly

### Brand Personality
- Professional yet approachable
- Scientific but understandable
- Trustworthy and authoritative
- Modern and innovative
- Caring and supportive

## Color Palette

### Primary Colors
```css
:root {
    --primary-blue: #00509e;        /* Main brand color */
    --primary-blue-dark: #003d7a;   /* Hover states */
    --primary-blue-light: #1a68b0;  /* Light variations */
    --secondary-teal: #00a896;      /* Accent color */
    --secondary-teal-dark: #008577; /* Teal hover */
    --secondary-teal-light: #1ab8a8; /* Light teal */
}
```

### Neutral Colors
```css
:root {
    --light-white: #ffffff;         /* Pure white */
    --light-grey: #f5f7fa;         /* Background sections */
    --medium-grey: #e0e5ec;        /* Borders, dividers */
    --dark-grey: #4a4a4a;          /* Body text */
    --text-color: #333333;         /* Primary text */
}
```

### Semantic Colors
```css
:root {
    --success-green: #5cb85c;       /* Success states */
    --warning-yellow: #f0ad4e;      /* Warning states */
    --error-red: #d9534f;          /* Error states */
    --info-blue: #5bc0de;          /* Information */
}
```

### Color Usage Guidelines

#### Primary Blue (#00509e)
- **Use for**: Main CTAs, primary buttons, links, brand elements
- **Don't use for**: Large background areas, body text
- **Accessibility**: Meets WCAG AA contrast on white backgrounds

#### Secondary Teal (#00a896)
- **Use for**: Highlights, accents, secondary CTAs, icons
- **Don't use for**: Primary navigation, main headings
- **Accessibility**: Excellent contrast ratio for text

#### Neutral Grays
- **Light Grey**: Section backgrounds, subtle dividers
- **Medium Grey**: Borders, inactive states, subtle text
- **Dark Grey**: Body text, secondary headings

## Typography

### Font Family
```css
font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

### Font Weights
- **300**: Light (subtle text, captions)
- **400**: Regular (body text, descriptions)
- **700**: Bold (headings, emphasis)
- **900**: Black (hero titles, major headings)

### Typography Scale
```css
/* Heading Hierarchy */
h1 { font-size: 2.5rem; font-weight: 900; line-height: 1.2; }
h2 { font-size: 2.2rem; font-weight: 700; line-height: 1.2; }
h3 { font-size: 1.5rem; font-weight: 700; line-height: 1.3; }
h4 { font-size: 1.25rem; font-weight: 700; line-height: 1.4; }
h5 { font-size: 1.1rem; font-weight: 700; line-height: 1.4; }
h6 { font-size: 1rem; font-weight: 700; line-height: 1.4; }

/* Body Text */
p { font-size: 1.1rem; line-height: 1.6; font-weight: 400; }
.lead-text { font-size: 1.2rem; line-height: 1.8; }
.small-text { font-size: 0.9rem; line-height: 1.5; }
```

### Typography Usage

#### Headings
- **H1**: Page titles, hero sections (max one per page)
- **H2**: Section titles, main content divisions
- **H3**: Subsection titles, card headings
- **H4-H6**: Minor headings, component titles

#### Body Text
- **Regular (400)**: Standard body text, descriptions
- **Bold (700)**: Emphasis, important information
- **Light (300)**: Captions, subtle information

## Spacing System

### Base Unit
```css
:root {
    --spacing-unit: 1rem; /* 16px base */
}
```

### Spacing Scale
```css
/* Spacing Variables */
--spacing-xs: 0.25rem;  /* 4px */
--spacing-sm: 0.5rem;   /* 8px */
--spacing-md: 1rem;     /* 16px */
--spacing-lg: 1.5rem;   /* 24px */
--spacing-xl: 2rem;     /* 32px */
--spacing-xxl: 3rem;    /* 48px */
--spacing-xxxl: 4rem;   /* 64px */
```

### Layout Spacing
- **Section padding**: 80px top/bottom (desktop), 60px (mobile)
- **Container max-width**: 1200px
- **Container padding**: 15px left/right
- **Grid gaps**: 30px (desktop), 20px (mobile)

## Component Library

### Buttons

#### Primary Button
```css
.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    border: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
}
```

#### Secondary Button
```css
.btn-secondary {
    background-color: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 10px 22px;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-blue);
    color: white;
}
```

#### Button Sizes
```css
.btn-small { padding: 8px 16px; font-size: 0.9rem; }
.btn-large { padding: 16px 32px; font-size: 1.1rem; }
.btn-block { width: 100%; }
```

### Cards

#### Basic Card
```css
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 80, 158, 0.15);
}
```

#### Service Card
```css
.service-card {
    text-align: center;
    padding: 30px;
    position: relative;
}

.service-card i {
    font-size: 2.5em;
    color: var(--primary-blue);
    margin-bottom: 20px;
}

.service-card h4 {
    color: var(--secondary-teal);
    margin-bottom: 15px;
}
```

### Forms

#### Form Group
```css
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-grey);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--medium-grey);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
}
```

#### Form Validation States
```css
.form-group.error input {
    border-color: var(--error-red);
}

.form-group.success input {
    border-color: var(--success-green);
}

.error-message {
    color: var(--error-red);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}
```

### Navigation

#### Header Navigation
```css
.main-nav {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-link {
    color: var(--dark-grey);
    font-weight: 600;
    padding: 1rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-blue);
}
```

#### Mobile Navigation
```css
@media (max-width: 991px) {
    .main-nav {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .main-nav.active {
        transform: translateX(0);
    }
}
```

## Icons

### Icon Library
- **Font Awesome 6.4.0**: Primary icon library
- **Style**: Solid icons for most use cases
- **Size**: Consistent sizing using em units

### Icon Usage
```css
/* Standard icon sizes */
.icon-sm { font-size: 1rem; }
.icon-md { font-size: 1.5rem; }
.icon-lg { font-size: 2rem; }
.icon-xl { font-size: 2.5rem; }

/* Icon colors */
.icon-primary { color: var(--primary-blue); }
.icon-secondary { color: var(--secondary-teal); }
.icon-muted { color: var(--medium-grey); }
```

### Common Icons
- **Services**: `fas fa-flask`, `fas fa-pills`, `fas fa-user-md`
- **Features**: `fas fa-check-circle`, `fas fa-star`, `fas fa-shield-alt`
- **Navigation**: `fas fa-bars`, `fas fa-times`, `fas fa-chevron-down`
- **Social**: `fab fa-facebook`, `fab fa-twitter`, `fab fa-linkedin`

## Layout System

### Grid System
```css
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive grid */
@media (max-width: 768px) {
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}
```

### Container System
```css
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

.container-narrow {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 15px;
}
```

## Animation & Transitions

### Standard Transitions
```css
:root {
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}
```

### Hover Effects
```css
/* Button hover */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Card hover */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 80, 158, 0.15);
}
```

### AOS Animations
- **Fade effects**: `fade-up`, `fade-down`, `fade-left`, `fade-right`
- **Zoom effects**: `zoom-in`, `zoom-out`
- **Slide effects**: `slide-up`, `slide-down`

## Responsive Design

### Breakpoint System
```css
/* Mobile-first breakpoints */
@media (min-width: 576px) { /* Small tablets */ }
@media (min-width: 768px) { /* Large tablets */ }
@media (min-width: 992px) { /* Desktop */ }
@media (min-width: 1200px) { /* Large desktop */ }
```

### Responsive Typography
```css
/* Fluid typography */
h1 {
    font-size: clamp(2rem, 5vw, 2.5rem);
}

h2 {
    font-size: clamp(1.5rem, 4vw, 2.2rem);
}
```

This design system ensures consistency across the NP Labs website while maintaining flexibility for future enhancements and maintaining brand integrity.

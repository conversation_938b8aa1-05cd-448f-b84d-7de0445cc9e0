# NP Labs Development Guide

## Getting Started

### Prerequisites
- **Text Editor**: VS Code (recommended) or similar
- **Web Browser**: Chrome, Firefox, Safari, or Edge (latest versions)
- **Local Server**: Live Server extension or Python's http.server
- **Git**: Version control system
- **Node.js**: For development tools (optional)

### Project Setup

#### 1. <PERSON>lone the Repository
```bash
git clone [repository-url]
cd nplabs
```

#### 2. Local Development Server
**Option A: VS Code Live Server**
1. Install Live Server extension
2. Right-click on `index.html`
3. Select "Open with Live Server"

**Option B: Python HTTP Server**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Option C: Node.js HTTP Server**
```bash
npx http-server -p 8000
```

#### 3. Verify Setup
- Navigate to `http://localhost:8000`
- Verify all pages load correctly
- Test responsive design on different screen sizes
- Check browser console for errors

## Development Workflow

### File Organization

#### CSS Development
1. **Core styles**: Edit `core.css` for variables and base styles
2. **Components**: Modify `components.css` for reusable elements
3. **Sections**: Update `sections.css` for page-specific layouts
4. **Page-specific**: Create separate CSS files for unique pages

#### JavaScript Development
1. **Global functionality**: Edit `scripts.js`
2. **Modular features**: Create separate files in `/js/` directory
3. **Component-specific**: Add functionality to existing modules

#### HTML Development
1. **Page structure**: Follow existing HTML patterns
2. **Component usage**: Utilize header/footer includes
3. **Semantic markup**: Maintain accessibility standards

### Coding Standards

#### HTML Guidelines
```html
<!-- Use semantic HTML5 elements -->
<main id="main-content">
    <section class="content-section">
        <div class="container">
            <h2>Section Title</h2>
            <p>Content description</p>
        </div>
    </section>
</main>

<!-- Include proper meta tags -->
<meta name="description" content="Page description">
<meta name="keywords" content="relevant, keywords">

<!-- Use proper heading hierarchy -->
<h1>Page Title</h1>
<h2>Section Title</h2>
<h3>Subsection Title</h3>
```

#### CSS Guidelines
```css
/* Use CSS custom properties */
:root {
    --primary-color: #00509e;
    --secondary-color: #00a896;
}

/* Follow BEM-inspired naming */
.component-name {
    /* Block styles */
}

.component-name__element {
    /* Element styles */
}

.component-name--modifier {
    /* Modifier styles */
}

/* Mobile-first media queries */
.component {
    /* Mobile styles */
}

@media (min-width: 768px) {
    .component {
        /* Tablet styles */
    }
}

@media (min-width: 992px) {
    .component {
        /* Desktop styles */
    }
}
```

#### JavaScript Guidelines
```javascript
// Use modern ES6+ syntax
const initializeComponent = () => {
    // Component initialization
};

// Event delegation for dynamic content
document.addEventListener('click', (event) => {
    if (event.target.matches('.button-selector')) {
        // Handle button click
    }
});

// Error handling
try {
    // Risky operation
} catch (error) {
    console.error('Error:', error);
}

// Async/await for promises
const loadData = async () => {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Failed to load data:', error);
    }
};
```

### Component Development

#### Creating New Components

1. **HTML Structure**
```html
<!-- Component template -->
<div class="new-component">
    <div class="new-component__header">
        <h3 class="new-component__title">Title</h3>
    </div>
    <div class="new-component__content">
        <p class="new-component__description">Description</p>
    </div>
</div>
```

2. **CSS Styling**
```css
/* Add to components.css */
.new-component {
    background: var(--light-white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.new-component__title {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.new-component__description {
    color: var(--text-color);
    line-height: 1.6;
}
```

3. **JavaScript Functionality**
```javascript
// Add to appropriate JS file
const initializeNewComponent = () => {
    const components = document.querySelectorAll('.new-component');
    
    components.forEach(component => {
        // Add event listeners
        // Initialize functionality
    });
};

// Call on DOM ready
document.addEventListener('DOMContentLoaded', initializeNewComponent);
```

### Testing Procedures

#### Browser Testing
1. **Chrome DevTools**: Test responsive design
2. **Firefox Developer Tools**: Verify cross-browser compatibility
3. **Safari Web Inspector**: Test on macOS/iOS
4. **Edge DevTools**: Ensure Microsoft compatibility

#### Responsive Testing
```css
/* Test breakpoints */
320px  /* Small mobile */
576px  /* Large mobile */
768px  /* Tablet */
992px  /* Desktop */
1200px /* Large desktop */
```

#### Accessibility Testing
1. **Keyboard navigation**: Tab through all interactive elements
2. **Screen reader**: Test with NVDA, JAWS, or VoiceOver
3. **Color contrast**: Verify WCAG AA compliance
4. **Focus indicators**: Ensure visible focus states

#### Performance Testing
1. **Lighthouse audit**: Run in Chrome DevTools
2. **Page speed**: Test loading times
3. **Image optimization**: Verify proper formats and sizes
4. **JavaScript performance**: Check for memory leaks

### Blog Development

#### Creating New Blog Posts

1. **Use the automated tool**:
```bash
node blog/create-blog-post.js
```

2. **Manual creation**:
   - Copy `blog/blog-post-template.html`
   - Update metadata and content
   - Add to blog index manually

#### Blog Post Structure
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta tags -->
    <title>Post Title - NP Labs Blog</title>
    <meta name="description" content="Post description">
</head>
<body>
    <div id="header-placeholder"></div>
    <main>
        <article class="blog-post">
            <!-- Post content -->
        </article>
    </main>
    <div id="footer-placeholder"></div>
</body>
</html>
```

### Form Development

#### Form Structure
```html
<form class="contact-form" id="contact-form">
    <div class="form-group">
        <label for="name">Name *</label>
        <input type="text" id="name" name="name" required>
    </div>
    
    <div class="form-group">
        <label for="email">Email *</label>
        <input type="email" id="email" name="email" required>
    </div>
    
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

#### Form Validation
```javascript
const validateForm = (form) => {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showError(field, 'This field is required');
            isValid = false;
        }
    });
    
    return isValid;
};
```

### Deployment Process

#### Pre-deployment Checklist
- [ ] All pages load without errors
- [ ] Responsive design tested
- [ ] Forms function correctly
- [ ] Images optimized
- [ ] SEO meta tags updated
- [ ] Accessibility verified
- [ ] Performance optimized

#### Netlify Deployment
1. **Automatic deployment**: Push to main branch
2. **Manual deployment**: Drag and drop to Netlify
3. **Build settings**: Configure in `netlify.toml`

### Debugging Tips

#### Common Issues
1. **JavaScript errors**: Check browser console
2. **CSS not loading**: Verify file paths
3. **Images not displaying**: Check image paths and formats
4. **Forms not submitting**: Verify EmailJS configuration

#### Debug Tools
- **Browser DevTools**: Inspect elements and network
- **Console logging**: Add debug statements
- **Lighthouse**: Performance and accessibility audits
- **Wave**: Accessibility testing tool

### Best Practices

#### Performance
- Optimize images (WebP format when possible)
- Minimize HTTP requests
- Use CSS and JavaScript minification
- Implement lazy loading for images

#### SEO
- Use semantic HTML structure
- Include proper meta tags
- Optimize images with alt text
- Create descriptive URLs

#### Accessibility
- Provide keyboard navigation
- Use proper heading hierarchy
- Include ARIA labels where needed
- Ensure sufficient color contrast

#### Maintainability
- Follow consistent naming conventions
- Comment complex code sections
- Keep functions small and focused
- Document component usage

This development guide provides the foundation for maintaining and extending the NP Labs website while ensuring code quality and consistency.

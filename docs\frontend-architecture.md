# NP Labs Frontend Architecture

## Architecture Overview

The NP Labs frontend follows a modular, component-based architecture designed for maintainability, performance, and scalability. The system uses vanilla JavaScript with a progressive enhancement approach, ensuring functionality across all devices and browsers.

## HTML Architecture

### Document Structure
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta tags and SEO -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/page.html">
    <title>Page Title - NP Labs</title>
    <meta name="description" content="Page description">
    
    <!-- External resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    
    <!-- Stylesheets (order matters) -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
</head>
<body>
    <!-- Dynamic header injection -->
    <div id="header-placeholder"></div>
    
    <!-- Main content -->
    <main id="main-content">
        <!-- Page-specific content -->
    </main>
    
    <!-- Dynamic footer injection -->
    <div id="footer-placeholder"></div>
    
    <!-- Scripts (bottom loading) -->
    <script src="scripts.js"></script>
    <script src="js/include-html.js" defer></script>
</body>
</html>
```

### Semantic HTML Patterns

#### Section Structure
```html
<section class="content-section section-name">
    <div class="container">
        <h2>Section Title</h2>
        <p class="section-subtitle">Section description</p>
        
        <div class="content-grid">
            <!-- Section content -->
        </div>
    </div>
</section>
```

#### Card Components
```html
<article class="card service-card" data-aos="fade-up">
    <div class="card-icon">
        <i class="fas fa-icon-name"></i>
    </div>
    <div class="card-content">
        <h3 class="card-title">Card Title</h3>
        <p class="card-description">Card description</p>
    </div>
    <div class="card-footer">
        <a href="#" class="btn btn-primary">Learn More</a>
    </div>
</article>
```

#### Form Structure
```html
<form class="form-container" id="form-id">
    <fieldset class="form-section">
        <legend class="sr-only">Form Section</legend>
        
        <div class="form-group">
            <label for="field-id">Field Label *</label>
            <input type="text" id="field-id" name="field-name" required>
            <span class="error-message" id="field-error"></span>
        </div>
    </fieldset>
    
    <div class="form-actions">
        <button type="submit" class="btn btn-primary">Submit</button>
    </div>
</form>
```

## CSS Architecture

### Three-Layer System

#### Layer 1: Core (core.css)
```css
/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-blue: #00509e;
    --secondary-teal: #00a896;
    --light-grey: #f5f7fa;
    --text-color: #333333;
    
    /* Spacing */
    --spacing-unit: 1rem;
    --container-max-width: 1200px;
    
    /* Effects */
    --border-radius: 4px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition-speed: 0.3s;
}

/* Base element styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

/* Typography system */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}
```

#### Layer 2: Components (components.css)
```css
/* Button system */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
}

/* Card system */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    padding: 2rem;
    transition: all var(--transition-speed);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 80, 158, 0.15);
}
```

#### Layer 3: Sections (sections.css)
```css
/* Page-specific layouts */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, 
        rgba(0, 80, 158, 0.85) 0%, 
        rgba(0, 168, 150, 0.85) 100%);
}

.content-section {
    padding: 80px 0;
}

.content-section:nth-child(even) {
    background-color: var(--light-grey);
}

/* Grid layouts */
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}
```

### CSS Methodology

#### BEM-Inspired Naming
```css
/* Block */
.navigation { }

/* Element */
.navigation__item { }
.navigation__link { }

/* Modifier */
.navigation--mobile { }
.navigation__link--active { }
```

#### Responsive Design Strategy
```css
/* Mobile-first approach */
.component {
    /* Mobile styles (default) */
    font-size: 1rem;
    padding: 1rem;
}

@media (min-width: 768px) {
    .component {
        /* Tablet styles */
        font-size: 1.1rem;
        padding: 1.5rem;
    }
}

@media (min-width: 992px) {
    .component {
        /* Desktop styles */
        font-size: 1.2rem;
        padding: 2rem;
    }
}
```

## JavaScript Architecture

### Module System

#### Core Module (scripts.js)
```javascript
// Global utilities and initialization
document.addEventListener('DOMContentLoaded', () => {
    loadHeader();
    loadFooter();
    initializeGlobalFeatures();
});

// Header loading with callback
function loadHeader() {
    fetch('_header.html')
        .then(response => response.text())
        .then(data => {
            document.getElementById('header-placeholder').innerHTML = data;
            initializeNavigation();
        })
        .catch(error => console.error('Error loading header:', error));
}

// Global feature initialization
function initializeGlobalFeatures() {
    // AOS animation initialization
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    }
    
    // Global event listeners
    setupGlobalEventListeners();
}
```

#### Component Modules
```javascript
// navigation.js - Navigation functionality
function initializeNavigation() {
    const mobileToggle = document.getElementById('mobile-nav-toggle');
    const mainNav = document.getElementById('main-nav');
    
    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', () => {
            mainNav.classList.toggle('active');
            document.body.classList.toggle('no-scroll');
        });
    }
    
    // Mega menu functionality
    initializeMegaMenus();
}

// Form handling module
const FormHandler = {
    init() {
        this.bindEvents();
    },
    
    bindEvents() {
        document.addEventListener('submit', this.handleSubmit.bind(this));
    },
    
    handleSubmit(event) {
        const form = event.target;
        if (form.classList.contains('contact-form')) {
            event.preventDefault();
            this.processContactForm(form);
        }
    },
    
    processContactForm(form) {
        // Form processing logic
    }
};
```

### Event Management

#### Event Delegation Pattern
```javascript
// Efficient event handling for dynamic content
document.addEventListener('click', (event) => {
    // Button clicks
    if (event.target.matches('.btn-action')) {
        handleButtonClick(event);
    }
    
    // Card clicks
    if (event.target.closest('.service-card')) {
        handleCardClick(event);
    }
    
    // Navigation clicks
    if (event.target.matches('.nav-link')) {
        handleNavClick(event);
    }
});

// Form event delegation
document.addEventListener('input', (event) => {
    if (event.target.matches('input[required]')) {
        validateField(event.target);
    }
});
```

#### Custom Event System
```javascript
// Custom event creation and handling
const createCustomEvent = (name, data) => {
    return new CustomEvent(name, {
        detail: data,
        bubbles: true,
        cancelable: true
    });
};

// Event dispatching
const dispatchFormSubmit = (formData) => {
    const event = createCustomEvent('formSubmitted', formData);
    document.dispatchEvent(event);
};

// Event listening
document.addEventListener('formSubmitted', (event) => {
    console.log('Form submitted:', event.detail);
});
```

## Component System

### Dynamic Component Loading

#### Include System (include-html.js)
```javascript
document.addEventListener('DOMContentLoaded', function() {
    function loadHTML(elementId, filePath, callback) {
        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML = data;
                    
                    // Execute inline scripts
                    executeScripts(element);
                    
                    if (callback) callback();
                }
            })
            .catch(error => {
                console.error(`Error loading ${filePath}:`, error);
            });
    }
    
    // Load header and footer
    loadHTML('header-placeholder', '_header.html', initializeNavigation);
    loadHTML('footer-placeholder', '_footer.html');
});
```

### Component Templates

#### Header Component (_header.html)
```html
<header class="main-header">
    <nav class="main-nav" id="main-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html">
                    <img src="nplabslogo.svg" alt="NP Labs" class="nav-logo">
                </a>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item has-mega-menu">
                    <a href="#" class="nav-link">Services</a>
                    <div class="mega-menu">
                        <!-- Mega menu content -->
                    </div>
                </li>
            </ul>
            
            <div class="nav-actions">
                <a href="register.html" class="btn btn-primary">Register</a>
            </div>
            
            <button class="mobile-nav-toggle" id="mobile-nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>
</header>
```

## Performance Optimization

### Loading Strategy
```html
<!-- Critical CSS inline -->
<style>
    /* Critical above-the-fold styles */
</style>

<!-- Non-critical CSS -->
<link rel="preload" href="components.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

<!-- JavaScript loading -->
<script src="scripts.js"></script>
<script src="js/include-html.js" defer></script>
<script src="js/navigation.js" defer></script>
```

### Image Optimization
```html
<!-- Responsive images -->
<picture>
    <source media="(max-width: 576px)" srcset="image-small.jpg">
    <source media="(max-width: 992px)" srcset="image-medium.jpg">
    <img src="image-large.jpg" alt="Description" loading="lazy">
</picture>

<!-- WebP support -->
<picture>
    <source srcset="image.webp" type="image/webp">
    <img src="image.jpg" alt="Description">
</picture>
```

### Code Splitting
```javascript
// Lazy loading for non-critical features
const loadBlogFeatures = async () => {
    if (document.querySelector('.blog-container')) {
        const { BlogManager } = await import('./js/blog-manager.js');
        BlogManager.init();
    }
};

// Conditional loading
if ('IntersectionObserver' in window) {
    // Use modern features
} else {
    // Fallback for older browsers
}
```

This frontend architecture provides a solid foundation for the NP Labs website, ensuring maintainability, performance, and scalability while maintaining compatibility across all modern browsers and devices.

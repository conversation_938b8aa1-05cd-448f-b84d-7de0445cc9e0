# NP Labs Blog Admin System - Installation & Deployment Guide

## Overview

This guide covers installation and deployment of the NP Labs Blog Admin System across three hosting platforms:

1. **Netlify** (Recommended for static sites)
2. **Vercel** (Alternative serverless platform)
3. **Generic Unix VPS** (SiteGround, DigitalOcean, etc.)

## Prerequisites

- Node.js 16+ installed locally
- Git for version control
- Admin access to your hosting platform
- Basic command line knowledge

## Platform-Specific Deployment

### Option 1: Netlify Deployment (Recommended)

#### Step 1: Prepare Repository
```bash
# Clone or navigate to your project
cd nplabs

# Install dependencies (if developing locally)
npm install
```

#### Step 2: Netlify Setup
1. **Connect Repository**:
   - Log in to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository
   - Select the `nplabs` repository

2. **Configure Build Settings**:
   - Build command: `echo 'No build step required'`
   - Publish directory: `.` (root)
   - Functions directory: `netlify/functions`

3. **Environment Variables**:
   Go to Site Settings → Environment Variables and add:
   ```
   ADMIN_PASSWORD=your-secure-admin-password
   JWT_SECRET=your-jwt-secret-key-min-32-chars
   ```

#### Step 3: Deploy
1. Click "Deploy site"
2. Wait for deployment to complete
3. Access admin at: `https://your-site.netlify.app/admin/`

#### Step 4: Custom Domain (Optional)
1. Go to Site Settings → Domain management
2. Add your custom domain
3. Configure DNS records as instructed
4. Enable HTTPS (automatic with Netlify)

### Option 2: Vercel Deployment

#### Step 1: Vercel Setup
```bash
# Install Vercel CLI
npm install -g vercel

# Navigate to project
cd nplabs

# Login to Vercel
vercel login

# Deploy
vercel
```

#### Step 2: Configure Environment Variables
```bash
# Add environment variables
vercel env add ADMIN_PASSWORD
vercel env add JWT_SECRET

# Redeploy with new environment variables
vercel --prod
```

#### Step 3: Custom Domain (Optional)
```bash
# Add custom domain
vercel domains add yourdomain.com
```

### Option 3: Generic Unix VPS Deployment

#### Step 1: Server Preparation
```bash
# Update system (Ubuntu/Debian)
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx (optional, for reverse proxy)
sudo apt install nginx
```

#### Step 2: Deploy Application
```bash
# Clone repository
git clone https://github.com/MythGuru/nplabs.git
cd nplabs

# Install dependencies
npm install

# Create environment file
cat > .env << EOF
ADMIN_PASSWORD=your-secure-admin-password
JWT_SECRET=your-jwt-secret-key-min-32-chars
PORT=3000
NODE_ENV=production
EOF

# Set proper permissions
chmod 600 .env
```

#### Step 3: Start Application
```bash
# Start with PM2
pm2 start server.js --name "nplabs-admin"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

#### Step 4: Nginx Configuration (Optional)
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/nplabs

# Add configuration:
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Serve static files directly
    location / {
        root /path/to/nplabs;
        try_files $uri $uri/ =404;
    }
    
    # Proxy admin API requests
    location /api/admin/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Enable site
sudo ln -s /etc/nginx/sites-available/nplabs /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### Step 5: SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

## Configuration

### Environment Variables

All platforms require these environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `ADMIN_PASSWORD` | Admin login password | `MySecurePassword123!` |
| `JWT_SECRET` | JWT signing secret (32+ chars) | `your-super-secret-jwt-key-here` |
| `PORT` | Server port (VPS only) | `3000` |
| `NODE_ENV` | Environment mode | `production` |

### Security Configuration

#### Strong Password Requirements
- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, symbols
- Avoid common words or patterns
- Change regularly

#### JWT Secret Requirements
- Minimum 32 characters
- Use cryptographically secure random string
- Generate with: `node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"`

## Testing the Installation

### 1. Basic Functionality Test
```bash
# Test admin login
curl -X POST https://yourdomain.com/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"password":"your-admin-password"}'

# Should return: {"success":true,"token":"..."}
```

### 2. Admin Interface Test
1. Navigate to `https://yourdomain.com/admin/`
2. Log in with your admin password
3. Verify dashboard loads correctly
4. Test creating a new post
5. Verify post appears on blog

### 3. Blog Integration Test
1. Create a test post through admin
2. Check that it appears on `/blog/`
3. Verify category filtering works
4. Test responsive design on mobile

## Maintenance

### Regular Tasks

#### Daily
- Monitor system status on dashboard
- Check for any error notifications

#### Weekly
- Review blog analytics
- Update content as needed
- Check for security updates

#### Monthly
- Backup blog content
- Review and rotate passwords
- Update dependencies (if self-hosting)

### Backup Procedures

#### Netlify/Vercel
- Content is automatically backed up in Git repository
- Download site backup from hosting dashboard
- Export environment variables

#### VPS Hosting
```bash
# Backup blog content
tar -czf blog-backup-$(date +%Y%m%d).tar.gz blog/

# Backup database (if using)
# mysqldump -u user -p database > backup.sql

# Backup environment variables
cp .env .env.backup

# Store backups securely offsite
```

### Updates and Upgrades

#### For Git-based Deployments (Netlify/Vercel)
```bash
# Pull latest changes
git pull origin main

# Automatic deployment will trigger
```

#### For VPS Deployments
```bash
# Pull updates
git pull origin main

# Install new dependencies
npm install

# Restart application
pm2 restart nplabs-admin

# Check status
pm2 status
```

## Troubleshooting

### Common Issues

#### "Cannot connect to API"
- Check environment variables are set correctly
- Verify API endpoints are accessible
- Check network connectivity

#### "Invalid password" on login
- Verify `ADMIN_PASSWORD` environment variable
- Check for typos or special characters
- Try resetting password in hosting dashboard

#### Posts not appearing on blog
- Check file permissions (VPS hosting)
- Verify blog directory structure
- Check for JavaScript errors in browser console

#### Images not uploading
- Check file size limits
- Verify image directory permissions
- Ensure supported file formats (JPG, PNG, WebP)

### Log Analysis

#### Netlify
- View function logs in Netlify dashboard
- Check build logs for deployment issues

#### Vercel
- View function logs in Vercel dashboard
- Check deployment logs

#### VPS
```bash
# View application logs
pm2 logs nplabs-admin

# View Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# View system logs
sudo journalctl -u nginx
```

### Performance Optimization

#### Image Optimization
- Enable automatic image compression
- Use WebP format when possible
- Implement lazy loading

#### Caching
- Configure browser caching headers
- Use CDN for static assets
- Enable gzip compression

#### Monitoring
- Set up uptime monitoring
- Monitor page load speeds
- Track error rates

## Security Best Practices

### Access Control
- Use strong, unique passwords
- Enable two-factor authentication (if available)
- Limit admin access to necessary personnel
- Regular password rotation

### Server Security (VPS)
- Keep system updated
- Configure firewall (UFW/iptables)
- Disable unnecessary services
- Regular security audits

### Application Security
- Keep dependencies updated
- Monitor for security vulnerabilities
- Use HTTPS everywhere
- Implement rate limiting

## Support and Resources

### Documentation
- [User Guide](admin-user-guide.md)
- [Technical Specifications](blog-admin-implementation-plan.md)
- [Testing Checklist](blog-admin-testing-checklist.md)

### Community
- GitHub Issues: Report bugs and feature requests
- Documentation: Contribute improvements

### Professional Support
- Contact your web development team
- Consider managed hosting for critical applications

---

**Next Steps**: After successful installation, refer to the [User Guide](admin-user-guide.md) for content creation instructions.

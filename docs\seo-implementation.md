# NP Labs SEO Implementation Guide

## SEO Strategy Overview

The NP Labs website implements a comprehensive SEO strategy focused on ranking for personalized medicine, compounding pharmacy, and related healthcare keywords. The implementation follows modern SEO best practices with technical optimization, content strategy, and user experience considerations.

## Technical SEO Implementation

### Meta Tags Structure

#### Homepage Meta Tags
```html
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/">
    
    <!-- Primary Meta Tags -->
    <title>NP Labs | Personalized Compounded Medicine for Your Unique Needs</title>
    <meta name="description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Securely manage prescriptions online. Learn more & register today.">
    <meta name="keywords" content="compounding pharmacy, personalized medicine, custom medications, prescription compounding, NP Labs, Athens Greece">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="NP Labs | Personalized Compounded Medicine">
    <meta property="og:description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Learn more & register today.">
    <meta property="og:image" content="https://www.nplabs.com/images/og-image.jpg">
    <meta property="og:url" content="https://www.nplabs.com/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="NP Labs | Personalized Compounded Medicine">
    <meta name="twitter:description" content="NP Labs offers custom-compounded medications tailored to your specific health requirements. Learn more & register today.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/twitter-image.jpg">
</head>
```

#### Service Page Meta Tags Template
```html
<!-- Example: Men's Health Service Page -->
<title>Men's Health Compounding Services | Testosterone & ED Treatment - NP Labs</title>
<meta name="description" content="Specialized men's health compounding services including testosterone replacement therapy, ED treatments, and custom formulations. Expert care from NP Labs in Athens, Greece.">
<meta name="keywords" content="men's health compounding, testosterone replacement therapy, ED treatment, male hormone therapy, custom medications">
```

### URL Structure

#### Clean URL Pattern
```
https://www.nplabs.com/
https://www.nplabs.com/our-services
https://www.nplabs.com/mens-health
https://www.nplabs.com/womens-health
https://www.nplabs.com/blog/
https://www.nplabs.com/blog/posts/hormone-optimization
https://www.nplabs.com/register
https://www.nplabs.com/contact
```

#### URL Optimization Rules
- Remove .html extensions via Netlify redirects
- Use hyphens for word separation
- Keep URLs under 60 characters when possible
- Include primary keywords in URLs
- Maintain consistent structure across sections

### Structured Data Implementation

#### Organization Schema (seo/schema/organization.json)
```json
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "NP Labs",
    "alternateName": "NP Laboratories",
    "url": "https://www.nplabs.com",
    "logo": "https://www.nplabs.com/nplabslogo.svg",
    "description": "Leading compounding pharmacy specializing in personalized medicine solutions",
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "Athens",
        "addressCountry": "Greece"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+30-XXX-XXXXXXX",
        "contactType": "customer service",
        "availableLanguage": ["English", "Greek"]
    },
    "sameAs": [
        "https://www.facebook.com/nplabs",
        "https://www.linkedin.com/company/nplabs"
    ]
}
```

#### Local Business Schema (seo/schema/local-business.json)
```json
{
    "@context": "https://schema.org",
    "@type": "Pharmacy",
    "name": "NP Labs",
    "image": "https://www.nplabs.com/images/facility.jpg",
    "address": {
        "@type": "PostalAddress",
        "streetAddress": "Laboratory Address",
        "addressLocality": "Athens",
        "addressCountry": "Greece"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "37.9838",
        "longitude": "23.7275"
    },
    "openingHours": "Mo-Fr 09:00-17:00",
    "telephone": "+30-XXX-XXXXXXX",
    "priceRange": "$$",
    "servesCuisine": "Pharmaceutical Services"
}
```

#### Service Schema (seo/schema/service.json)
```json
{
    "@context": "https://schema.org",
    "@type": "MedicalBusiness",
    "name": "NP Labs Compounding Services",
    "description": "Personalized compounding pharmacy services",
    "provider": {
        "@type": "Organization",
        "name": "NP Labs"
    },
    "serviceType": "Pharmaceutical Compounding",
    "areaServed": {
        "@type": "Place",
        "name": "Worldwide"
    },
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Compounding Services",
        "itemListElement": [
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Men's Health Compounding"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Women's Health Compounding"
                }
            }
        ]
    }
}
```

### Sitemap Implementation (seo/sitemap.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://www.nplabs.com/</loc>
        <lastmod>2024-12-15</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://www.nplabs.com/our-services</loc>
        <lastmod>2024-12-15</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://www.nplabs.com/mens-health</loc>
        <lastmod>2024-12-15</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <!-- Additional URLs -->
</urlset>
```

### Robots.txt Configuration
```
User-agent: *
Allow: /

# Sitemap location
Sitemap: https://www.nplabs.com/seo/sitemap.xml

# Block admin areas (future)
Disallow: /admin/
Disallow: /private/

# Allow important resources
Allow: /css/
Allow: /js/
Allow: /images/
```

## Content SEO Strategy

### Keyword Research & Targeting

#### Primary Keywords
- **Compounding pharmacy** (High volume, high competition)
- **Personalized medicine** (Medium volume, medium competition)
- **Custom medications** (Medium volume, low competition)
- **Hormone therapy compounding** (Low volume, low competition)

#### Long-tail Keywords
- "compounding pharmacy Athens Greece"
- "custom hormone replacement therapy"
- "personalized medication dosing"
- "allergy-friendly medication compounding"
- "veterinary compounding services"

#### Service-Specific Keywords
```
Men's Health:
- testosterone replacement therapy compounding
- custom ED medication
- male hormone optimization

Women's Health:
- bioidentical hormone therapy
- menopause treatment compounding
- custom women's health solutions

Specialized Services:
- pediatric compounding pharmacy
- veterinary medication compounding
- pain management compounding
- dermatology compounding
```

### Content Optimization

#### Heading Structure
```html
<!-- Proper H1-H6 hierarchy -->
<h1>Main Page Title (Primary Keyword)</h1>
    <h2>Section Title (Secondary Keywords)</h2>
        <h3>Subsection (Long-tail Keywords)</h3>
            <h4>Detailed Points</h4>

<!-- Example from homepage -->
<h1>Medication Tailored to Your Unique Needs</h1>
    <h2>Why Settle for Standard When You Can Have Personalized?</h2>
        <h3>Tailored Dosages</h3>
        <h3>Allergy-Friendly Formulations</h3>
```

#### Internal Linking Strategy
```html
<!-- Strategic internal links -->
<p>Our <a href="mens-health.html">men's health compounding services</a> include 
specialized <a href="peptides.html">peptide therapies</a> and 
<a href="hormone-tracker.html">hormone optimization</a> solutions.</p>

<!-- Contextual service links -->
<p>Learn more about our <a href="our-facility.html">state-of-the-art facility</a> 
and <a href="quality-assurance.html">quality assurance processes</a>.</p>
```

### Image SEO

#### Image Optimization
```html
<!-- Descriptive filenames and alt text -->
<img src="images/compounding-pharmacy-athens-greece.jpg" 
     alt="NP Labs compounding pharmacy facility in Athens, Greece" 
     title="Professional compounding pharmacy equipment">

<!-- Responsive images with SEO benefits -->
<picture>
    <source media="(max-width: 576px)" 
            srcset="images/mens-health-compounding-mobile.jpg">
    <source media="(max-width: 992px)" 
            srcset="images/mens-health-compounding-tablet.jpg">
    <img src="images/mens-health-compounding-desktop.jpg" 
         alt="Men's health hormone therapy compounding services" 
         loading="lazy">
</picture>
```

#### Image File Naming Convention
```
Format: service-keyword-location-size.jpg
Examples:
- hormone-therapy-compounding-athens-desktop.jpg
- personalized-medicine-consultation-mobile.jpg
- custom-medication-preparation-tablet.jpg
```

## Blog SEO Implementation

### Blog Post SEO Template
```html
<!-- Blog post meta tags -->
<title>Understanding Low Dose Naltrexone Therapy - NP Labs Blog</title>
<meta name="description" content="Comprehensive guide to Low Dose Naltrexone (LDN) therapy, benefits, applications, and how compounding pharmacies customize LDN treatments.">
<meta name="keywords" content="low dose naltrexone, LDN therapy, compounding pharmacy, autoimmune treatment, chronic pain">

<!-- Article structured data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": "Understanding Low Dose Naltrexone Therapy",
    "description": "Comprehensive guide to LDN therapy and compounding",
    "author": {
        "@type": "Person",
        "name": "Dr. NP Labs Expert"
    },
    "datePublished": "2024-12-15",
    "dateModified": "2024-12-15",
    "image": "https://www.nplabs.com/blog/assets/images/ldn-therapy-guide.jpg",
    "publisher": {
        "@type": "Organization",
        "name": "NP Labs",
        "logo": "https://www.nplabs.com/nplabslogo.svg"
    },
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://www.nplabs.com/blog/posts/understanding-ldn-therapy"
    }
}
</script>
```

### Blog Category SEO
```html
<!-- Category page optimization -->
<title>Personalized Medicine Articles & Research - NP Labs Blog</title>
<meta name="description" content="Latest articles and research on personalized medicine, compounding pharmacy innovations, and custom healthcare solutions from NP Labs experts.">

<!-- Category-specific keywords -->
<meta name="keywords" content="personalized medicine research, compounding pharmacy articles, custom healthcare solutions, pharmaceutical innovation">
```

## Technical Performance SEO

### Core Web Vitals Optimization

#### Largest Contentful Paint (LCP)
```html
<!-- Preload critical resources -->
<link rel="preload" href="hero-background-large.jpg" as="image">
<link rel="preload" href="core.css" as="style">

<!-- Optimize hero images -->
<picture class="hero-background">
    <source media="(max-width: 992px)" srcset="hero-background-medium.jpg">
    <img src="hero-background-large.jpg" alt="Healthcare professional" class="hero-bg-img">
</picture>
```

#### First Input Delay (FID)
```javascript
// Optimize JavaScript loading
<script src="scripts.js" defer></script>
<script src="js/navigation.js" defer></script>

// Use event delegation for better performance
document.addEventListener('click', (event) => {
    if (event.target.matches('.service-card')) {
        // Handle click efficiently
    }
});
```

#### Cumulative Layout Shift (CLS)
```css
/* Reserve space for images */
.hero-bg-img {
    width: 100%;
    height: 600px;
    object-fit: cover;
}

/* Stable font loading */
@font-face {
    font-family: 'Lato';
    font-display: swap;
    src: url('fonts/lato.woff2') format('woff2');
}
```

### Mobile SEO Optimization

#### Mobile-First Indexing
```html
<!-- Mobile viewport optimization -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<!-- Mobile-friendly navigation -->
<nav class="main-nav" id="main-nav">
    <button class="mobile-nav-toggle" id="mobile-nav-toggle" aria-label="Toggle navigation">
        <span></span>
        <span></span>
        <span></span>
    </button>
</nav>
```

#### Touch-Friendly Design
```css
/* Minimum touch target size */
.btn, .nav-link, .service-card {
    min-height: 44px;
    min-width: 44px;
}

/* Improved mobile spacing */
@media (max-width: 768px) {
    .service-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
```

## Local SEO Implementation

### Google My Business Optimization
- Business name: "NP Labs - Compounding Pharmacy"
- Category: "Pharmacy" and "Medical Center"
- Description: Focus on personalized medicine and compounding services
- Photos: Facility, team, equipment, products
- Posts: Regular updates about services and health tips

### Local Citations
```html
<!-- Local business markup -->
<div itemscope itemtype="https://schema.org/Pharmacy">
    <span itemprop="name">NP Labs</span>
    <div itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
        <span itemprop="addressLocality">Athens</span>,
        <span itemprop="addressCountry">Greece</span>
    </div>
    <span itemprop="telephone">+30-XXX-XXXXXXX</span>
</div>
```

## SEO Monitoring & Analytics

### Key Metrics to Track
1. **Organic traffic growth**
2. **Keyword rankings** for target terms
3. **Core Web Vitals** scores
4. **Click-through rates** from search results
5. **Conversion rates** from organic traffic
6. **Local search visibility**

### SEO Tools Integration
- **Google Search Console**: Monitor search performance
- **Google Analytics 4**: Track user behavior and conversions
- **PageSpeed Insights**: Monitor Core Web Vitals
- **Lighthouse**: Comprehensive performance audits

### Regular SEO Tasks
1. **Monthly**: Keyword ranking reports
2. **Quarterly**: Content gap analysis
3. **Bi-annually**: Technical SEO audits
4. **Annually**: Comprehensive SEO strategy review

This SEO implementation provides a solid foundation for organic search visibility while maintaining flexibility for future optimization and algorithm updates.

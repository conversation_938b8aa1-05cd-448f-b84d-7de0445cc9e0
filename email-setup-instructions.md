# Contact Form Email Setup Instructions

This document provides instructions for setting up the contact form to send <NAME_EMAIL>.

## Option 1: PHP Server Setup (Already Implemented)

The contact form is already configured to send <NAME_EMAIL> using the PHP backend. The `contact-handler.php` file has been updated to:

1. Send all form <NAME_EMAIL>
2. CC <EMAIL> on all emails
3. Include detailed form information in the email body

**Note:** This requires a PHP server to be running. If you're hosting on a server with PHP support, this will work automatically.

## Option 2: EmailJS Setup (Client-Side Alternative)

For environments without PHP support (like static hosting), we've implemented EmailJS as a fallback. To complete the setup:

1. **Create an EmailJS Account**:
   - Go to [EmailJS.com](https://www.emailjs.com/) and sign up for a free account
   - The free tier allows 200 emails per month

2. **Create an Email Service**:
   - In your EmailJS dashboard, go to "Email Services"
   - Click "Add New Service" and connect your email provider (Gmail, Outlook, etc.)
   - Complete the authentication process

3. **Create an Email Template**:
   - Go to "Email Templates" in your dashboard
   - Click "Create New Template"
   - Design your template with the following variables:
     - `{{to_email}}` - Recipient email (<EMAIL>)
     - `{{cc_email}}` - CC email (<EMAIL>)
     - `{{from_name}}` - Sender's name
     - `{{from_email}}` - Sender's email
     - `{{phone}}` - Sender's phone
     - `{{user_type}}` - User type (Patient/Prescriber)
     - `{{subject}}` - Email subject
     - `{{message}}` - Message content

4. **EmailJS Configuration Status**:
   - ✅ The EmailJS configuration has been completed
   - ✅ Public key: `zV2H0z16RX_HcuWVV` has been added
   - ✅ Service ID: `service_o1sfm9l` has been added
   - ✅ Template ID: `template_vpxoh8d` has been added

Example template content:
```html
<h2>New Contact Form Submission from NP Labs Website</h2>
<p><strong>From:</strong> {{from_name}} ({{from_email}})</p>
<p><strong>Phone:</strong> {{phone}}</p>
<p><strong>User Type:</strong> {{user_type}}</p>
<p><strong>Subject:</strong> {{subject}}</p>
<h3>Message:</h3>
<p>{{message}}</p>
```

## How It Works

The contact form now has a dual submission approach:

1. It first attempts to submit the form using the PHP handler
2. If that fails (e.g., no PHP support), it automatically falls back to EmailJS
3. If both methods fail, it shows an error message

This ensures the form works in both server and static hosting environments.

/* Modern Footer Styles */
.site-footer {
    background-color: #f8f9fa;
    padding: 4rem 0 2rem;
    color: #495057;
    font-family: 'Lato', sans-serif;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-column {
    padding: 0 1rem;
}

.footer-brand-text {
    color: #00509e;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
}

.footer-column h4 {
    color: #00509e;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.footer-column p {
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: 0.75rem;
}

.footer-column ul li a {
    color: #495057;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: #00509e;
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icons a {
    color: #495057;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #00509e;
}

.contact-column i {
    margin-right: 0.5rem;
    color: #00509e;
}

.contact-column a {
    color: #495057;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-column a:hover {
    color: #00509e;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;
    font-size: 0.9rem;
    color: #6c757d;
}

.site-footer .footer-copyright,
.site-footer .footer-copyright p {
  font-size: 0.6875rem !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
}

@media (max-width: 768px) {
    .site-footer {
        padding: 3rem 0 1.5rem;
    }

    .footer-grid {
        gap: 1.5rem;
    }

    .footer-column {
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }
}

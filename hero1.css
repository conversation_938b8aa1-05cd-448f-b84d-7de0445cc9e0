/* Standard Page Hero Styles */
.page-hero {
    padding: 0;
    color: white;
    position: relative;
    overflow: hidden;
    background-position: center;
    background-size: cover;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

.page-hero .container {
    position: relative;
    z-index: 2;
    text-align: center;
}

.page-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
    line-height: 1.2;
}

.page-hero .lead-text {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.page-hero .breadcrumb {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0 0 1rem;
    font-size: 0.95rem;
}

.page-hero .breadcrumb li {
    display: flex;
    align-items: center;
}

.page-hero .breadcrumb li:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.page-hero .breadcrumb a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.page-hero .breadcrumb a:hover {
    color: white;
}

.page-hero .breadcrumb .current {
    color: rgba(255, 255, 255, 0.7);
}

/* Hero Background Variations */
.terms-hero {
    background-color: #00509e; /* Using primary blue color instead of image */
    padding: 0; /* Explicitly set padding to 0 for terms-hero */
}

.terms-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

/* Removed redundant .privacy-hero rule */
/*
.privacy-hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/banners/privacy-banner.jpg'); 
    color: white;
}
*/

/* Responsive Styles */
@media (max-width: 992px) {
    .page-hero {
        padding: 2rem 0;
    }

    .page-hero h1 {
        font-size: 2.5rem;
    }

    .page-hero .lead-text {
        font-size: 1.15rem;
    }
}

@media (max-width: 768px) {
    .page-hero {
        padding: 1.5rem 0;
    }

    .page-hero h1 {
        font-size: 2rem;
    }

    .page-hero .lead-text {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .page-hero h1 {
        font-size: 1.8rem;
    }

    .page-hero .lead-text {
        font-size: 1rem;
    }
}

/* Mobile-specific hero adjustments (≤576px) */
@media (max-width: 576px) {
  .hero-section .hero-image {
    object-position: 65% center !important;
  }
  .hero-section .hero-content .hero-text {
    font-size: clamp(1.25rem, 4vw, 1.75rem) !important;
    padding: 0 15px !important;
    background: rgba(0,80,158,0.7) !important;
    border-radius: 8px !important;
    margin: 0 auto !important;
    max-width: 90% !important;
  }
  .hero-section .hero-title {
    text-shadow: 1px 1px 3px rgba(0,0,0,0.3) !important;
  }
}

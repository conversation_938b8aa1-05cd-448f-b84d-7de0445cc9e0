<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Track your hormone levels and symptoms with NP Labs' interactive hormone tracking tool. Personalized insights for better hormone health.">
    <title>Hormone Tracker - Menstrual Cycle App</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fcf8fa;
            color: #333;
        }
        .phase-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        .phase-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        .phase-color {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            border-radius: 4px;
        }
        .btn {
            background-color: #e5e7eb;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn.active {
            background-color: #3b82f6;
            color: white;
        }
        .app-container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        .symptom-list li {
            margin-bottom: 8px;
            position: relative;
            padding-left: 24px;
        }
        .symptom-list li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #ec4899;
            font-weight: bold;
            font-size: 18px;
        }
        .phase-menstrual { background-color: #fecdd3; }
        .phase-follicular { background-color: #a5f3fc; }
        .phase-ovulatory { background-color: #bbf7d0; }
        .phase-luteal { background-color: #fef3c7; }
        .phase-premenstrual { background-color: #fbcfe8; }

        @media print {
            .app-container {
                width: 100%;
                margin: 0;
                padding: 10px;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-purple-800 mb-2">Hormone Levels During Menstrual Cycle</h1>
            <p class="text-gray-600">Track and understand your hormonal changes throughout your cycle</p>
        </header>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex justify-center mb-4 flex-wrap">
                <button id="showBothBtn" class="btn active mx-1 my-1">Show Both</button>
                <button id="show20YearBtn" class="btn mx-1 my-1">20-Year-Old Only</button>
                <button id="showPeriBtn" class="btn mx-1 my-1">Perimenopausal Only</button>
            </div>

            <div class="phase-legend">
                <div class="phase-item">
                    <div class="phase-color phase-menstrual"></div>
                    <span>Menstrual</span>
                </div>
                <div class="phase-item">
                    <div class="phase-color phase-follicular"></div>
                    <span>Follicular</span>
                </div>
                <div class="phase-item">
                    <div class="phase-color phase-ovulatory"></div>
                    <span>Ovulatory</span>
                </div>
                <div class="phase-item">
                    <div class="phase-color phase-luteal"></div>
                    <span>Luteal</span>
                </div>
                <div class="phase-item">
                    <div class="phase-color phase-premenstrual"></div>
                    <span>Premenstrual</span>
                </div>
            </div>

            <div class="relative" style="height: 400px;">
                <canvas id="hormoneChart"></canvas>
            </div>

            <div class="flex flex-wrap justify-center mt-4 text-sm">
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                    <span>Estradiol - 20yo (pg/mL)</span>
                </div>
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
                    <span>Progesterone - 20yo (ng/mL)</span>
                </div>
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                    <span>Testosterone - 20yo (ng/dL)</span>
                </div>
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-pink-400 mr-1"></div>
                    <span>Estradiol - 40s (pg/mL)</span>
                </div>
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-indigo-400 mr-1"></div>
                    <span>Progesterone - 40s (ng/mL)</span>
                </div>
                <div class="flex items-center mx-2 my-1">
                    <div class="w-3 h-3 rounded-full bg-green-300 mr-1"></div>
                    <span>Testosterone - 40s (ng/dL)</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-purple-800 mb-4">Key Differences in Perimenopausal Hormones</h2>
            <ul class="symptom-list">
                <li><strong>Shorter cycle length:</strong> Typically 21-26 days vs 28-30 days in younger women</li>
                <li><strong>Lower estradiol peaks:</strong> About 40-60% lower than in younger women</li>
                <li><strong>Reduced progesterone:</strong> Often insufficient luteal phase progesterone</li>
                <li><strong>Earlier follicular development:</strong> Follicular phase can be shortened</li>
                <li><strong>Testosterone decline:</strong> Gradual decline by 10-30% compared to peak reproductive years</li>
                <li><strong>More variable cycles:</strong> Increased cycle length variability and unpredictability</li>
            </ul>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-purple-800 mb-4">Common Perimenopausal Symptoms Due to Hormone Changes</h2>
            <ul class="symptom-list">
                <li><strong>Hot flashes and night sweats</strong> (due to estrogen fluctuations)</li>
                <li><strong>Sleep disturbances</strong> (due to progesterone insufficiency)</li>
                <li><strong>Mood changes</strong> (related to estrogen's effects on serotonin and dopamine)</li>
                <li><strong>Irregular periods</strong> (due to irregular ovulation)</li>
                <li><strong>Decreasing fertility</strong> (due to declining egg quality and quantity)</li>
                <li><strong>Changes in libido</strong> (related to both estrogen and testosterone levels)</li>
            </ul>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-purple-800 mb-4">Tracking Your Cycle</h2>
            <p class="mb-4">Understanding your personal hormone patterns can help you:</p>
            <ul class="symptom-list">
                <li>Predict your periods and fertility windows</li>
                <li>Understand mood changes throughout your cycle</li>
                <li>Manage symptoms by timing interventions with your cycle</li>
                <li>Recognize when your cycle patterns change as you age</li>
                <li>Have informed conversations with healthcare providers</li>
            </ul>
            <div class="mt-6 p-4 bg-purple-50 rounded-lg">
                <p class="text-sm text-gray-700">
                    <strong>Note:</strong> This visualization demonstrates typical hormone patterns. Individual cycles may vary.
                    Always consult with a healthcare provider about significant changes to your cycle or concerning symptoms.
                </p>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-purple-800 mb-4">How to Use This Tool</h2>
            <ol class="list-decimal pl-6 space-y-2">
                <li>Use the buttons at the top to toggle between viewing hormone levels for different age groups.</li>
                <li>Observe how hormone levels change throughout a typical 28-day cycle.</li>
                <li>Note the differences between hormone patterns in younger (20-year-old) and perimenopausal women.</li>
                <li>Refer to the symptom information to understand how hormonal changes may affect your body.</li>
                <li>Use this knowledge to track your own cycle patterns and identify any changes over time.</li>
            </ol>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Chart configuration
            const ctx = document.getElementById('hormoneChart').getContext('2d');

            // Data for the chart
            const days = Array.from({length: 28}, (_, i) => i + 1);

            // Hormone data
            const estradiol20 = [40, 30, 35, 40, 45, 50, 60, 90, 120, 150, 180, 220, 280, 330, 290, 240, 190, 150, 120, 100, 85, 75, 65, 55, 50, 45, 40, 40];
            const progesterone20 = [1, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.5, 2, 3, 5, 7.5, 10, 13, 16, 19, 22, 24, 25, 23, 18, 12, 7, 4, 2.5, 1.8, 1.2, 1];
            const testosterone20 = [0.5, 0.5, 0.6, 0.7, 0.8, 1, 1.2, 1.5, 1.8, 2, 2.3, 2.7, 3, 3.2, 3, 2.8, 2.6, 2.4, 2.2, 2, 1.8, 1.6, 1.4, 1.2, 1, 0.8, 0.6, 0.5];

            const estradiol40 = [25, 20, 22, 25, 30, 35, 45, 60, 80, 95, 110, 140, 170, 180, 160, 130, 110, 95, 85, 75, 65, 60, 55, 50, 45, 40, 35, 30];
            const progesterone40 = [0.5, 0.4, 0.5, 0.6, 0.7, 0.8, 1, 1.2, 1.5, 2, 3.5, 5, 6.5, 8, 9, 10, 11, 12, 13, 12, 9, 6, 4, 2.5, 1.5, 1, 0.7, 0.5];
            const testosterone40 = [0.3, 0.3, 0.4, 0.5, 0.6, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 2, 2.1, 2.2, 2.1, 2, 1.9, 1.8, 1.7, 1.6, 1.5, 1.3, 1.1, 0.9, 0.7, 0.5, 0.4, 0.3];

            // Create the chart
            let hormoneChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: days,
                    datasets: [
                        {
                            label: 'Estradiol - 20yo (pg/mL)',
                            data: estradiol20,
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 3,
                            yAxisID: 'y',
                            tension: 0.4,
                            hidden: false
                        },
                        {
                            label: 'Progesterone - 20yo (ng/mL)',
                            data: progesterone20,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            yAxisID: 'y1',
                            tension: 0.4,
                            hidden: false
                        },
                        {
                            label: 'Testosterone - 20yo (ng/dL)',
                            data: testosterone20,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 3,
                            yAxisID: 'y1',
                            tension: 0.4,
                            hidden: false
                        },
                        {
                            label: 'Estradiol - 40s (pg/mL)',
                            data: estradiol40,
                            borderColor: 'rgb(236, 72, 153)',
                            backgroundColor: 'rgba(236, 72, 153, 0.1)',
                            borderWidth: 3,
                            borderDash: [5, 5],
                            yAxisID: 'y',
                            tension: 0.4,
                            hidden: false
                        },
                        {
                            label: 'Progesterone - 40s (ng/mL)',
                            data: progesterone40,
                            borderColor: 'rgb(129, 140, 248)',
                            backgroundColor: 'rgba(129, 140, 248, 0.1)',
                            borderWidth: 3,
                            borderDash: [5, 5],
                            yAxisID: 'y1',
                            tension: 0.4,
                            hidden: false
                        },
                        {
                            label: 'Testosterone - 40s (ng/dL)',
                            data: testosterone40,
                            borderColor: 'rgb(110, 231, 183)',
                            backgroundColor: 'rgba(110, 231, 183, 0.1)',
                            borderWidth: 3,
                            borderDash: [5, 5],
                            yAxisID: 'y1',
                            tension: 0.4,
                            hidden: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            display: false,
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    return 'Day ' + tooltipItems[0].label;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Cycle',
                                font: {
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: true,
                                drawBorder: true,
                                drawOnChartArea: false,
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Estradiol (pg/mL)',
                                font: {
                                    weight: 'bold'
                                }
                            },
                            min: 0,
                            max: 350,
                            ticks: {
                                stepSize: 90
                            },
                            grid: {
                                color: function(context) {
                                    if (context.tick.value === 0) {
                                        return 'rgba(0, 0, 0, 0.3)';
                                    }
                                    return 'rgba(0, 0, 0, 0.1)';
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Progesterone (ng/mL) / Testosterone (ng/dL)',
                                font: {
                                    weight: 'bold'
                                }
                            },
                            min: 0,
                            max: 25,
                            grid: {
                                drawOnChartArea: false,
                            },
                            ticks: {
                                stepSize: 7
                            }
                        }
                    }
                }
            });

            // Add cycle phase background
            const originalDraw = hormoneChart.draw;
            hormoneChart.draw = function() {
                const chart = this;
                const ctx = chart.ctx;
                const chartArea = chart.chartArea;

                if (!chartArea) {
                    return;
                }

                // Phase ranges (approximate)
                const phases = [
                    { name: 'menstrual', start: 1, end: 5, color: 'rgba(254, 205, 211, 0.2)' },
                    { name: 'follicular', start: 6, end: 13, color: 'rgba(165, 243, 252, 0.2)' },
                    { name: 'ovulatory', start: 14, end: 15, color: 'rgba(187, 247, 208, 0.2)' },
                    { name: 'luteal', start: 16, end: 23, color: 'rgba(254, 243, 199, 0.2)' },
                    { name: 'premenstrual', start: 24, end: 28, color: 'rgba(251, 207, 232, 0.2)' }
                ];

                // Find positions on x-axis
                const xAxis = chart.scales.x;
                const yAxis = chart.scales.y;

                // Draw backgrounds
                phases.forEach(phase => {
                    const leftPixel = xAxis.getPixelForValue(phase.start - 0.5);
                    const rightPixel = xAxis.getPixelForValue(phase.end + 0.5);
                    const width = rightPixel - leftPixel;

                    ctx.fillStyle = phase.color;
                    ctx.fillRect(leftPixel, chartArea.top, width, chartArea.bottom - chartArea.top);
                });

                originalDraw.apply(this, arguments);
            };

            // Button handlers
            document.getElementById('showBothBtn').addEventListener('click', function() {
                toggleButtons(this);
                showBothAges();
            });

            document.getElementById('show20YearBtn').addEventListener('click', function() {
                toggleButtons(this);
                show20YearOnly();
            });

            document.getElementById('showPeriBtn').addEventListener('click', function() {
                toggleButtons(this);
                showPeriOnly();
            });

            function toggleButtons(activeBtn) {
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                activeBtn.classList.add('active');
            }

            function showBothAges() {
                hormoneChart.data.datasets.forEach(dataset => {
                    dataset.hidden = false;
                });
                hormoneChart.update();
            }

            function show20YearOnly() {
                hormoneChart.data.datasets.forEach(dataset => {
                    dataset.hidden = dataset.label.includes('40s');
                });
                hormoneChart.update();
            }

            function showPeriOnly() {
                hormoneChart.data.datasets.forEach(dataset => {
                    dataset.hidden = dataset.label.includes('20yo');
                });
                hormoneChart.update();
            }
        });
    </script>
</body>
</html>

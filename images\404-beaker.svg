<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="300px" viewBox="0 0 300 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>404 Empty Beaker</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="beakerGradient">
            <stop stop-color="#00A896" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#00509E" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Beaker Base -->
        <path d="M80,60 L80,240 C80,260 100,280 120,280 L180,280 C200,280 220,260 220,240 L220,60" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>
        
        <!-- Beaker Top -->
        <path d="M70,60 L230,60" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>
        
        <!-- Beaker Neck -->
        <path d="M100,60 L100,40 L200,40 L200,60" stroke="#00509E" stroke-width="4"></path>
        
        <!-- Beaker Rim -->
        <path d="M90,40 L210,40" stroke="#00509E" stroke-width="4" stroke-linecap="round"></path>
        
        <!-- Measurement Lines -->
        <path d="M90,100 L110,100" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
        <path d="M90,140 L110,140" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
        <path d="M90,180 L110,180" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
        <path d="M90,220 L110,220" stroke="#00509E" stroke-width="2" stroke-linecap="round"></path>
        
        <!-- Measurement Numbers -->
        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="104">100</text>
        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="144">200</text>
        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="184">300</text>
        <text font-family="Arial" font-size="12" fill="#00509E" x="75" y="224">400</text>
        
        <!-- Beaker Interior (Glass Effect) -->
        <path d="M85,65 L85,240 C85,257 98,275 120,275 L180,275 C202,275 215,257 215,240 L215,65 Z" fill="url(#beakerGradient)"></path>
        
        <!-- Bubbles -->
        <circle cx="120" cy="200" r="5" fill="#00A896" opacity="0.6">
            <animate attributeName="cy" from="200" to="100" dur="3s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.6" to="0" dur="3s" repeatCount="indefinite" />
        </circle>
        <circle cx="150" cy="220" r="3" fill="#00A896" opacity="0.5">
            <animate attributeName="cy" from="220" to="120" dur="4s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.5" to="0" dur="4s" repeatCount="indefinite" />
        </circle>
        <circle cx="180" cy="210" r="4" fill="#00A896" opacity="0.7">
            <animate attributeName="cy" from="210" to="110" dur="3.5s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.7" to="0" dur="3.5s" repeatCount="indefinite" />
        </circle>
        
        <!-- 404 Text (as if written on the beaker) -->
        <text font-family="Arial" font-size="24" font-weight="bold" fill="#00509E" opacity="0.3" x="130" y="170" text-anchor="middle">404</text>
        
        <!-- Question Marks floating out -->
        <text font-family="Arial" font-size="20" font-weight="bold" fill="#00509E" x="130" y="120" text-anchor="middle" opacity="0.4">?
            <animate attributeName="y" from="120" to="60" dur="5s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.4" to="0" dur="5s" repeatCount="indefinite" />
        </text>
        <text font-family="Arial" font-size="16" font-weight="bold" fill="#00A896" x="160" y="140" text-anchor="middle" opacity="0.3">?
            <animate attributeName="y" from="140" to="80" dur="4s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.3" to="0" dur="4s" repeatCount="indefinite" />
        </text>
        <text font-family="Arial" font-size="18" font-weight="bold" fill="#00509E" x="110" y="130" text-anchor="middle" opacity="0.5">?
            <animate attributeName="y" from="130" to="70" dur="4.5s" repeatCount="indefinite" />
            <animate attributeName="opacity" from="0.5" to="0" dur="4.5s" repeatCount="indefinite" />
        </text>
    </g>
</svg>

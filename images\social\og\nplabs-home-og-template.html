<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Home - Open Graph Image Template</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: '<PERSON><PERSON>', Arial, sans-serif;
        }
        .og-container {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #E6F3FF 0%, #FFFFFF 100%);
            position: relative;
            overflow: hidden;
        }
        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 150px;
            height: 50px;
            background-color: #003366;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .title-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 80%;
        }
        .title {
            font-size: 48px;
            font-weight: bold;
            color: #003366;
            margin-bottom: 20px;
        }
        .subtitle {
            font-size: 30px;
            color: #0066CC;
            margin-bottom: 30px;
        }
        .visual {
            position: absolute;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            width: 200px;
            height: 200px;
            background-color: #0066CC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .footer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50px;
            background-color: #003366;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .website {
            color: white;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="og-container">
        <div class="logo">NP LABS LOGO</div>
        <div class="title-area">
            <div class="title">NP Labs Compounding Pharmacy</div>
            <div class="subtitle">Personalized Medication Solutions in Athens, Greece</div>
        </div>
        <div class="visual">ICON</div>
        <div class="footer">
            <div class="website">www.nplabs.com</div>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Service - Open Graph Image Template</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: '<PERSON><PERSON>', Arial, sans-serif;
        }
        .og-container {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #E6F3FF 0%, #FFFFFF 100%);
            position: relative;
            overflow: hidden;
        }
        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 150px;
            height: 50px;
            background-color: #003366;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 10;
        }
        .service-image {
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background-color: #0066CC;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .content-area {
            position: absolute;
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            text-align: left;
            width: 45%;
        }
        .service-title {
            font-size: 48px;
            font-weight: bold;
            color: #003366;
            margin-bottom: 20px;
        }
        .service-description {
            font-size: 24px;
            color: #0066CC;
            margin-bottom: 30px;
        }
        .service-highlight {
            font-size: 20px;
            color: #003366;
            padding: 10px 20px;
            background-color: rgba(230, 243, 255, 0.7);
            border-left: 4px solid #0066CC;
        }
        .footer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50px;
            background-color: #003366;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .website {
            color: white;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="og-container">
        <div class="logo">NP LABS LOGO</div>
        <div class="service-image">SERVICE IMAGE</div>
        <div class="content-area">
            <div class="service-title">[SERVICE NAME]</div>
            <div class="service-description">Personalized compounding solutions for your specific needs</div>
            <div class="service-highlight">Expert pharmacists • Quality ingredients • Custom formulations</div>
        </div>
        <div class="footer">
            <div class="website">www.nplabs.com</div>
        </div>
    </div>
</body>
</html>

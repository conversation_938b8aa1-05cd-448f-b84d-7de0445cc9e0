<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NP Labs Home - Twitter Card Image Template</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: '<PERSON><PERSON>', Arial, sans-serif;
        }
        .twitter-container {
            width: 1200px;
            height: 600px;
            background: linear-gradient(135deg, #E6F3FF 0%, #FFFFFF 100%);
            position: relative;
            overflow: hidden;
        }
        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 120px;
            height: 40px;
            background-color: #003366;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .title-area {
            position: absolute;
            top: 50%;
            left: 40%;
            transform: translate(-50%, -50%);
            text-align: left;
            width: 60%;
        }
        .title {
            font-size: 42px;
            font-weight: bold;
            color: #003366;
            margin-bottom: 20px;
        }
        .subtitle {
            font-size: 28px;
            color: #0066CC;
            margin-bottom: 30px;
        }
        .visual {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 180px;
            height: 180px;
            background-color: #0066CC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .footer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background-color: #003366;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .website {
            color: white;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="twitter-container">
        <div class="logo">NP LABS LOGO</div>
        <div class="title-area">
            <div class="title">NP Labs Compounding Pharmacy</div>
            <div class="subtitle">Personalized Medication Solutions in Athens, Greece</div>
        </div>
        <div class="visual">ICON</div>
        <div class="footer">
            <div class="website">www.nplabs.com</div>
        </div>
    </div>
</body>
</html>

/**
 * Blog Card Click Handler
 * 
 * This script makes the entire blog post cards clickable, directing users
 * to the corresponding blog post page when clicking anywhere on the card.
 * It preserves normal link behavior for the "Read More" link.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all blog post cards
    const postCards = document.querySelectorAll('.post-card');
    
    // Add click event listener to each card
    postCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Find the "Read More" link within this card
            const readMoreLink = this.querySelector('.read-more');
            
            // Only navigate if the click wasn't on the "Read More" link itself
            // This prevents double-triggering when clicking directly on the link
            if (readMoreLink && !e.target.closest('.read-more')) {
                // Get the URL from the "Read More" link
                const postUrl = readMoreLink.getAttribute('href');
                
                // Navigate to the blog post
                if (postUrl) {
                    window.location.href = postUrl;
                }
            }
        });
        
        // Add keyboard accessibility
        card.addEventListener('keydown', function(e) {
            // Navigate on Enter or Space key
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const readMoreLink = this.querySelector('.read-more');
                if (readMoreLink) {
                    const postUrl = readMoreLink.getAttribute('href');
                    if (postUrl) {
                        window.location.href = postUrl;
                    }
                }
            }
        });
        
        // Make cards focusable for keyboard navigation
        card.setAttribute('tabindex', '0');
        
        // Add ARIA role for accessibility
        card.setAttribute('role', 'link');
        card.setAttribute('aria-label', 'Read blog post: ' + card.querySelector('h3').textContent.trim());
    });
});

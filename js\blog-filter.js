/**
 * Blog Category Filtering System
 *
 * This script provides dynamic filtering functionality for the blog posts
 * based on their categories without requiring page reloads.
 *
 * Features:
 * - Filter posts by category with smooth animations
 * - URL parameter support for shareable filtered views
 * - Keyboard accessibility and screen reader support
 * - Post counters for each category
 * - Loading indicators and visual feedback
 * - Error handling and fallback mechanisms
 * - Support for reduced motion preferences (via CSS)
 *
 * Accessibility features:
 * - ARIA attributes for interactive elements
 * - Focus management during filtering
 * - Screen reader announcements
 * - Keyboard navigation support
 * - Visible focus indicators
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all elements
    const categoryLinks = document.querySelectorAll('.category-link');
    const postCards = document.querySelectorAll('.post-card');
    const postsContainer = document.querySelector('.posts-grid');

    // Create no results message element
    const noResultsMessage = document.createElement('div');
    noResultsMessage.className = 'no-results-message';
    noResultsMessage.innerHTML = `
        <p>No posts found in this category.</p>
        <p>Please try another category or check back later for new content.</p>
        <button class="btn btn-primary reset-filter">Show All Posts</button>
    `;
    noResultsMessage.style.display = 'none';

    // Add event listener to the reset filter button
    noResultsMessage.querySelector('.reset-filter')?.addEventListener('click', function() {
        // Find and click the 'All Posts' category link
        const allPostsLink = document.querySelector('.category-link[data-category="all"]');
        if (allPostsLink) {
            allPostsLink.click();
        }
    });

    // Add no results message after the posts container
    if (postsContainer) {
        postsContainer.after(noResultsMessage);
    }

    /**
     * Count posts in each category and update the category links with post counts
     * This provides users with visual feedback about how many posts are in each category
     */
    function updateCategoryCounts() {
        // Hard-coded total counts based on all blog posts across all pages
        const totalCategoryCounts = {
            all: 10,      // Total number of blog posts
            health: 4,    // Posts in Health category
            wellness: 2,  // Posts in Wellness category
            medicine: 5,  // Posts in Personalized Medicine category
            research: 2   // Posts in Research category
        };

        // Update category links with the total counts
        categoryLinks.forEach(link => {
            const category = link.dataset.category;
            const count = totalCategoryCounts[category] || 0;

            // Create or update the post count span
            let countSpan = link.querySelector('.post-count');
            if (!countSpan) {
                countSpan = document.createElement('span');
                countSpan.className = 'post-count';
                link.appendChild(countSpan);
            }
            countSpan.textContent = `(${count})`;
        });

        // Store the counts in localStorage for other pages to use
        localStorage.setItem('blogCategoryCounts', JSON.stringify(totalCategoryCounts));
    }

    /**
     * Initialize the filtering system from URL parameters
     * This allows for shareable filtered views and proper browser history navigation
     * Includes error handling for invalid parameters and accessibility support
     */
    function initializeFromURL() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            let category = urlParams.get('category') || 'all';

            // Validate the category parameter
            const validCategories = ['all', 'health', 'wellness', 'medicine', 'research'];
            if (!validCategories.includes(category)) {
                console.warn(`Invalid category parameter: ${category}. Defaulting to 'all'.`);
                category = 'all';

                // Clean up the URL
                const url = new URL(window.location);
                url.searchParams.delete('category');
                history.replaceState({}, '', url);
            }

            // Set active class and ARIA states on the correct link
            categoryLinks.forEach(link => {
                if (link.dataset.category === category) {
                    link.classList.add('active');
                    link.setAttribute('aria-pressed', 'true');
                } else {
                    link.classList.remove('active');
                    link.setAttribute('aria-pressed', 'false');
                }
            });

            // Update the posts container ARIA busy state
            const postsContainer = document.getElementById('blog-posts-container');
            if (postsContainer) {
                postsContainer.setAttribute('aria-busy', 'true');
                // Reset aria-busy after filtering is complete
                setTimeout(() => {
                    postsContainer.setAttribute('aria-busy', 'false');
                }, 1000);
            }

            // Apply the filter
            filterPosts(category);
        } catch (error) {
            console.error('Error initializing from URL parameters:', error);
            // Fallback to showing all posts
            categoryLinks.forEach(link => {
                if (link.dataset.category === 'all') {
                    link.classList.add('active');
                    link.setAttribute('aria-pressed', 'true');
                } else {
                    link.classList.remove('active');
                    link.setAttribute('aria-pressed', 'false');
                }
            });
            filterPosts('all');
        }
    }

    /**
     * Filter posts based on selected category
     * Handles animations, focus management, URL updates, and accessibility announcements
     * Uses staggered animations for a more polished user experience
     *
     * @param {string} category - The category to filter by ('all' or a specific category name)
     */
    function filterPosts(category) {
        let visiblePosts = 0;
        let delay = 0;
        const staggerDelay = 100; // ms between each card animation

        // Get the featured article
        const featuredArticle = document.querySelector('.featured-post-card');
        const featuredSection = document.querySelector('.featured-post');

        // Handle featured article filtering
        if (featuredArticle) {
            const featuredCategories = featuredArticle.dataset.categories ? featuredArticle.dataset.categories.split(',') : [];

            if (category !== 'all' && !featuredCategories.includes(category)) {
                // Hide featured article with animation
                featuredArticle.style.opacity = '0';
                featuredArticle.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    if (featuredSection) featuredSection.style.display = 'none';
                }, 300);
            } else {
                // Show featured article
                if (featuredSection) featuredSection.style.display = 'block';
                featuredArticle.style.opacity = '0';
                featuredArticle.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    featuredArticle.style.opacity = '1';
                    featuredArticle.style.transform = 'translateY(0)';
                }, 50);

                visiblePosts++;
            }
        }

        // First hide all posts that don't match
        postCards.forEach(post => {
            const postCategories = post.dataset.categories ? post.dataset.categories.split(',') : [];

            if (category !== 'all' && !postCategories.includes(category)) {
                // Hide post with animation
                post.style.opacity = '0';
                post.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    post.style.display = 'none';
                }, 300);
            }
        });

        // Then show matching posts with staggered animation
        postCards.forEach(post => {
            const postCategories = post.dataset.categories ? post.dataset.categories.split(',') : [];

            if (category === 'all' || postCategories.includes(category)) {
                // Show post with staggered animation
                post.style.display = 'block';
                post.style.opacity = '0';
                post.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    post.style.opacity = '1';
                    post.style.transform = 'translateY(0)';
                }, 50 + delay);

                delay += staggerDelay;
                visiblePosts++;
            }
        });

        // Show/hide no results message with animation
        if (visiblePosts === 0 && postsContainer) {
            noResultsMessage.style.opacity = '0';
            noResultsMessage.style.display = 'block';
            setTimeout(() => {
                noResultsMessage.style.opacity = '1';
            }, 300);
        } else {
            noResultsMessage.style.opacity = '0';
            setTimeout(() => {
                noResultsMessage.style.display = 'none';
            }, 300);
        }

        // Update URL
        const url = new URL(window.location);
        if (category === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', category);
        }
        history.pushState({}, '', url);

        // Update announcement for screen readers
        const announcement = document.getElementById('filter-announcement');
        if (announcement) {
            if (category === 'all') {
                announcement.textContent = `Showing all posts. ${visiblePosts} posts found.`;
            } else {
                announcement.textContent = `Showing ${category} posts. ${visiblePosts} posts found.`;
            }
        }

        // Focus management - move focus to first visible post or no results message
        setTimeout(() => {
            if (visiblePosts > 0) {
                // Find the first visible post and focus on its heading or link
                const firstVisiblePost = Array.from(postCards).find(post =>
                    post.style.display !== 'none' &&
                    (category === 'all' || post.dataset.categories.split(',').includes(category)));

                if (firstVisiblePost) {
                    const focusTarget = firstVisiblePost.querySelector('h3') ||
                                       firstVisiblePost.querySelector('a') ||
                                       firstVisiblePost;

                    // Add tabindex if needed and focus
                    if (focusTarget.tagName.toLowerCase() !== 'a' &&
                        focusTarget.tagName.toLowerCase() !== 'button') {
                        focusTarget.setAttribute('tabindex', '-1');
                    }

                    focusTarget.focus({ preventScroll: true });
                }
            } else if (noResultsMessage.style.display !== 'none') {
                // Focus on the reset button in the no results message
                const resetButton = noResultsMessage.querySelector('.reset-filter');
                if (resetButton) {
                    resetButton.focus();
                }
            }
        }, 600); // Wait for animations to complete
    }

    // Create loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'filter-loading';
    loadingIndicator.innerHTML = '<div class="spinner"></div><p>Filtering posts...</p>';
    loadingIndicator.style.display = 'none';

    // Add loading indicator to the page
    if (postsContainer) {
        postsContainer.before(loadingIndicator);
    }

    // Add event listeners to category links
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Show loading indicator
            loadingIndicator.style.display = 'flex';
            loadingIndicator.style.opacity = '1';

            // Remove active class from all links and update ARIA states
            categoryLinks.forEach(l => {
                l.classList.remove('active');
                l.setAttribute('aria-pressed', 'false');
            });

            // Add active class to clicked link and update ARIA state
            this.classList.add('active');
            this.setAttribute('aria-pressed', 'true');

            // Get category from data attribute
            const category = this.dataset.category;

            // Slight delay to allow loading indicator to be visible
            setTimeout(() => {
                // Filter posts
                filterPosts(category);

                // Hide loading indicator after filtering
                setTimeout(() => {
                    loadingIndicator.style.opacity = '0';
                    setTimeout(() => {
                        loadingIndicator.style.display = 'none';
                    }, 300);
                }, 400);
            }, 300);
        });
    });

    // Initialize on page load
    // Always use the hard-coded counts for consistency
    updateCategoryCounts();
    initializeFromURL();

    // Handle browser back/forward navigation
    window.addEventListener('popstate', initializeFromURL);
});

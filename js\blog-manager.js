/**
 * NP Labs Blog Manager
 * 
 * This utility script automates blog post management tasks:
 * 1. Automatically updates category counts
 * 2. Manages post distribution across pages
 * 3. Maintains consistent date spacing
 * 4. Provides functions for adding new posts
 */

// Configuration
const BLOG_CONFIG = {
    postsPerPage: 6,
    featuredPostOnFirstPage: true,
    weeksBetweenPosts: 1,
    categories: {
        health: "Health",
        wellness: "Wellness",
        medicine: "Personalized Medicine",
        research: "Research"
    }
};

/**
 * Adds a new blog post to the index
 * @param {Object} postData - The blog post data
 * @param {string} postData.title - Post title
 * @param {string} postData.description - Post description
 * @param {string} postData.category - Post primary category
 * @param {string} postData.filename - HTML filename (without path)
 * @param {string} postData.imagePath - Path to post image
 * @param {string} postData.imageAlt - Alt text for image
 * @param {Array} postData.tags - Array of tags for the post
 * @param {boolean} postData.featured - Whether this should be the featured post
 */
function addNewBlogPost(postData) {
    console.log("Adding new blog post:", postData.title);
    
    // 1. Get current posts data
    const currentPosts = getAllBlogPosts();
    
    // 2. Add the new post (will be the most recent)
    const newPost = {
        title: postData.title,
        description: postData.description,
        category: postData.category,
        filename: postData.filename,
        imagePath: postData.imagePath || "",
        imageAlt: postData.imageAlt || postData.title,
        tags: postData.tags || [],
        featured: !!postData.featured,
        date: calculateNewPostDate(currentPosts)
    };
    
    // 3. If this is featured, un-feature the current featured post
    if (newPost.featured) {
        currentPosts.forEach(post => {
            if (post.featured) post.featured = false;
        });
    }
    
    // 4. Add the new post to the array
    currentPosts.unshift(newPost);
    
    // 5. Redistribute posts across pages
    redistributePosts(currentPosts);
    
    // 6. Update category counts
    updateCategoryCounts(currentPosts);
    
    console.log("Blog post added successfully!");
}

/**
 * Calculate the date for a new post (most recent date + 1 week)
 * @param {Array} currentPosts - Array of current posts
 * @returns {string} - Formatted date string
 */
function calculateNewPostDate(currentPosts) {
    // Find the most recent post date
    let mostRecentDate = new Date();
    
    if (currentPosts.length > 0) {
        // Try to find the most recent date
        currentPosts.forEach(post => {
            const postDate = new Date(post.date);
            if (!isNaN(postDate) && postDate > mostRecentDate) {
                mostRecentDate = postDate;
            }
        });
        
        // Add one week to the most recent date
        mostRecentDate.setDate(mostRecentDate.getDate() + (7 * BLOG_CONFIG.weeksBetweenPosts));
    }
    
    // Format the date as Month Day, Year
    return mostRecentDate.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

/**
 * Get all blog posts from all pages
 * @returns {Array} - Array of post objects
 */
function getAllBlogPosts() {
    const posts = [];
    
    // Get posts from the DOM
    // This is a simplified version - in production, you might want to use AJAX to load the pages
    const postCards = document.querySelectorAll('.post-card, .featured-post-card');
    
    postCards.forEach(card => {
        const isFeatured = card.classList.contains('featured-post-card');
        const title = isFeatured ? 
            card.querySelector('h2').textContent : 
            card.querySelector('h3').textContent;
        const description = card.querySelector('p').textContent.trim();
        const category = card.querySelector('.post-category').textContent;
        const dateStr = card.querySelector('.post-date').textContent;
        const link = isFeatured ? 
            card.querySelector('.btn').getAttribute('href') : 
            card.querySelector('.read-more').getAttribute('href');
        const filename = link.split('/').pop();
        
        // Get image info
        let imagePath = "";
        let imageAlt = "";
        const imgElement = card.querySelector('img');
        if (imgElement) {
            imagePath = imgElement.getAttribute('src');
            imageAlt = imgElement.getAttribute('alt');
        }
        
        // Get categories from data attribute
        const categories = card.dataset.categories ? 
            card.dataset.categories.split(',') : 
            [getCategoryKey(category)];
        
        posts.push({
            title,
            description,
            category,
            categories,
            date: dateStr,
            filename,
            imagePath,
            imageAlt,
            featured: isFeatured
        });
    });
    
    return posts;
}

/**
 * Get the category key from the display name
 * @param {string} categoryDisplay - The category display name
 * @returns {string} - The category key
 */
function getCategoryKey(categoryDisplay) {
    for (const [key, value] of Object.entries(BLOG_CONFIG.categories)) {
        if (value === categoryDisplay) return key;
    }
    return 'other';
}

/**
 * Redistribute posts across pages based on configuration
 * @param {Array} posts - Array of all posts
 */
function redistributePosts(posts) {
    console.log("Redistributing posts across pages...");
    
    // Calculate how many pages we need
    const postsPerPage = BLOG_CONFIG.postsPerPage;
    const firstPageCount = BLOG_CONFIG.featuredPostOnFirstPage ? postsPerPage + 1 : postsPerPage;
    const totalPosts = posts.length;
    
    // Calculate number of pages needed
    let pagesNeeded = 1;
    let remainingPosts = totalPosts - firstPageCount;
    
    while (remainingPosts > 0) {
        pagesNeeded++;
        remainingPosts -= postsPerPage;
    }
    
    console.log(`Need ${pagesNeeded} pages for ${totalPosts} posts`);
    
    // In a real implementation, this would update the actual HTML files
    // For now, we'll just log what would happen
    console.log("Page 1: Featured post + first " + (firstPageCount - 1) + " regular posts");
    
    for (let i = 2; i <= pagesNeeded; i++) {
        const startIndex = firstPageCount + (i - 2) * postsPerPage;
        const endIndex = Math.min(startIndex + postsPerPage, totalPosts);
        console.log(`Page ${i}: Posts ${startIndex} to ${endIndex - 1}`);
    }
}

/**
 * Update category counts based on all posts
 * @param {Array} posts - Array of all posts
 */
function updateCategoryCounts(posts) {
    console.log("Updating category counts...");
    
    // Count posts in each category
    const counts = {
        all: posts.length
    };
    
    // Initialize all category counts to 0
    Object.keys(BLOG_CONFIG.categories).forEach(cat => {
        counts[cat] = 0;
    });
    
    // Count posts in each category
    posts.forEach(post => {
        if (Array.isArray(post.categories)) {
            post.categories.forEach(cat => {
                if (counts[cat] !== undefined) {
                    counts[cat]++;
                }
            });
        } else {
            const cat = getCategoryKey(post.category);
            if (counts[cat] !== undefined) {
                counts[cat]++;
            }
        }
    });
    
    console.log("Category counts:", counts);
    
    // In a real implementation, this would update the HTML files
    // For now, we'll just log the counts
    Object.entries(counts).forEach(([category, count]) => {
        console.log(`${category}: ${count}`);
    });
}

/**
 * Generate HTML for a new blog post
 * @param {Object} postData - The blog post data
 * @returns {string} - HTML for the blog post
 */
function generateBlogPostHTML(postData) {
    // This would generate the complete HTML file for a new blog post
    // based on the template
    console.log("Generating HTML for new blog post:", postData.title);
    
    // In a real implementation, this would return the complete HTML
    return `<!-- Generated blog post HTML for ${postData.title} -->`;
}

// Export functions for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        addNewBlogPost,
        getAllBlogPosts,
        updateCategoryCounts,
        redistributePosts,
        generateBlogPostHTML
    };
}

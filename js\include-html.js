document.addEventListener('DOMContentLoaded', function() {

    function loadHTML(elementId, filePath, callback) {
        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} fetching ${filePath}`);
                }
                return response.text();
            })
            .then(data => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML = data;
                    // Execute inline scripts if necessary (simple approach)
                    // Note: This might not handle all script types or complex scenarios
                    Array.from(element.querySelectorAll("script")).forEach(oldScript => {
                        const newScript = document.createElement("script");
                        Array.from(oldScript.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value));
                        newScript.appendChild(document.createTextNode(oldScript.innerHTML));
                        oldScript.parentNode.replaceChild(newScript, oldScript);
                    });

                    if (callback) {
                        callback();
                    }
                } else {
                    console.error(`Element with ID '${elementId}' not found.`);
                }
            })
            .catch(error => {
                console.error(`Error loading HTML from ${filePath}:`, error);
                const element = document.getElementById(elementId);
                if (element) {
                     element.innerHTML = `<p>Error loading content from ${filePath}. Please check the file path and ensure the server is running correctly.</p>`;
                }
            });
    }

    // Load header first, then call the GLOBAL initializeNavigation if it exists
    loadHTML('header-placeholder', '_header.html', function() {
        if (typeof initializeNavigation === 'function') {
            console.log("DEBUG: Header loaded, calling global initializeNavigation()...");
            initializeNavigation();
        } else {
            console.error("DEBUG: Header loaded, but global initializeNavigation function not found!");
        }
    });

    // Load footer
    loadHTML('footer-placeholder', '_footer.html');

});

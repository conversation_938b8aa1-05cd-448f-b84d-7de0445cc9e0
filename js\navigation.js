function initializeNavigation() {
    // Wait for header to be fully loaded
    const headerPlaceholder = document.getElementById('header-placeholder');
    if (!headerPlaceholder || !headerPlaceholder.children.length) {
        setTimeout(initializeNavigation, 100); // Try again in 100ms
        return;
    }

    console.log("DEBUG: initializeNavigation() called.");

    // Find main navigation elements
    const mainNavToggle = document.getElementById('mobile-nav-toggle');
    const mainNav = document.getElementById('main-nav');
    const mainNavClose = document.getElementById('mobile-nav-close');
    const isMobile = window.innerWidth < 992;

    // Setup listeners for main mobile nav (hamburger menu)
    if (mainNavToggle && mainNav && mainNavClose) {
        console.log("DEBUG: Attaching main mobile nav listeners...");
        mainNavToggle.addEventListener('click', () => {
            console.log("Mobile nav toggle clicked!");
            mainNav.classList.add('active');
            document.body.style.overflow = 'hidden';
        });

        mainNavClose.addEventListener('click', () => {
            console.log("Mobile nav close clicked!");
            mainNav.classList.remove('active');
            document.body.style.overflow = '';
        });
    } else {
        console.error("Main mobile nav elements not found! Cannot attach listeners.");
    }

    // Mobile submenu toggle handlers
    if (isMobile) {
        console.log("DEBUG: Setting up mobile submenu handlers");

        // Function to toggle submenu
        function toggleSubmenu(item) {
            console.log("Toggling submenu for:", item);

            const megaMenu = item.querySelector('.mega-menu');
            if (!megaMenu) {
                console.error("No mega menu found for item:", item);
                return;
            }

            const wasActive = item.classList.contains('active');

            // First, close all open menus
            document.querySelectorAll('.nav-item.has-mega-menu.active').forEach(activeItem => {
                if (activeItem !== item) {  // Don't close the current item
                    activeItem.classList.remove('active');
                    const activeMenu = activeItem.querySelector('.mega-menu');
                    if (activeMenu) {
                        activeMenu.style.display = 'none';
                    }
                }
            });

            // Toggle the current item
            if (!wasActive) {
                item.classList.add('active');
                megaMenu.style.display = 'block';
                megaMenu.style.opacity = '1';
                megaMenu.style.visibility = 'visible';
                console.log("Opened submenu");
            } else {
                item.classList.remove('active');
                megaMenu.style.display = 'none';
                console.log("Closed submenu");
            }
        }

        // Handle submenu toggle button clicks
        document.querySelectorAll('.mobile-submenu-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                console.log("Mobile submenu toggle button clicked");
                e.preventDefault();
                e.stopPropagation();
                const item = toggle.closest('.nav-item');
                if (item) toggleSubmenu(item);
            });
        });

        // Handle nav link clicks for items with submenus
        document.querySelectorAll('.nav-item.has-mega-menu > .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                console.log("Mobile nav link clicked");
                const item = link.closest('.nav-item');
                if (item && item.classList.contains('has-mega-menu')) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSubmenu(item);
                }
            });
        });

        // Handle clicks outside navigation to close menus
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.nav-item') && !e.target.closest('.mobile-nav-toggle')) {
                document.querySelectorAll('.nav-item.has-mega-menu.active').forEach(item => {
                    item.classList.remove('active');
                    const menu = item.querySelector('.mega-menu');
                    if (menu) {
                        menu.style.display = 'none';
                    }
                });
            }
        });
    }
    // Desktop GSAP animations (only initialize if not mobile)
    else {
        console.log("DEBUG: Initializing GSAP and Desktop Menu Logic...");

        // Ensure GSAP is loaded
        if (typeof gsap === 'undefined') {
            console.warn('GSAP not loaded yet, retrying in 100ms...');
            setTimeout(initializeNavigation, 100);
            return;
        }

        gsap.registerPlugin(ScrollTrigger);

        // Menu state
        let activeMenu = null;
        let isAnimating = false;

        // Helper function to close mega menu
        function closeMegaMenu(megaMenu, immediate = false) {
            if (!megaMenu) return;

            if (immediate) {
                gsap.set(megaMenu, { display: 'none', opacity: 0 });
                megaMenu.classList.remove('active');
                return;
            }

            const timeline = gsap.timeline({
                onComplete: () => {
                    megaMenu.classList.remove('active');
                    isAnimating = false;
                }
            });

            // Get mega menu content that actually exists
            const megaMenuContent = megaMenu.querySelector('.mega-menu-content');
            const megaMenuColumns = megaMenu.querySelectorAll('.mega-menu-column');

            // Simple fade out animation
            timeline.to(megaMenu, {
                opacity: 0,
                duration: 0.2,
                onComplete: () => {
                    gsap.set(megaMenu, { display: 'none' });
                }
            });

            // Only animate inner elements if they exist
            if (megaMenuContent && megaMenuColumns.length) {
                timeline.to(megaMenuColumns, {
                    opacity: 0,
                    y: -10,
                    duration: 0.2,
                    stagger: 0.05
                }, 0);
            }
        }

        // Helper function to open mega menu
        function openMegaMenu(megaMenu) {
            if (!megaMenu || isAnimating) return;
            isAnimating = true;

            // Close any active menu first
            if (activeMenu && activeMenu !== megaMenu) {
                closeMegaMenu(activeMenu, true);
            }

            activeMenu = megaMenu;
            megaMenu.classList.add('active');

            // Initial state
            gsap.set(megaMenu, {
                display: 'block',
                opacity: 0
            });

            // Get mega menu content that actually exists
            const megaMenuContent = megaMenu.querySelector('.mega-menu-content');
            const megaMenuColumns = megaMenu.querySelectorAll('.mega-menu-column');

            // Set initial state for columns if they exist
            if (megaMenuColumns.length) {
                gsap.set(megaMenuColumns, {
                    opacity: 0,
                    y: 10
                });
            }

            const timeline = gsap.timeline({
                onComplete: () => isAnimating = false
            });

            // Simple fade in animation
            timeline.to(megaMenu, {
                opacity: 1,
                duration: 0.3
            });

            // Only animate inner elements if they exist
            if (megaMenuContent && megaMenuColumns.length) {
                timeline.to(megaMenuColumns, {
                    opacity: 1,
                    y: 0,
                    duration: 0.3,
                    stagger: 0.05
                }, "-=0.2");
            }
        }

        // Desktop mega menu hover handlers
        const menuItems = document.querySelectorAll('.nav-item.has-mega-menu');
        menuItems.forEach(item => {
            const megaMenu = item.querySelector('.mega-menu');
            if (!megaMenu) return;

            // Initialize mega menu state
            gsap.set(megaMenu, {
                display: 'none',
                opacity: 0
            });

            // Show menu on hover
            item.addEventListener('mouseenter', () => {
                if (window.innerWidth >= 992) { // Double-check we're still in desktop
                    openMegaMenu(megaMenu);
                }
            });

            // Hide menu when mouse leaves
            item.addEventListener('mouseleave', () => {
                if (window.innerWidth >= 992) { // Double-check we're still in desktop
                    closeMegaMenu(megaMenu);
                }
            });
        });
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        const newIsMobile = window.innerWidth < 992;
        if (newIsMobile !== isMobile) {
            // Refresh page to reinitialize everything
            window.location.reload();
        }
    });
}

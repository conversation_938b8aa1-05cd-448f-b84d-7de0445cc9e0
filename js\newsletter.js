/**
 * Newsletter Subscription Functionality
 * Handles the newsletter signup form submission and displays appropriate messages
 */

document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.getElementById('newsletter-form');
    const responseDiv = document.getElementById('newsletter-response');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(event) {
            event.preventDefault();

            // Get form data
            const emailInput = document.getElementById('newsletter-email');
            const consentCheckbox = document.getElementById('newsletter-consent');

            // Validate form
            if (!emailInput.value.trim()) {
                showMessage('Please enter your email address.', 'error');
                return;
            }

            if (!consentCheckbox.checked) {
                showMessage('Please agree to receive emails from NP Labs.', 'error');
                return;
            }

            // Show loading state
            const submitButton = newsletterForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';

            // Prepare data for EmailJS
            const templateParams = {
                to_email: '<EMAIL>',
                subscriber_email: emailInput.value.trim(),
                subscription_date: new Date().toLocaleString(),
                subscription_source: 'Website Newsletter Form'
            };

            // For demo purposes, simulate a successful submission
            // In production, uncomment the EmailJS code below
            setTimeout(() => {
                console.log('SUCCESS! (Demo mode)');

                // Extract name from email (if possible)
                let subscriberName = 'valued subscriber';
                const emailParts = emailInput.value.split('@');
                if (emailParts.length > 0) {
                    // Try to get a name from the email address
                    const namePart = emailParts[0];
                    // Capitalize first letter and replace dots/underscores with spaces
                    subscriberName = namePart
                        .replace(/[._]/g, ' ')
                        .replace(/\b\w/g, l => l.toUpperCase());
                }

                // Show success message
                showMessage(`
                    <div class="success-icon"><i class="fas fa-check-circle"></i></div>
                    <h4 class="success-title">Thank you, ${subscriberName}!</h4>
                    <p class="success-text">You've been successfully subscribed to our newsletter.</p>
                    <p class="success-subtext">Stay tuned for personalized health insights, compounding news, and wellness tips tailored to your needs.</p>
                    <p class="success-note">We respect your privacy and will never share your information with third parties.</p>
                `, 'success');

                // Reset form
                newsletterForm.reset();
            }, 1500);

            /* Production code - uncomment when ready
            emailjs.send('service_o1sfm9l', 'template_vpxoh8d', templateParams)
                .then(function(response) {
                    console.log('SUCCESS!', response.status, response.text);

                    // Extract name from email (if possible)
                    let subscriberName = 'valued subscriber';
                    const emailParts = emailInput.value.split('@');
                    if (emailParts.length > 0) {
                        // Try to get a name from the email address
                        const namePart = emailParts[0];
                        // Capitalize first letter and replace dots/underscores with spaces
                        subscriberName = namePart
                            .replace(/[._]/g, ' ')
                            .replace(/\b\w/g, l => l.toUpperCase());
                    }

                    // Show success message
                    showMessage(`
                        <div class="success-icon"><i class="fas fa-check-circle"></i></div>
                        <h4 class="success-title">Thank you, ${subscriberName}!</h4>
                        <p class="success-text">You've been successfully subscribed to our newsletter.</p>
                        <p class="success-subtext">Stay tuned for personalized health insights, compounding news, and wellness tips tailored to your needs.</p>
                        <p class="success-note">We respect your privacy and will never share your information with third parties.</p>
                    `, 'success');

                    // Reset form
                    newsletterForm.reset();
                })
            */

            // Reset button state after timeout (for demo)
            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            }, 1500);

            /* Production code - uncomment when ready
                .catch(function(error) {
                    console.error('FAILED...', error);
                    showMessage('Sorry, there was a problem subscribing you. Please try again later.', 'error');
                })
                .finally(function() {
                    // Reset button state
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                });
            */
        });
    }

    // Helper function to show messages
    function showMessage(message, type) {
        if (!responseDiv) return;

        // Add a fade-in class for animation
        responseDiv.innerHTML = `<div class="alert alert-${type === 'success' ? 'success' : 'danger'}">${message}</div>`;

        // Scroll to the message if it's not visible (with a slight delay for better UX)
        setTimeout(() => {
            responseDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);

        // If success, hide the message after 12 seconds
        if (type === 'success') {
            setTimeout(() => {
                // Fade out effect
                const alert = responseDiv.querySelector('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    alert.style.transition = 'opacity 0.5s ease';

                    // Remove after fade completes
                    setTimeout(() => {
                        responseDiv.innerHTML = '';
                    }, 500);
                }
            }, 12000);
        }
    }
});

/**
 * Service Card Click Handler
 * 
 * This script enhances the service cards by making the entire card clickable,
 * directing users to the corresponding service page when clicking anywhere on the card.
 * It preserves normal link behavior while adding keyboard accessibility.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all service cards
    const serviceCardLinks = document.querySelectorAll('.service-card-link');
    
    // Add keyboard accessibility
    serviceCardLinks.forEach(link => {
        // Make cards focusable for keyboard navigation if not already
        if (!link.hasAttribute('tabindex')) {
            link.setAttribute('tabindex', '0');
        }
        
        // Add ARIA role for accessibility
        link.setAttribute('role', 'link');
        const heading = link.querySelector('h4');
        if (heading) {
            link.setAttribute('aria-label', 'Learn more about ' + heading.textContent.trim());
        }
        
        // Add keyboard event listener
        link.addEventListener('keydown', function(e) {
            // Navigate on Enter or Space key
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.location.href = this.getAttribute('href');
            }
        });
    });
});

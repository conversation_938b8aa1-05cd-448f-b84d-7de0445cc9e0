/**
 * NP Labs Testimonials Page JavaScript
 * This file contains all the interactive functionality for the testimonials page
 * including 3D effects, animations, and interactive elements.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Register GSAP plugins if available
    if (typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
    }

    if (typeof ScrollToPlugin !== 'undefined') {
        gsap.registerPlugin(ScrollToPlugin);
    }

    // Initialize all components
    initHeroCanvas();
    init3DCards();
    initGalleryFilter();
    initTestimonialSphere();
    initVideoModal();
    initStatCounters();
});

/**
 * Initialize the 3D animated canvas in the hero section
 * Uses Three.js to create a dynamic particle background
 */
function initHeroCanvas() {
    const canvas = document.getElementById('hero-canvas');

    if (!canvas) return;

    // Set up Three.js scene
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);

    // Create particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 1500;

    const posArray = new Float32Array(particlesCount * 3);

    for (let i = 0; i < particlesCount * 3; i++) {
        posArray[i] = (Math.random() - 0.5) * 5;
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

    // Create material
    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.005,
        color: 0xffffff,
        transparent: true,
        opacity: 0.8
    });

    // Create mesh
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);

    // Position camera
    camera.position.z = 3;

    // Mouse movement effect
    let mouseX = 0;
    let mouseY = 0;

    function onDocumentMouseMove(event) {
        mouseX = (event.clientX - window.innerWidth / 2) / 100;
        mouseY = (event.clientY - window.innerHeight / 2) / 100;
    }

    document.addEventListener('mousemove', onDocumentMouseMove);

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);

        particlesMesh.rotation.x += 0.001;
        particlesMesh.rotation.y += 0.001;

        // Responsive to mouse movement
        particlesMesh.rotation.x += mouseY * 0.001;
        particlesMesh.rotation.y += mouseX * 0.001;

        renderer.render(scene, camera);
    }

    animate();

    // Handle window resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

/**
 * Initialize the 3D flip cards in the featured testimonials section
 */
function init3DCards() {
    const cards = document.querySelectorAll('.testimonial-card-3d');

    cards.forEach(card => {
        card.addEventListener('click', () => {
            card.classList.toggle('flipped');
        });

        // Add hover effect for desktop
        card.addEventListener('mouseenter', () => {
            gsap.to(card.querySelector('.card-inner'), {
                rotateY: '15deg',
                duration: 0.5,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', () => {
            if (!card.classList.contains('flipped')) {
                gsap.to(card.querySelector('.card-inner'), {
                    rotateY: '0deg',
                    duration: 0.5,
                    ease: 'power2.out'
                });
            }
        });
    });
}

/**
 * Initialize the gallery filter functionality
 */
function initGalleryFilter() {
    const filterButtons = document.querySelectorAll('.gallery-filter');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            const filter = button.getAttribute('data-filter');

            // Filter gallery items
            galleryItems.forEach(item => {
                const category = item.getAttribute('data-category');

                if (filter === 'all' || filter === category) {
                    gsap.to(item, {
                        opacity: 1,
                        scale: 1,
                        duration: 0.4,
                        ease: 'power2.out',
                        clearProps: 'all'
                    });
                    item.style.display = 'block';
                } else {
                    gsap.to(item, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.4,
                        ease: 'power2.out',
                        onComplete: () => {
                            item.style.display = 'none';
                        }
                    });
                }
            });
        });
    });
}

/**
 * Initialize the 3D testimonial sphere
 * Uses TagCloud.js to create an interactive 3D cloud of testimonial keywords
 */
function initTestimonialSphere() {
    const container = document.getElementById('testimonial-sphere');
    const currentTestimonial = document.querySelector('.current-testimonial');

    if (!container) return;

    // Testimonial data
    const testimonials = [
        {
            text: "After years of struggling with standard medications, the compounded solution from NP Labs changed everything for me.",
            author: "- James T., 45"
        },
        {
            text: "My daughter couldn't take her medication due to severe allergies. NP Labs created a custom formula without allergens that works perfectly.",
            author: "- Maria L., Mother"
        },
        {
            text: "The hormone therapy from NP Labs has eliminated my symptoms completely. I feel like myself again!",
            author: "- Patricia S., 52"
        },
        {
            text: "As a veterinarian, I've seen remarkable improvements in my patients with NP Labs' compounded pet medications.",
            author: "- Dr. Robert K., DVM"
        },
        {
            text: "The pain cream formulated for my arthritis works better than any pill I've ever taken, with zero side effects.",
            author: "- Eleanor M., 68"
        },
        {
            text: "My son's ADHD medication caused terrible side effects until we switched to a compounded formula. The difference is night and day.",
            author: "- Thomas W., Father"
        },
        {
            text: "I've struggled with migraines for 20 years. The compounded nasal spray from NP Labs is the only thing that gives me relief.",
            author: "- Sophia R., 41"
        },
        {
            text: "The custom thyroid medication has finally stabilized my levels after years of ups and downs with commercial options.",
            author: "- David L., 55"
        }
    ];

    // Create an array of keywords for the sphere
    const keywords = [
        'Personalized', 'Relief', 'Custom', 'Effective',
        'Life-changing', 'Allergy-free', 'Precise', 'Innovative',
        'Hormone', 'Pain', 'Pediatric', 'Veterinary',
        'Dermatology', 'Thyroid', 'Migraine', 'Arthritis',
        'ADHD', 'Autism', 'Compounding', 'Solution',
        'Medication', 'Therapy', 'Wellness', 'Health'
    ];

    // Configure the tag cloud
    const options = {
        radius: 300,
        maxSpeed: 'normal',
        initSpeed: 'normal',
        direction: 135,
        keep: true,
        useContainerInlineStyles: false
    };

    // Create the tag cloud
    const tagCloud = TagCloud(container, keywords, options);

    // Add click event to show random testimonial
    container.addEventListener('click', () => {
        const randomIndex = Math.floor(Math.random() * testimonials.length);
        const testimonial = testimonials[randomIndex];

        // Scroll to make sure the testimonial is visible
        const sphereOverlay = document.querySelector('.sphere-overlay');

        // Update the testimonial text with animation
        gsap.to(currentTestimonial, {
            opacity: 0,
            y: 20,
            duration: 0.3,
            onComplete: () => {
                currentTestimonial.querySelector('.quote').textContent = testimonial.text;
                currentTestimonial.querySelector('.author').textContent = testimonial.author;

                // Scroll the container into view if needed
                if (sphereOverlay) {
                    gsap.to(window, {
                        duration: 0.7,
                        scrollTo: {
                            y: sphereOverlay,
                            offsetY: 100
                        },
                        ease: 'power2.out'
                    });
                }

                gsap.to(currentTestimonial, {
                    opacity: 1,
                    y: 0,
                    duration: 0.5
                });
            }
        });
    });

    // Make sphere responsive
    window.addEventListener('resize', () => {
        const width = container.offsetWidth;
        const newRadius = width < 500 ? 200 : 300;
        tagCloud.update({ radius: newRadius });
    });
}

/**
 * Initialize the video modal functionality
 */
function initVideoModal() {
    const videoThumbnails = document.querySelectorAll('.video-thumbnail');
    const modal = document.getElementById('video-modal');
    const videoPlayer = document.getElementById('video-player');
    const closeModal = document.querySelector('.close-modal');

    if (!modal || !videoPlayer) return;

    // Video sources (in a real implementation, these would be actual video files)
    const videoSources = {
        testimonial1: 'videos/testimonial-hormone-therapy.mp4',
        testimonial2: 'videos/testimonial-pain-management.mp4',
        testimonial3: 'videos/testimonial-pediatric.mp4'
    };

    // Open modal and play video
    videoThumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', () => {
            const videoId = thumbnail.getAttribute('data-video-id');
            const videoSource = videoSources[videoId];

            // In a real implementation, set the video source
            // videoPlayer.querySelector('source').setAttribute('src', videoSource);
            // videoPlayer.load();

            // For demo purposes, show a message
            console.log(`Video would play: ${videoSource}`);

            // Show modal
            modal.style.display = 'flex';

            // Add animation
            gsap.fromTo(modal.querySelector('.modal-content'),
                { opacity: 0, y: 50 },
                { opacity: 1, y: 0, duration: 0.5 }
            );
        });
    });

    // Close modal
    closeModal.addEventListener('click', () => {
        // Pause video
        videoPlayer.pause();

        // Hide modal with animation
        gsap.to(modal.querySelector('.modal-content'), {
            opacity: 0,
            y: 50,
            duration: 0.3,
            onComplete: () => {
                modal.style.display = 'none';
            }
        });
    });

    // Close modal when clicking outside content
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal.click();
        }
    });
}

/**
 * Initialize the animated stat counters
 */
function initStatCounters() {
    const stats = document.querySelectorAll('.stat-number');

    // Create intersection observer to trigger counting animation when stats are in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const countTo = parseInt(target.getAttribute('data-count'));

                // Animate the counter
                let count = 0;
                const updateCount = () => {
                    const increment = countTo / 50; // Divide by steps

                    if (count < countTo) {
                        count += increment;
                        target.textContent = Math.ceil(count);
                        requestAnimationFrame(updateCount);
                    } else {
                        target.textContent = countTo;
                    }
                };

                updateCount();

                // Unobserve after animation
                observer.unobserve(target);
            }
        });
    }, { threshold: 0.5 });

    // Observe all stat numbers
    stats.forEach(stat => {
        observer.observe(stat);
    });
}

/* Enhanced Login Page Styles */

/* Main Login Section */
.login-section {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    min-height: 600px;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 2rem;
}

/* Left Panel - Branding */
.login-brand-panel {
    flex: 1;
    background-image: url('images/login-bg.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.login-brand-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

.login-brand-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 400px;
}

.login-logo {
    margin-bottom: 2rem;
}

.login-logo img {
    height: 60px;
    filter: brightness(0) invert(1);
}

.login-brand-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-brand-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.login-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: left;
}

.login-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.login-feature:hover {
    transform: translateX(5px);
}

.login-feature i {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Right Panel - Form */
.login-form-panel {
    flex: 1;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    width: 100%;
    max-width: 400px;
    background-color: transparent;
    box-shadow: none;
    padding: 0;
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-header h2 {
    font-size: 2.2rem;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.login-description {
    color: var(--dark-grey);
    font-size: 1.1rem;
}

/* Form Styling */
.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 0.95rem;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-grey);
    font-size: 1.1rem;
}

.input-with-icon input {
    width: 100%;
    padding: 1rem 1rem 1rem 45px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-with-icon input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
    outline: none;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--dark-grey);
    cursor: pointer;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-blue);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-blue);
    cursor: pointer;
}

.remember-me label {
    margin: 0;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
}

.forgot-password a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.forgot-password a:hover {
    color: var(--secondary-teal);
    text-decoration: underline;
}

/* Login Button */
.login-button {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.login-button:hover::before {
    left: 100%;
}

.login-button i {
    transition: transform 0.3s ease;
}

.login-button:hover i {
    transform: translateX(3px);
}

/* Divider */
.login-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: var(--dark-grey);
    font-size: 0.9rem;
}

.login-divider::before,
.login-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
}

.login-divider span {
    padding: 0 1rem;
}

/* Social Login */
.social-login {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-social {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-social:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-social img {
    width: 24px;
    height: 24px;
}

.btn-social i {
    font-size: 1.5rem;
}

.btn-google:hover {
    border-color: #DB4437;
}

.btn-apple:hover {
    border-color: #000;
}

.btn-facebook:hover {
    border-color: #4267B2;
}

.btn-apple i {
    color: #000;
}

.btn-facebook i {
    color: #4267B2;
}

/* Register Prompt */
.register-prompt {
    text-align: center;
    color: var(--dark-grey);
    font-size: 1rem;
    margin: 0;
}

.register-prompt a {
    color: var(--primary-blue);
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
}

.register-prompt a:hover {
    color: var(--secondary-teal);
    text-decoration: underline;
}

/* Form Error Messages */
.form-error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
}

input.is-invalid {
    border-color: #dc3545 !important;
}

input.is-invalid + .toggle-password {
    right: 40px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .login-container {
        flex-direction: column;
        max-width: 600px;
        margin: 1.5rem;
    }

    .login-brand-panel {
        padding: 2.5rem 1.5rem;
    }

    .login-form-panel {
        padding: 2.5rem 1.5rem;
    }

    .login-brand-title {
        font-size: 2rem;
    }

    .login-features {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem 2rem;
    }

    .login-feature {
        width: auto;
    }
}

@media (max-width: 768px) {
    .login-container {
        margin: 1rem;
        min-height: auto;
    }

    .login-brand-panel {
        padding: 2rem 1rem;
    }

    .login-form-panel {
        padding: 2rem 1rem;
    }

    .login-header h2 {
        font-size: 1.8rem;
    }

    .login-description {
        font-size: 1rem;
    }

    .login-features {
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .login-feature {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .forgot-password {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .login-container {
        margin: 0;
        border-radius: 0;
        box-shadow: none;
    }

    .login-brand-panel {
        display: none;
    }

    .login-form-panel {
        padding: 1.5rem 1rem;
    }

    .login-button {
        padding: 0.9rem;
    }

    .social-login {
        gap: 0.75rem;
    }

    .btn-social {
        width: 45px;
        height: 45px;
    }
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header,
.login-form,
.login-divider,
.social-login,
.register-prompt {
    animation: fadeIn 0.6s ease forwards;
}

.login-form {
    animation-delay: 0.1s;
}

.login-divider {
    animation-delay: 0.2s;
}

.social-login {
    animation-delay: 0.3s;
}

.register-prompt {
    animation-delay: 0.4s;
}

.login-feature:nth-child(1) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.2s;
}

.login-feature:nth-child(2) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.3s;
}

.login-feature:nth-child(3) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.4s;
}

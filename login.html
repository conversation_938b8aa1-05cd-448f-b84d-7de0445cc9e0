<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/login.html">
    <title>Login | NP Labs Compounding Pharmacy</title>
    <meta name="description" content="Securely access your NP Labs account. Login to manage your prescriptions, orders, and personalized medication information from our compounding pharmacy.">
    <meta name="keywords" content="login, account access, patient portal, prescriber portal, secure login, pharmacy account, NP Labs login">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Login | NP Labs Compounding Pharmacy">
    <meta property="og:description" content="Securely access your NP Labs account. Login to manage your prescriptions and orders.">
    <meta property="og:image" content="https://www.nplabs.com/images/nplabs-login-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/login.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Login | NP Labs Compounding Pharmacy">
    <meta name="twitter:description" content="Securely access your NP Labs account. Login to manage your prescriptions and orders.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/nplabs-login-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="stylesheet" href="login.css">
    <link rel="stylesheet" href="css/form-improvements.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
</head>
<body>

    <div id="header-placeholder"></div> <!-- Header will be loaded here -->

    <main id="main-content" class="form-page">
        <section class="login-section">
            <div class="login-container">
                <!-- Left side - Image/Branding -->
                <div class="login-brand-panel">
                    <div class="login-brand-content">
                        <div class="login-logo">
                            <img src="nplabslogo.svg" alt="NP Labs Logo">
                        </div>
                        <h1 class="login-brand-title">Welcome to NP Labs</h1>
                        <p class="login-brand-subtitle">Your trusted partner in personalized compounding solutions</p>
                        <div class="login-features">
                            <div class="login-feature">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure Access</span>
                            </div>
                            <div class="login-feature">
                                <i class="fas fa-prescription-bottle-alt"></i>
                                <span>Manage Prescriptions</span>
                            </div>
                            <div class="login-feature">
                                <i class="fas fa-history"></i>
                                <span>Track Orders</span>
                            </div>
                        </div>
                    </div>
                    <div class="login-brand-overlay"></div>
                </div>

                <!-- Right side - Login Form -->
                <div class="login-form-panel">
                    <div class="login-card">
                        <div class="login-header">
                            <h2>Sign In</h2>
                            <p class="login-description">Access your personal dashboard</p>
                        </div>

                        <form class="login-form" id="login-form" novalidate>
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <div class="input-with-icon">
                                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required aria-describedby="email-error">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <small id="email-error" class="form-error-message" aria-live="polite"></small>
                            </div>

                            <div class="form-group password-group">
                                <label for="password">Password</label>
                                <div class="input-with-icon password-input-wrapper">
                                    <input type="password" id="password" name="password" placeholder="Enter your password" required aria-describedby="password-error">
                                    <i class="fas fa-lock"></i>
                                    <button type="button" class="toggle-password" aria-label="Show password">
                                        <i class="fa-regular fa-eye"></i>
                                    </button>
                                </div>
                                <small id="password-error" class="form-error-message" aria-live="polite"></small>
                            </div>

                            <div class="form-group form-options">
                                <div class="remember-me">
                                    <input type="checkbox" id="remember" name="remember">
                                    <label for="remember">Remember me</label>
                                </div>
                                <div class="forgot-password">
                                    <a href="#">Forgot Password?</a>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary login-button">
                                <span>Sign In</span>
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </form>

                        <div class="login-divider"><span>or continue with</span></div>

                        <div class="social-login">
                            <button class="btn-social btn-google">
                                <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg" alt="Google">
                            </button>
                            <button class="btn-social btn-apple">
                                <i class="fab fa-apple"></i>
                            </button>
                            <button class="btn-social btn-facebook">
                                <i class="fab fa-facebook-f"></i>
                            </button>
                        </div>

                        <p class="register-prompt">Don't have an account? <a href="register.html">Create Account</a></p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div> <!-- Footer will be loaded here -->

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>
    <script src="scripts.js"></script>
    <script src="js/include-html.js" defer></script>
    <script>
        // Enhanced form validation and interactions
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('login-form');
            const togglePasswordBtn = document.querySelector('.toggle-password');
            const passwordInput = document.getElementById('password');
            const emailInput = document.getElementById('email');

            // Toggle password visibility
            togglePasswordBtn.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Toggle icon
                const icon = this.querySelector('i');
                if (type === 'text') {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });

            // Real-time validation feedback
            emailInput.addEventListener('input', function() {
                validateEmail();
            });

            passwordInput.addEventListener('input', function() {
                validatePassword();
            });

            // Form submission validation
            form.addEventListener('submit', function(event) {
                let isValid = true;

                // Reset previous errors
                document.querySelectorAll('.form-error-message').forEach(el => el.textContent = '');
                emailInput.classList.remove('is-invalid');
                passwordInput.classList.remove('is-invalid');

                // Validate email
                if (!validateEmail()) {
                    isValid = false;
                }

                // Validate password
                if (!validatePassword()) {
                    isValid = false;
                }

                if (!isValid) {
                    event.preventDefault(); // Prevent form submission if invalid
                    // Add shake animation to form
                    form.classList.add('shake');
                    setTimeout(() => {
                        form.classList.remove('shake');
                    }, 500);
                } else {
                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
                    submitBtn.disabled = true;

                    // Simulate form submission (remove in production)
                    event.preventDefault();
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        // Redirect or show success message
                        // window.location.href = 'dashboard.html';
                    }, 2000);
                }
            });

            // Helper functions
            function validateEmail() {
                const value = emailInput.value.trim();
                const errorElement = document.getElementById('email-error');

                if (!value) {
                    emailInput.classList.add('is-invalid');
                    errorElement.textContent = 'Email address is required';
                    return false;
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    emailInput.classList.add('is-invalid');
                    errorElement.textContent = 'Please enter a valid email address';
                    return false;
                } else {
                    emailInput.classList.remove('is-invalid');
                    errorElement.textContent = '';
                    return true;
                }
            }

            function validatePassword() {
                const value = passwordInput.value;
                const errorElement = document.getElementById('password-error');

                if (!value) {
                    passwordInput.classList.add('is-invalid');
                    errorElement.textContent = 'Password is required';
                    return false;
                } else {
                    passwordInput.classList.remove('is-invalid');
                    errorElement.textContent = '';
                    return true;
                }
            }

            // Social login buttons
            document.querySelectorAll('.btn-social').forEach(btn => {
                btn.addEventListener('click', function() {
                    alert('Social login is not implemented in this demo.');
                });
            });
        });
    </script>

    <style>
        /* Additional animations */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
        }
    </style>
</body>
</html>
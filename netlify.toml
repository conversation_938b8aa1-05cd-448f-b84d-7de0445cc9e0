# Netlify configuration file

[build]
  # Build command (not needed for static site)
  command = "echo 'No build step required'"

  # Directory to publish
  publish = "."

  # Functions directory
  functions = "netlify/functions"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# Admin API redirects
[[redirects]]
  from = "/api/admin/*"
  to = "/.netlify/functions/admin-api/:splat"
  status = 200

# Admin interface redirects
[[redirects]]
  from = "/admin"
  to = "/admin/index.html"
  status = 200

[[redirects]]
  from = "/admin/*"
  to = "/admin/:splat"
  status = 200

# Blog redirects (preserve existing structure)
[[redirects]]
  from = "/blog"
  to = "/blog/index.html"
  status = 200

[[redirects]]
  from = "/blog/*"
  to = "/blog/:splat"
  status = 200

# Enable pretty URLs (remove .html extension) - but exclude admin
[[redirects]]
  from = "/*.html"
  to = "/:splat"
  status = 301
  force = false
  conditions = {Path = ["!admin/*"]}

# Set custom 404 page
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Performance optimization
[build.processing]
  skip_processing = false
[build.processing.css]
  bundle = true
  minify = true
[build.processing.js]
  bundle = true
  minify = true
[build.processing.html]
  pretty_urls = true
[build.processing.images]
  compress = true

# Admin-specific security headers
[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.quilljs.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.quilljs.com https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob:; connect-src 'self';"

# API headers for CORS
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type, Authorization"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"

# General site headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net; img-src 'self' data:; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://unpkg.com; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://unpkg.com;"

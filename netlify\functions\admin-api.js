/**
 * Netlify Functions API for NP Labs Blog Admin
 * 
 * This function handles all admin API requests for the blog system.
 * It provides endpoints for authentication, post management, and file operations.
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Configuration
const CONFIG = {
    adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    blogPath: path.join(process.cwd(), 'blog'),
    postsPath: path.join(process.cwd(), 'blog', 'posts'),
    imagesPath: path.join(process.cwd(), 'blog', 'assets', 'images')
};

// Simple JWT implementation
const JWT = {
    sign(payload, secret, expiresIn = '24h') {
        const header = { alg: 'HS256', typ: 'JWT' };
        const now = Math.floor(Date.now() / 1000);
        const exp = now + (24 * 60 * 60); // 24 hours
        
        const jwtPayload = { ...payload, iat: now, exp };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
        const encodedPayload = Buffer.from(JSON.stringify(jwtPayload)).toString('base64url');
        
        const signature = crypto
            .createHmac('sha256', secret)
            .update(`${encodedHeader}.${encodedPayload}`)
            .digest('base64url');
        
        return `${encodedHeader}.${encodedPayload}.${signature}`;
    },
    
    verify(token, secret) {
        try {
            const [header, payload, signature] = token.split('.');
            
            const expectedSignature = crypto
                .createHmac('sha256', secret)
                .update(`${header}.${payload}`)
                .digest('base64url');
            
            if (signature !== expectedSignature) {
                throw new Error('Invalid signature');
            }
            
            const decodedPayload = JSON.parse(Buffer.from(payload, 'base64url').toString());
            
            if (decodedPayload.exp < Math.floor(Date.now() / 1000)) {
                throw new Error('Token expired');
            }
            
            return decodedPayload;
        } catch (error) {
            throw new Error('Invalid token');
        }
    }
};

// Middleware for authentication
function requireAuth(token) {
    if (!token) {
        throw new Error('No token provided');
    }
    
    const bearerToken = token.startsWith('Bearer ') ? token.slice(7) : token;
    return JWT.verify(bearerToken, CONFIG.jwtSecret);
}

// Route handlers
const routes = {
    // Authentication
    'POST /auth/login': async (event) => {
        const { password } = JSON.parse(event.body);
        
        if (password === CONFIG.adminPassword) {
            const token = JWT.sign({ role: 'admin' }, CONFIG.jwtSecret);
            return {
                statusCode: 200,
                body: JSON.stringify({ success: true, token })
            };
        }
        
        return {
            statusCode: 401,
            body: JSON.stringify({ success: false, message: 'Invalid password' })
        };
    },

    // Get blog statistics
    'GET /stats': async (event) => {
        requireAuth(event.headers.authorization);
        
        try {
            const stats = await getBlogStats();
            return {
                statusCode: 200,
                body: JSON.stringify(stats)
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: error.message })
            };
        }
    },

    // Get posts list
    'GET /posts': async (event) => {
        requireAuth(event.headers.authorization);
        
        try {
            const posts = await getAllPosts();
            return {
                statusCode: 200,
                body: JSON.stringify(posts)
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: error.message })
            };
        }
    },

    // Create new post
    'POST /posts': async (event) => {
        requireAuth(event.headers.authorization);
        
        try {
            const postData = JSON.parse(event.body);
            const result = await createPost(postData);
            return {
                statusCode: 201,
                body: JSON.stringify(result)
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: error.message })
            };
        }
    },

    // Update existing post
    'PUT /posts/:slug': async (event) => {
        requireAuth(event.headers.authorization);
        
        try {
            const slug = event.path.split('/').pop();
            const postData = JSON.parse(event.body);
            const result = await updatePost(slug, postData);
            return {
                statusCode: 200,
                body: JSON.stringify(result)
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: error.message })
            };
        }
    },

    // Delete post
    'DELETE /posts/:slug': async (event) => {
        requireAuth(event.headers.authorization);
        
        try {
            const slug = event.path.split('/').pop();
            await deletePost(slug);
            return {
                statusCode: 200,
                body: JSON.stringify({ success: true })
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: error.message })
            };
        }
    }
};

// Helper functions
async function getBlogStats() {
    try {
        // Read blog index to count posts
        const indexPath = path.join(CONFIG.blogPath, 'index.html');
        const indexContent = await fs.readFile(indexPath, 'utf8');
        
        // Simple regex to count posts (this could be improved)
        const postMatches = indexContent.match(/class="post-card"/g) || [];
        const featuredMatches = indexContent.match(/class="featured-post-card"/g) || [];
        
        return {
            totalPosts: postMatches.length + featuredMatches.length,
            featuredPosts: featuredMatches.length,
            totalImages: 15, // Placeholder
            categories: {
                health: 4,
                wellness: 3,
                medicine: 5,
                research: 3
            }
        };
    } catch (error) {
        throw new Error('Failed to get blog stats');
    }
}

async function getAllPosts() {
    try {
        const postsDir = await fs.readdir(CONFIG.postsPath);
        const posts = [];
        
        for (const file of postsDir) {
            if (file.endsWith('.html') && !file.startsWith('_')) {
                const postPath = path.join(CONFIG.postsPath, file);
                const content = await fs.readFile(postPath, 'utf8');
                
                // Extract post metadata (this is a simplified version)
                const titleMatch = content.match(/<title>(.*?)<\/title>/);
                const title = titleMatch ? titleMatch[1].split(' | ')[0] : 'Untitled';
                
                posts.push({
                    slug: file.replace('.html', ''),
                    title,
                    filename: file,
                    lastModified: (await fs.stat(postPath)).mtime
                });
            }
        }
        
        return posts.sort((a, b) => b.lastModified - a.lastModified);
    } catch (error) {
        throw new Error('Failed to get posts list');
    }
}

async function createPost(postData) {
    // This is a simplified version - in a real implementation,
    // this would generate the full HTML file and update the blog index
    
    const slug = postData.slug || generateSlug(postData.title);
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    // Check if post already exists
    try {
        await fs.access(postPath);
        throw new Error('Post with this slug already exists');
    } catch (error) {
        if (error.code !== 'ENOENT') {
            throw error;
        }
    }
    
    // Generate post HTML (simplified)
    const postHTML = generatePostHTML(postData, slug);
    
    // Write post file
    await fs.writeFile(postPath, postHTML);
    
    // Update blog index (simplified)
    await updateBlogIndex(postData, slug);
    
    return { slug, filename, success: true };
}

async function updatePost(slug, postData) {
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    // Check if post exists
    try {
        await fs.access(postPath);
    } catch (error) {
        throw new Error('Post not found');
    }
    
    // Generate updated HTML
    const postHTML = generatePostHTML(postData, slug);
    
    // Write updated file
    await fs.writeFile(postPath, postHTML);
    
    return { slug, filename, success: true };
}

async function deletePost(slug) {
    const filename = `${slug}.html`;
    const postPath = path.join(CONFIG.postsPath, filename);
    
    // Delete post file
    await fs.unlink(postPath);
    
    // Remove from blog index (simplified)
    // In a real implementation, this would update the index file
    
    return { success: true };
}

function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

function generatePostHTML(postData, slug) {
    // This is a very simplified template
    // In a real implementation, this would use a proper template engine
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${postData.title} | NP Labs Blog</title>
    <meta name="description" content="${postData.metaDescription || ''}">
    <!-- Additional meta tags would go here -->
</head>
<body>
    <h1>${postData.title}</h1>
    <div class="post-content">
        ${postData.content}
    </div>
</body>
</html>`;
}

async function updateBlogIndex(postData, slug) {
    // Simplified blog index update
    // In a real implementation, this would properly update the blog index
    console.log('Updating blog index for:', slug);
}

// Main handler
exports.handler = async (event, context) => {
    // Enable CORS
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Extract route
        const method = event.httpMethod;
        const path = event.path.replace('/.netlify/functions/admin-api', '');
        const route = `${method} ${path}`;
        
        // Find matching route
        const handler = routes[route] || routes[`${method} ${path.replace(/\/[^\/]+$/, '/:slug')}`];
        
        if (!handler) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Route not found' })
            };
        }
        
        const result = await handler(event);
        return {
            ...result,
            headers: { ...headers, ...result.headers }
        };
        
    } catch (error) {
        console.error('API Error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

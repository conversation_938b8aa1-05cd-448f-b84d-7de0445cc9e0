
```powershell
# Check if footer is loaded via JS
Select-String -Path "c:\Users\<USER>\Downloads\NPLabtest2\nplabs\js\include-html.js" -Pattern "footer"# NPLabs Project Technical Report

## Overview
NPLabs is a pharmaceutical/healthcare company website with these key features:
- Multi-page static site (12+ HTML pages)
- Responsive design (mobile, tablet, desktop)
- Patient/prescriber registration forms
- Team member profiles
- Service descriptions
- Partner showcase

## Architecture
### Frontend Implementation Details
- **HTML Structure**:
  - Semantic HTML5 with proper landmark regions
  - Modular components (headers/footers loaded via JS)
  - Each page has consistent DOM structure:
    ```html
    <!DOCTYPE html>
    <html>
      <head>...</head>
      <body>
        <div id="header-placeholder"></div>
        <main id="main-content">...</main>
        <div id="footer-placeholder"></div>
      </body>
    </html>
    ```

- **CSS Architecture**:
  - 3-tier CSS structure:
    1. `core.css` - Variables and base styles
    2. `components.css` - Reusable UI components
    3. `sections.css` - Page-specific sections
  - CSS Variables for theming:
    ```css
    :root {
      --primary-blue: #00509e;
      --secondary-teal: #00a896;
      --border-radius: 4px;
      --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    ```
  - Mobile-first media queries at 576px, 768px, 992px breakpoints

- **JavaScript Features**:
  - Dynamic header/footer loading via `include-html.js`
  - Mobile menu toggle functionality
  - Form validation helpers
  - AOS animations initialization

## Pixel-Perfect Implementation Guide

### 1. Exact Breakpoints
```css
/* Mobile-first media queries */
@media (min-width: 576px) {  /* Portrait tablets */
  :root { --header-height: 70px; }
}

@media (min-width: 768px) {  /* Landscape tablets */
  :root { --header-height: 80px; }
  .team-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 992px) {  /* Desktops */
  :root { --header-height: 90px; }
  .team-grid { grid-template-columns: repeat(3, 1fr); }
}
```

### 2. Complete Color System
| Variable | Hex | Usage |
|----------|-----|-------|
| --primary-blue | #00509e | Main buttons, headings |
| --primary-blue-light | #1a68b0 | Hover states |
| --secondary-teal | #00a896 | Accent elements |
| --light-grey | #f5f7fa | Section backgrounds |
| --text-color | #333333 | Body text |

### 3. Typography Implementation
```css
/* core.css */
body {
  font-family: 'Lato', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
}

h1 { font-size: 2.5rem; line-height: 1.2; }
h2 { font-size: 2rem; line-height: 1.3; }

/* Font loading (add to head) */
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700;900&display=swap" rel="stylesheet">
```

### 4. Animation Specifications
| Element | Property | Duration | Easing | Delay |
|---------|----------|----------|--------|-------|
| Hero content | fade-in | 0.5s | ease-out | 0.1s |
| Team cards | slide-up | 0.4s | cubic-bezier(.17,.67,.83,.67) | 0.2s |
| Form errors | shake | 0.6s | ease-in-out | none |

### 5. Form Validation Rules
```javascript
// script.js - Validation logic
const validationRules = {
  'register-email': {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    error: 'Valid email required'
  },
  'register-password': {
    minLength: 8,
    maxLength: 32,
    requireNumber: true,
    error: '8-32 chars with 1 number'
  }
};
```

### 6. Image Optimization Guide
| Directory | Current Size | Recommended | Format |
|-----------|-------------|-------------|--------|
| /images/team | 2.1MB | 500KB | WebP |
| /images/hero | 1.8MB | 300KB | AVIF |
| /images/partners | 0.3MB | 150KB | SVG |

### 7. Performance Budget
- Max page weight: 1.5MB
- CSS budget: 50KB
- JS budget: 100KB
- Largest contentful paint: <2s

### 8. Critical CSS
```css
/* Above-the-fold styles to inline */
.hero-section {
  min-height: 100vh;
  background: var(--primary-blue);
}
.header {
  position: fixed;
  top: 0;
  width: 100%;
}
```

## Interaction Flow Documentation

### 1. Hero Banner Interaction
**Mermaid Code**:
```mermaid
flowchart TD
    A[Page Load] --> B[Load Hero Image]
    B --> C{Image Loaded?}
    C -- Yes --> D[Add 'loaded' class]
    D --> E[Start Background Zoom]
    E --> F[Fade In Content]
    F --> G[Stagger CTA Buttons]
    C -- No --> H[Show Fallback Color]
```

**Rendered Output Specifications**:
- **Format**: SVG vector graphic
- **Dimensions**: 800x600px
- **Colors**:
  - Success path: #4CAF50
  - Error path: #F44336
  - Neutral nodes: #2196F3

### 2. Mobile Menu State Machine
**Mermaid Code**:
```mermaid
stateDiagram-v2
    [*] --> Closed
    Closed --> Opening: Click Hamburger
    Opening --> Open: Animation Complete
    Open --> Closing: Click X/Overlay
    Closing --> Closed: Animation Complete
```

**Rendering Instructions**:
1. Install Mermaid CLI: `npm install -g @mermaid-js/mermaid-cli`
2. Generate diagram: `mmdc -i menu.mmd -o menu.svg`
3. Embed in HTML: `<img src="menu.svg" alt="Mobile Menu States">`

### 3. Form Submission Process
**Visual Representation**:
```
[User Input] → [Validation] → Valid? → Yes → [Submit] → [API Call]
                   ↓                       ↑
                   No                      ↓
                   ↓                  [Success/Failure]
                [Show Error]               ↓
                                      [Update UI]
```

**Alternative Formats**:
- **PNG**: High-res for presentations
- **PDF**: Vector for print documentation
- **Interactive**: HTML/JS version available in `/docs/interactive`

To render all diagrams locally:
```bash
# Install dependencies
npm install -g @mermaid-js/mermaid-cli

# Process all .mmd files
find ./docs -name "*.mmd" -exec mmdc -i {} -o {}.svg \;
```

## Interactive Component Specifications

### 1. Hero Banner Component
**File**: `index.html` + `hero1.css`

**HTML Structure**:
```html
<section class="hero-section">
  <div class="hero-background">
    <picture>
      <source media="(max-width: 768px)" srcset="images/hero-mobile.jpg">
      <img src="images/hero-desktop.jpg" alt="" class="hero-bg-img">
    </picture>
  </div>
  
  <div class="hero-content" data-aos="fade-up">
    <h1>Personalized Medicine Solutions</h1>
    <p>Advanced compounding for your unique health needs</p>
    <div class="hero-cta-group">
      <a href="/register" class="hero-cta primary">Get Started</a>
      <a href="/about" class="hero-cta secondary">Learn More</a>
    </div>
  </div>
</section>
```

**Animation Sequence**:
1. Background image zooms in (0.5s ease-in)
2. Content fades up (0.6s ease-out with 0.3s delay)
3. CTA buttons stagger in (each delayed by 0.1s)

**CSS Implementation**:
```css
.hero-section {
  height: 100vh;
  min-height: 600px;
  position: relative;
  overflow: hidden;
}

.hero-bg-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1);
  transition: transform 8s ease;
}

.hero-section.loaded .hero-bg-img {
  transform: scale(1.1);
}

.hero-content {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out 0.3s;
}

.hero-section.loaded .hero-content {
  opacity: 1;
  transform: translateY(0);
}
```

### 2. Mobile Menu Component
**File**: `navigation.js` + `components.css`

**State Diagram**:
```
[Closed]
  → Hamburger icon visible
  → Menu off-screen (left: -100%)
  → Click → [Opening Animation] → [Open]

[Open] 
  → X icon visible
  → Menu visible (left: 0)
  → Overlay dims content
  → Click/Scroll → [Closing Animation] → [Closed]
```

**Interaction Details**:
```javascript
// Mobile menu state management
const menuStates = {
  CLOSED: 0,
  OPENING: 1,
  OPEN: 2,
  CLOSING: 3
};

let currentState = menuStates.CLOSED;

function toggleMenu() {
  if (currentState === menuStates.CLOSED) {
    openMenu();
  } else if (currentState === menuStates.OPEN) {
    closeMenu();
  }
}

function openMenu() {
  currentState = menuStates.OPENING;
  nav.style.transition = 'left 0.3s ease';
  overlay.style.transition = 'opacity 0.3s ease';
  
  nav.style.left = '0';
  overlay.style.opacity = '1';
  
  setTimeout(() => {
    currentState = menuStates.OPEN;
    document.body.style.overflow = 'hidden';
  }, 300);
}
```

**Accessibility Features**:
```javascript
// Handle keyboard navigation
nav.addEventListener('keydown', (e) => {
  if (currentState !== menuStates.OPEN) return;
  
  if (e.key === 'Escape') closeMenu();
  if (e.key === 'Tab') trapFocus(e);
});

// Focus management
function trapFocus(e) {
  const focusable = nav.querySelectorAll('button, a[href]');
  const first = focusable[0];
  const last = focusable[focusable.length - 1];
  
  if (e.shiftKey && document.activeElement === first) {
    last.focus();
    e.preventDefault();
  } else if (!e.shiftKey && document.activeElement === last) {
    first.focus();
    e.preventDefault();
  }
}
```

### 3. Footer Component
**File**: `_footer.html` + `footer.css`

**HTML Structure**:
```html
<footer class="main-footer">
  <div class="footer-grid">
    <div class="footer-col">
      <img src="images/nplabslogo-white.svg" alt="NP Labs" class="footer-logo">
      <div class="social-links">...</div>
    </div>
    
    <div class="footer-col">
      <h4>Quick Links</h4>
      <ul class="footer-links">
        <li><a href="/about">About Us</a></li>
        <!-- More links -->
      </ul>
    </div>
    
    <div class="footer-col">
      <h4>Contact</h4>
      <address>...</address>
    </div>
  </div>
  
  <div class="footer-bottom">
    <p> 2025 NP Labs. All rights reserved.</p>
    <div class="legal-links">
      <a href="/privacy">Privacy Policy</a>
      <a href="/terms">Terms of Use</a>
    </div>
  </div>
</footer>
```

**Responsive Behavior**:
```css
/* Desktop (3 columns) */
.footer-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

/* Tablet (2 columns) */
@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-col:last-child {
    grid-column: span 2;
  }
}

/* Mobile (1 column) */
@media (max-width: 480px) {
  .footer-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-col:last-child {
    grid-column: span 1;
  }
}
```

**Loading Sequence**:
1. Footer loads with content (no animation)
2. After 0.5s delay, border-top animation slides in (1s ease)
3. Social links fade in with staggered delay (0.1s each)

**Error States**:
- If dynamic loading fails, shows static copyright line
- Fallback to simple column layout if CSS Grid unsupported

## Interaction Flow Charts

### 1. Hero Banner Interaction Flow
```mermaid
flowchart TD
    A[Page Load] --> B[Load Hero Image]
    B --> C{Image Loaded?}
    C -- Yes --> D[Add 'loaded' class]
    D --> E[Start Background Zoom]
    E --> F[Fade In Content]
    F --> G[Stagger CTA Buttons]
    C -- No --> H[Show Fallback Color]
    
    subgraph Animations
    E -->|Duration: 8s| E1[Transform: scale(1.1)]
    F -->|Delay: 0.3s| F1[Opacity: 0→1]
    F -->|Duration: 0.6s| F2[TranslateY: 30px→0]
    G -->|Each Button| G1[Delay +0.1s]
    end
```

### 2. Mobile Menu State Flow
```mermaid
stateDiagram-v2
    [*] --> Closed
    Closed --> Opening: Click Hamburger
    Opening --> Open: Animation Complete
    Open --> Closing: Click X/Overlay
    Closing --> Closed: Animation Complete
    
    state Closed {
        Menu: left=-100%
        Icon: Hamburger
        Body: Scrollable
    }
    
    state Open {
        Menu: left=0
        Icon: X
        Body: Locked
        Overlay: Visible
    }
```

### 3. Form Submission Flow
```mermaid
flowchart LR
    A[User Input] --> B[Live Validation]
    B --> C{Valid?}
    C -- Yes --> D[Enable Submit]
    C -- No --> E[Show Error]
    D --> F[User Submits]
    F --> G[Show Loading State]
    G --> H[API Request]
    H --> I{Success?}
    I -- Yes --> J[Redirect/Confirmation]
    I -- No --> K[Show Error Message]
    K --> B
```

### 4. Responsive Layout Flow
```mermaid
flowchart TB
    A[Viewport Change] --> B{Width ≥ 992px?}
    B -- Yes --> C[Desktop Layout]
    B -- No --> D{Width ≥ 768px?}
    D -- Yes --> E[Tablet Layout]
    D -- No --> F[Mobile Layout]
    
    subgraph Desktop
    C --> C1[3 Column Grid]
    C --> C2[Full Navigation]
    end
    
    subgraph Tablet
    E --> E1[2 Column Grid]
    E --> E2[Condensed Nav]
    end
    
    subgraph Mobile
    F --> F1[1 Column Stack]
    F --> F2[Hamburger Menu]
    end
```

### 5. Dynamic Content Loading Flow
```mermaid
sequenceDiagram
    participant Page
    participant JS
    participant Server
    
    Page->>JS: Load include-html.js
    JS->>Server: GET /_header.html
    alt Success
        Server-->>JS: HTML Content
        JS->>Page: Inject into placeholder
        JS->>JS: Reinitialize scripts
    else Failure
        Server-->>JS: Error
        JS->>Page: Show error message
        JS->>Page: Load static fallback
    end
```

Each chart documents:
1. Key user interactions
2. System responses
3. Error handling paths
4. State transitions
5. Responsive breakpoints

## Component Implementation Details

### 1. Header/Navigation Component
**File**: `_header.html` + `navigation.js`

**HTML Structure**:
```html
<header class="main-header">
  <div class="header-container">
    <a href="/" class="logo-link">
      <img src="images/nplabslogo.svg" alt="NP Labs" class="logo">
    </a>
    
    <button id="mobile-nav-toggle" aria-label="Menu">
      <span class="hamburger"></span>
    </button>
    
    <nav id="main-nav">
      <ul class="nav-list">
        <li class="nav-item"><a href="/about">About</a></li>
        <li class="nav-item has-dropdown">
          <a href="/services">Services</a>
          <div class="dropdown-menu">...</div>
        </li>
      </ul>
    </nav>
  </div>
</header>
```

**CSS Highlights**:
```css
.main-header {
  height: var(--header-height);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Mobile menu styles */
@media (max-width: 991px) {
  #main-nav {
    position: fixed;
    top: var(--header-height);
    left: -100%;
    width: 80%;
    transition: left 0.3s ease;
  }
  #main-nav.active { left: 0; }
}
```

**JavaScript Behavior**:
```javascript
// Mobile menu toggle
const toggle = document.getElementById('mobile-nav-toggle');
toggle.addEventListener('click', () => {
  const nav = document.getElementById('main-nav');
  nav.classList.toggle('active');
  toggle.setAttribute('aria-expanded', nav.classList.contains('active'));
});
```

### 2. Team Member Card
**File**: `components.css` + `our-team.html`

**HTML Structure**:
```html
<div class="team-member-card" data-aos="fade-up">
  <div class="team-member-image">
    <img src="images/team/member.jpg" alt="Team Member">
  </div>
  <div class="team-member-info">
    <h4>Member Name</h4>
    <p>Role Description</p>
    <div class="social-links">...</div>
  </div>
</div>
```

**CSS Implementation**:
```css
.team-member-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.team-member-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.team-member-image {
  aspect-ratio: 1/1;
  overflow: hidden;
}

.team-member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.team-member-card:hover img {
  transform: scale(1.05);
}
```

### 3. Registration Form
**File**: `register.html` + `script.js`

**HTML Structure**:
```html
<form id="registration-form" class="registration-form">
  <div class="form-group">
    <label for="email">Email</label>
    <input type="email" id="email" required>
    <div class="error-message"></div>
  </div>
  
  <div class="form-group password-group">
    <label for="password">Password</label>
    <input type="password" id="password" required>
    <button type="button" class="password-toggle">
      <i class="far fa-eye"></i>
    </button>
    <div class="password-strength"></div>
  </div>
  
  <button type="submit" class="submit-btn">
    <span class="btn-text">Register</span>
    <span class="spinner"></span>
  </button>
</form>
```

**JavaScript Validation**:
```javascript
// Password strength indicator
passwordInput.addEventListener('input', () => {
  const strength = calculatePasswordStrength(passwordInput.value);
  strengthIndicator.style.width = `${strength}%`;
  strengthIndicator.style.backgroundColor = 
    strength < 30 ? 'red' : strength < 70 ? 'orange' : 'green';
});

// Form submission
form.addEventListener('submit', async (e) => {
  e.preventDefault();
  submitBtn.classList.add('loading');
  
  try {
    const response = await fetch('/api/register', {
      method: 'POST',
      body: JSON.stringify(formData)
    });
    // Handle response
  } catch (error) {
    showFormError(error.message);
  } finally {
    submitBtn.classList.remove('loading');
  }
});
```

### 4. Partner Logo Grid
**File**: `sections.css` + `index.html`

**HTML Structure**:
```html
<div class="partners-grid">
  <div class="partner-item">
    <div class="partner-logo-container">
      <img src="images/partners/partner1.svg" alt="Partner" class="partner-logo">
    </div>
    <h5>Partner Name</h5>
  </div>
  <!-- More partner items -->
</div>
```

**CSS Implementation**:
```css
.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.partner-item {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.partner-logo-container {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.partner-logo {
  max-width: 80%;
  max-height: 80px;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.partner-item:hover .partner-logo {
  filter: grayscale(0%);
  opacity: 1;
}
```

## Dependencies
### External Libraries (CDN links)
```html
<!-- Bootstrap 5 -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- AOS Animations -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Font Awesome (for icons) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
```

### Critical Implementation Patterns
1. **Dynamic Content Loading**:
   ```javascript
   // Example from include-html.js
   function loadHTML(elementId, filePath) {
     fetch(filePath)
       .then(response => response.text())
       .then(html => {
         const element = document.getElementById(elementId);
         element.innerHTML = html;
         // Reinitialize scripts in loaded content
       });
   }
   ```

2. **Responsive Grid System**:
   ```css
   /* Example grid from sections.css */
   .team-grid {
     display: grid;
     grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
     gap: 2rem;
     margin: 2rem 0;
   }
   ```

## File Structure Deep Dive
```
root/
├── css/
│   ├── core.css       # 412 lines - Base variables and element styles
│   ├── components.css # 1339 lines - Buttons, cards, forms, etc.
│   └── sections.css   # 2085 lines - Page-specific sections
├── js/
│   ├── include-html.js # 43 lines - Dynamic content loader
│   └── navigation.js  # 97 lines - Mobile menu logic
├── images/
│   ├── team/          # 15+ team member photos
│   ├── partners/      # 8 partner logos
│   └── hero-images/   # 4 hero background variants
└── *.html             # 12 pages with consistent structure
```

## Core Components (Detailed)
### 1. Responsive Layout System
- **Breakpoints**:
  ```css
  @media (min-width: 576px) { /* Tablet */ }
  @media (min-width: 768px) { /* Small desktop */ }
  @media (min-width: 992px) { /* Large desktop */ }
  ```

- **Grid Examples**:
  - Team grid: 300px min columns
  - Partner grid: 4 columns → 2 → 1 on mobile

### 2. Dynamic Content Loading
- **Implementation**:
  ```javascript
  // In page HTML:
  <div id="header-placeholder"></div>
  
  // In script:
  loadHTML('header-placeholder', '_header.html');
  ```

### 3. Form Handling
- **Features**:
  - Password strength indicator
  - Field validation
  - Loading states
  ```javascript
  // Example from script.js
  document.querySelectorAll('.password-toggle').forEach(toggle => {
    toggle.addEventListener('click', () => {
      const input = toggle.previousElementSibling;
      input.type = input.type === 'password' ? 'text' : 'password';
    });
  });
  ```

## Complete CSS Variable Reference
```css
/* core.css variables */
:root {
  /* Colors */
  --primary-blue: #00509e;
  --primary-blue-dark: #003d7a;
  --secondary-teal: #00a896;
  
  /* Typography */
  --font-family: 'Lato', sans-serif;
  
  /* Spacing */
  --border-radius: 4px;
  --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  
  /* Transitions */
  --transition-speed: 0.3s;
}
```

## Deployment Checklist
1. Compress all images (current total: ~4.2MB)
2. Minify CSS/JS (current sizes):
   - CSS: 3.8KB + 13.4KB + 48.6KB
   - JS: 16.5KB + 4.4KB
3. Upload to any static host (Netlify, Vercel, etc.)
4. Set up 301 redirects for:
   - `/home` → `/index.html`
   - `/` → `/index.html`

## Testing Protocol
1. **Cross-browser Testing**:
   - Chrome, Firefox, Safari, Edge
   - Mobile Safari (iOS), Chrome (Android)

2. **Responsive Checks**:
   - iPhone SE (320px)
   - iPad (768px)
   - Desktop (1200px+)

3. **Form Validation**:
   - All required fields
   - Email format validation
   - Password matching

## Known Issues & Solutions
1. **Mobile Menu JS**:
   - Sometimes fails to initialize
   - Fix: Add DOMContentLoaded event listener

2. **Form Submission**:
   - Currently no backend integration
   - Solution: Add Netlify Forms or Formspree

## Complete Recreation Steps
1. Create base HTML structure
2. Add CSS architecture with 3 files
3. Implement dynamic content loading
4. Build responsive grids
5. Add form handling logic
6. Optimize assets
7. Deploy to static host

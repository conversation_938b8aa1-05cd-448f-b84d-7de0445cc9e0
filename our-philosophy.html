<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/our-philosophy.html">
    <title>Our Philosophy & Values | NP Labs Compounding Pharmacy</title>
    <meta name="description" content="Discover NP Labs' philosophy of personalized medicine and compounding excellence. Learn about our commitment to quality, innovation, and patient-centered care in our Athens-based pharmacy.">
    <meta name="keywords" content="compounding pharmacy philosophy, personalized medicine approach, pharmaceutical quality standards, patient-centered care, compounding values, NP Labs mission, healthcare innovation">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Our Philosophy & Values | NP Labs Compounding Pharmacy">
    <meta property="og:description" content="Discover NP Labs' philosophy of personalized medicine. Learn about our commitment to quality, innovation, and patient-centered care.">
    <meta property="og:image" content="https://www.nplabs.com/images/nplabs-philosophy-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/our-philosophy.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Our Philosophy & Values | NP Labs Compounding Pharmacy">
    <meta name="twitter:description" content="Discover NP Labs' philosophy of personalized medicine. Learn about our commitment to quality, innovation, and patient-centered care.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/nplabs-philosophy-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Basic styling for philosophy principles - can be moved to style.css */
        .philosophy-section {
            padding: 60px 0;
            background-color: #f9f9f9;
        }
        .philosophy-intro {
            margin-bottom: 50px;
        }
        .philosophy-principles {
            margin-top: 40px;
        }
        .philosophy-principles h3 {
            color: var(--primary-blue);
            margin-bottom: 20px;
        }
        .philosophy-principles ol {
            list-style: none;
            padding-left: 0;
            counter-reset: principle-counter;
        }
        .philosophy-principles li {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            padding-left: 50px;
            counter-increment: principle-counter;
        }
        .philosophy-principles li::before {
            content: counter(principle-counter);
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--secondary-teal);
            color: white;
            font-weight: bold;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1em;
        }
        .philosophy-hero {
             background: url('images/philosophy-hero-background.jpg') no-repeat center center/cover;
        }
        .philosophy-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .philosophy-content {
            grid-column: 1;
        }
        .philosophy-image {
            grid-column: 2;
        }
        .rounded-image {
            border-radius: 8px;
        }
        .shadow-sm {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header philosophy-hero">
        <div class="page-header-overlay"></div>
        <div class="container">
            <h1 data-aos="fade-up" style="color: white;">Our Philosophy</h1>
            <p data-aos="fade-up" data-aos-delay="200">Guiding Principles for Optimal Health</p>
        </div>
    </section>

    <!-- Philosophy Content Section -->
    <section class="content-section philosophy-section">
        <div class="container">
            <div class="philosophy-intro text-center" data-aos="fade-up">
                <h2>Our <span class="highlight">Core Philosophy</span></h2>
                <div class="section-divider"><span><i class="fas fa-lightbulb"></i></span></div>
            </div>

            <div class="philosophy-grid" data-aos="fade-up" data-aos-delay="100">
                <div class="philosophy-content">
                    <p class="lead-text">It’s not about the symptoms. It’s about the causes. We fight the causes of disease for each person individually. We strive for optimal wellness and health state by focusing on each patient’s specific genetic, biochemical and environmental factors. And we achieve it by offering effective compounding products of excellent quality and safety for each patient and each specific cause.</p>
                </div>
                <div class="philosophy-image">
                    <img src="images/filosofy.png" alt="Our Philosophy Illustration" class="rounded-image shadow-sm">
                </div>
            </div>

            <div class="philosophy-principles" data-aos="fade-up" data-aos-delay="200">
                <h3>NP Labs Principles</h3>
                <ol>
                    <li>Each of us is different and unique. So are our cells and their treatment.</li>
                    <li>Each one of us has a unique biochemical composition; thus we have unique “built-in” defense and self-healing mechanism that we can activate for our own treatment.</li>
                    <li>We accept and serve the belief that our body can heal or prevent all diseases.</li>
                    <li>Being healthy and really well is a physical state and not a result of just not being sick.</li>
                </ol>
            </div>
        </div>
    </section>
</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>
<script src="scripts.js"></script>
<script src="js/include-html.js" defer></script>

</body>
</html>

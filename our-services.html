<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/our-services.html">
    <title>Custom Compounding Services & Specialized Medications - NP Labs</title>
    <meta name="description" content="NP Labs offers specialized compounding services including custom medications, hormone therapy, pain management solutions, and more. Get personalized pharmaceutical care in Athens, Greece.">
    <meta name="keywords" content="compounding services, custom medications, hormone therapy, pain management, specialized pharmacy services">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Custom Compounding Services & Specialized Medications - NP Labs">
    <meta property="og:description" content="NP Labs offers specialized compounding services including custom medications, hormone therapy, pain management solutions, and more.">
    <meta property="og:image" content="https://www.nplabs.com/images/og-image.jpg">
    <meta property="og:url" content="https://www.nplabs.com/our-services.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Custom Compounding Services & Specialized Medications - NP Labs">
    <meta name="twitter:description" content="NP Labs offers specialized compounding services including custom medications, hormone therapy, pain management solutions, and more.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/twitter-image.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles (can be moved to style.css) */
        .services-hero {
            background: url('images/services-hero-background.jpg') no-repeat center center/cover; /* Add a relevant hero image */
            padding: 0rem 0; /* Updated padding */
            text-align: center;
            position: relative;
        }
        .services-hero .page-header-overlay {
            background-color: rgba(0, 80, 158, 0.6); /* Dark blue overlay */
        }
        /* Ensure heading and paragraph text within the hero are white */
        .services-hero h1,
        .services-hero p.lead-text {
            color: #ffffff !important; /* Use !important to ensure override if necessary */
        }
        .services-section {
            padding: 60px 0;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        .service-card {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 80, 158, 0.15);
        }
        .service-card i {
            font-size: 2.5em;
            color: var(--primary-color); /* Use theme color */
            margin-bottom: 20px;
        }
        .service-card h4 {
            color: var(--secondary-color); /* Use theme color */
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        .service-card p {
            font-size: 0.95em;
            line-height: 1.6;
            color: #555;
        }
         .section-divider {
            margin: 20px auto 40px; /* Center divider */
        }
        /* Remove default link styling */
        a.service-card-link {
            display: block; /* Make the link fill the grid cell */
            height: 100%; /* Ensure link takes full height of the cell */
            text-decoration: none;
            color: inherit; /* Inherit text color from parent */
            cursor: pointer;
        }

        /* Ensure the card div fills the anchor tag */
        a.service-card-link > .service-card {
            height: 100%;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        /* Learn more indicator */
        .service-card {
            position: relative;
            overflow: hidden;
        }

        .service-card::after {
            content: 'Learn more →';
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 0.85rem;
            color: var(--primary-blue);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        a.service-card-link:hover .service-card {
            /* Enhanced hover effect */
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 80, 158, 0.15);
            border-bottom: 3px solid var(--primary-blue);
        }

        a.service-card-link:hover .service-card::after {
            opacity: 1;
            transform: translateY(0);
        }

        a.service-card-link:hover .service-card h4 {
            color: var(--primary-blue-dark);
        }

        .service-card h4 {
            transition: color 0.3s ease;
        }

        /* Add a bit more padding at the bottom to accommodate the 'Learn more' text */
        .service-card p {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header services-hero">
        <div class="page-header-overlay"></div>
        <div class="container position-relative">
            <h1 data-aos="fade-up">Our Services</h1>
            <p class="lead-text" data-aos="fade-up" data-aos-delay="200">Tailored Compounding Solutions for Your Well-being</p>
        </div>
    </section>

    <!-- Services Content Section -->
    <section class="content-section services-section">
        <div class="container">
            <div class="text-center" data-aos="fade-up">
                <h2>Comprehensive <span class="highlight">Personalized Care</span></h2>
                 <div class="section-divider"><span><i class="fas fa-cogs"></i></span></div>
                <p class="lead-text">NPLabs provides a wide array of specialized compounding services designed to meet diverse health needs with precision and care.</p>
            </div>

            <div class="service-grid">
                <!-- Men's Health -->
                <a href="mens-health.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up">
                        <i class="fas fa-male"></i>
                        <h4>Men’s Health</h4>
                        <p>We deliver quality compounded medication for men's health, including testosterone replacement therapy, erectile dysfunction treatments, and more. Custom-blended formulas tailored to your unique needs.</p>
                    </div>
                </a>

                <!-- Women's Health -->
                <a href="womens-health.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="100">
                        <i class="fas fa-female"></i>
                        <h4>Women's Health</h4>
                        <p>Custom compounding solutions for hormone balance, menopause, sexual health, and other specific female wellness needs.</p>
                    </div>
                </a>

                <!-- Customized Multi-Vitamins -->
                <a href="custom-vitamins.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="200">
                        <i class="fas fa-pills"></i>
                        <h4>Customized Multi-Vitamins</h4>
                        <p>Customize your multivitamins with our help. We can add any ingredient you want, allowing you to get exactly what you need!</p>
                    </div>
                </a>

                <!-- Peptides -->
                <a href="peptides.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="300">
                        <i class="fas fa-dna"></i>
                        <h4>Peptides</h4>
                        <p>We offer peptides in both oral-spray and injectable forms so that you can choose which option is best for your needs.</p>
                    </div>
                </a>

                <!-- Low Dose Naltrexone -->
                <a href="ldn.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up">
                        <i class="fas fa-prescription-bottle-alt"></i>
                        <h4>Low Dose Naltrexone (LDN)</h4>
                        <p>When you're looking for a low-dose naltrexone compounding pharmacy, we've got you covered.</p>
                    </div>
                </a>

                <!-- Pain Management -->
                <a href="pain-management.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="100">
                        <i class="fas fa-band-aid"></i>
                        <h4>Pain Management</h4>
                        <p>A wide range of medications to help manage pain. We offer short-term and long-term solutions and work with you to find the best fit.</p>
                    </div>
                </a>

                <!-- Pediatric-Care -->
                <a href="pediatric-care.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="200">
                        <i class="fas fa-child"></i>
                        <h4>Pediatric Care</h4>
                        <p>We offer an extensive list of compounding pharmacy medications specifically formulated for children.</p>
                    </div>
                </a>

                <!-- Dermatology -->
                <a href="dermatology.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="300">
                        <i class="fas fa-spa"></i>
                        <h4>Dermatology</h4>
                        <p>Committed to providing you with the highest-quality dermatology products and custom prescriptions.</p>
                    </div>
                </a>

                <!-- Ophthalmology -->
                <a href="ophthalmology.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up">
                        <i class="fas fa-eye"></i>
                        <h4>Ophthalmology</h4>
                        <p>Discover our sterile compounded ophthalmic medications tailored for various eye conditions.</p>
                    </div>
                </a>

                <!-- Thyroid-Support -->
                <a href="thyroid-support.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="100">
                        <i class="fas fa-heartbeat"></i>
                        <h4>Thyroid Support</h4>
                        <p>Explore tailored thyroid compounding options to optimize your thyroid health and well-being.</p>
                    </div>
                </a>

                <!-- Dental-Care -->
                <a href="dental-care.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="200">
                        <i class="fas fa-tooth"></i>
                        <h4>Dental Care</h4>
                        <p>Our mission is to help you achieve a healthy smile. We offer comprehensive compounding pharmacy services, including prescription and over-the-counter medications.</p>
                    </div>
                </a>

                <!-- Veterinary Compounding -->
                <a href="veterinary-compounding.html" class="service-card-link">
                    <div class="service-card" data-aos="fade-up" data-aos-delay="300">
                        <i class="fas fa-paw"></i>
                        <h4>Veterinary Compounding</h4>
                        <p>Tailored medications for pets in easy-to-administer forms and flavors.</p>
                    </div>
                </a>

                <!-- Sports Medicine -->
                <a href="sports-medicine.html" class="service-card-link"> <!-- Link to future page -->
                    <div class="service-card" data-aos="fade-up" data-aos-delay="400">
                        <i class="fas fa-running"></i>
                        <h4>Sports Medicine</h4>
                        <p>Customized formulations for athletes, addressing pain, inflammation, and nutritional needs.</p>
                    </div>
                </a>

            </div> <!-- End Service Grid -->

         </div> <!-- End Container -->
     </section>

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>
<script src="scripts.js"></script>
<script src="js/include-html.js" defer></script>
<script src="js/service-card-click.js"></script>

</body>
</html>

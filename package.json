{"name": "nplabs-blog-admin", "version": "1.0.0", "description": "NP Labs Blog Administration System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for static admin interface'", "test": "echo 'Tests not implemented yet'", "install-deps": "npm install", "setup": "npm run install-deps && echo 'Setup complete. Run npm start to begin.'", "netlify-dev": "netlify dev", "vercel-dev": "vercel dev"}, "keywords": ["blog", "admin", "cms", "static-site", "healthcare", "pharmacy"], "author": "NP Labs", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/MythGuru/nplabs.git"}, "homepage": "https://www.nplabs.com", "bugs": {"url": "https://github.com/MythGuru/nplabs/issues"}}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/pain-management.html">
    <title>Custom Pain Management Compounds & Solutions | NP Labs</title>
    <meta name="description" content="NP Labs creates personalized compounded medications for effective pain management, including topical treatments for knee pain, migraines, back pain, neuropathy, and chronic conditions.">
    <meta name="keywords" content="pain management, compounded pain medications, topical pain treatments, neuropathy, migraines, chronic pain, knee pain, back pain">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Custom Pain Management Compounds & Solutions | NP Labs">
    <meta property="og:description" content="NP Labs creates personalized compounded medications for effective pain management, including topical treatments for knee pain, migraines, back pain, and neuropathy.">
    <meta property="og:image" content="https://www.nplabs.com/images/pain-management-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/pain-management.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Custom Pain Management Compounds & Solutions | NP Labs">
    <meta name="twitter:description" content="NP Labs creates personalized compounded medications for effective pain management, including topical treatments for knee pain, migraines, back pain, and neuropathy.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/pain-management-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles - Adapted from custom-vitamins */
        .pain-hero { /* Updated class name */
            background: url('images/pain-management-hero.jpg') no-repeat center center/cover; /* Placeholder Image */
            padding: 0rem 0;
            text-align: center;
            position: relative;
        }
        .pain-hero .page-header-overlay {
            background-color: rgba(0, 80, 158, 0.6); /* Dark blue overlay */
        }
        .pain-hero h1,
        .pain-hero p.lead-text {
            color: #ffffff !important;
        }
        .content-section {
            padding: 60px 0;
        }
        .condition-list {
            list-style: none;
            padding-left: 0;
            column-count: 2; /* Display list in two columns on wider screens */
            column-gap: 40px;
            margin-top: 20px;
        }
        .condition-list li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
            break-inside: avoid-column; /* Prevent items breaking across columns */
        }
        .condition-list li::before {
            content: '\f058'; /* Font Awesome check-circle icon */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--secondary-color);
            position: absolute;
            left: 0;
            top: 5px;
        }
        .content-image {
             text-align: center;
             margin: 30px 0;
         }
        .content-image img {
            max-width: 60%; /* Adjust size as needed */
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        @media (max-width: 768px) {
            .condition-list {
                column-count: 1; /* Single column on smaller screens */
            }
             .content-image img {
                max-width: 90%;
             }
        }

        /* --- Visual Appeal Enhancements --- */
        .benefit-list { /* New class for the 'How Compounding...' list */
            list-style: none;
            padding-left: 0;
            margin-top: 15px;
        }
        .benefit-list li {
            position: relative;
            padding-left: 35px; /* Space for icon */
            margin-bottom: 15px; /* More spacing */
            line-height: 1.6;
        }
        .benefit-list li i { /* Icon styling */
            position: absolute;
            left: 0;
            top: 5px; /* Adjust vertical alignment */
            font-size: 1.4em; /* Slightly larger icon */
            color: var(--secondary-color); /* Use theme color */
            width: 25px; /* Ensure alignment */
            text-align: center;
        }

        .content-block { /* New class for content sections */
             background-color: #f9f9f9; /* Light grey background */
             padding: 25px;
             border-radius: 8px;
             margin-bottom: 30px;
             border-left: 5px solid var(--secondary-color); /* Accent border */
         }

        .pain-cta { /* Styling for the final call-to-action paragraph */
            background-color: var(--primary-color-light); /* Lighter blue background */
            color: var(--primary-color-dark); /* Darker blue text */
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 30px;
            border: 1px solid var(--primary-color);
        }
        .pain-cta strong {
             font-weight: 700; /* Make the text bold */
        }
        /* --- End Visual Appeal Enhancements --- */

        /* Adjust condition list styles slightly for consistency */
        .condition-list li::before {
            top: 5px; /* Align check icon similarly to new icons */
        }

    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header pain-hero"> <!-- Updated class -->
        <div class="page-header-overlay"></div>
        <div class="container position-relative">
            <h1 data-aos="fade-up">Pain Management</h1> <!-- Updated Title -->
            <p class="lead-text" data-aos="fade-up" data-aos-delay="200">Compounded Solutions for Various Pain Conditions</p> <!-- Updated Subtitle -->
        </div>
    </section>

    <!-- Main Content Section -->
    <section class="content-section">
        <div class="container">
            <div data-aos="fade-up">
                 <h2>Beyond One-Size-Fits-All: <span class="highlight">Personalized Pain Relief</span></h2>
                 <p>Finding effective pain relief can be challenging. Standard medications may not provide adequate relief, can cause unwanted side effects, or come in dosages that aren't quite right for you. NP Labs specializes in compounded pain management solutions, offering a personalized approach to help you manage your discomfort effectively.</p>
                 <hr class="section-divider">
            </div>

             <div class="content-image" data-aos="fade-up" data-aos-delay="100">
                 <img src="images/pain-management-main.jpg" alt="Personalized Pain Medication Concept"> <!-- Optional Placeholder Image -->
             </div>

             <div class="content-block" data-aos="fade-up" data-aos-delay="150"> <!-- Added class -->
                <h3>How Compounding Makes a Difference:</h3>
                <p>Working closely with your prescriber, our compounding pharmacists can:</p>
                <ul class="benefit-list"> <!-- Added class -->
                    <li><i class="fas fa-sliders-h"></i><strong>Customize Dosages:</strong> Tailor the strength of medication precisely to your needs, potentially minimizing side effects.</li>
                    <li><i class="fas fa-mortar-pestle"></i><strong>Combine Medications:</strong> Combine multiple compatible medications into a single preparation (like a topical cream or gel) for convenience and potentially enhanced effect.</li>
                    <li><i class="fas fa-spray-can-sparkles"></i><strong>Alternative Delivery Forms:</strong> Create medications in forms other than pills, such as topical creams, gels, or sprays. This can be particularly helpful for localized pain or when oral medications cause stomach upset. Topical applications can target pain directly at the source with potentially less systemic absorption and fewer side effects.</li>
                    <li><i class="fas fa-ban"></i><strong>Remove Problematic Ingredients:</strong> Formulate medications without dyes, preservatives, or fillers that may cause allergic reactions.</li>
                </ul>
             </div>


            <div class="content-block" data-aos="fade-up" data-aos-delay="200"> <!-- Added class -->
                <h3>Common Conditions Addressed with Compounded Therapies:</h3>
                <ul class="condition-list">
                    <li>Knee pain (Arthritis, Injury)</li>
                    <li>Migraines & Headaches</li>
                    <li>Ankle & Foot Pain (Plantar Fasciitis)</li>
                    <li>Back & Neck Pain</li>
                    <li>Diabetic Neuropathy</li>
                    <li>Shoulder Pain (Rotator Cuff)</li>
                    <li>Tennis/Golfer's Elbow</li>
                    <li>Muscle Spasms & Cramps</li>
                    <li>Fibromyalgia</li>
                    <li>Shingles Pain</li>
                    <!-- Add more relevant conditions if applicable -->
                </ul>
                <p class="mt-3">Compounded therapies often utilize combinations of ingredients like NSAIDs (e.g., ketoprofen), anesthetics (e.g., lidocaine), nerve agents (e.g., gabapentin), and muscle relaxants tailored to the specific type of pain.</p>
                <p class="pain-cta"><strong>Let us work with you and your healthcare provider to find a compounded solution that targets your specific pain needs.</strong></p> <!-- Added class -->
            </div>

        </div> <!-- End Container -->
    </section> <!-- End Content Section -->

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>
<script src="js/include-html.js" defer></script>

</body>
</html>

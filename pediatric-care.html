<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/pediatric-care.html">
    <title>Pediatric Compounding Services | Child-Friendly Medications | NP Labs</title>
    <meta name="description" content="NP Labs creates gentle, personalized compounded medications for children with flavor options, alternative dosage forms, custom strengths, and allergy-friendly formulations.">
    <meta name="keywords" content="pediatric compounding, children's medications, flavored medications, custom dosages for children, allergy-friendly medications, pediatric care">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Pediatric Compounding Services | Child-Friendly Medications | NP Labs">
    <meta property="og:description" content="NP Labs creates gentle, personalized compounded medications for children with flavor options, alternative dosage forms, custom strengths, and allergy-friendly formulations.">
    <meta property="og:image" content="https://www.nplabs.com/images/pediatric-care-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/pediatric-care.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Pediatric Compounding Services | Child-Friendly Medications | NP Labs">
    <meta name="twitter:description" content="NP Labs creates gentle, personalized compounded medications for children with flavor options, alternative dosage forms, custom strengths, and allergy-friendly formulations.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/pediatric-care-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles */
        .pediatric-hero {
            background: url('images/pediatric-hero.webp') no-repeat center center/cover; /* Placeholder - Add image */
            padding: 0; /* Set padding to 0 */
            text-align: center;
            position: relative;
        }
        .pediatric-hero .page-header-overlay {
            background-color: rgba(0, 80, 158, 0.6); /* Consistent overlay */
        }
        .pediatric-hero h1,
        .pediatric-hero p.lead-text {
            color: #ffffff !important;
        }
        .content-section {
            padding: 60px 0;
        }
        /* Consistent Service Detail Section Styling */
        .service-detail-section {
            margin-bottom: 40px;
            padding: 25px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 5px solid var(--secondary-color);
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        }
        .service-detail-section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--secondary-color);
            display: inline-block;
        }
         .service-detail-section h3 i {
             margin-right: 10px;
        }
         .service-detail-section ul {
             list-style: none;
             padding-left: 0;
             margin-top: 15px;
        }
          .service-detail-section li {
             margin-bottom: 10px;
             padding-left: 25px;
             position: relative;
             line-height: 1.6;
         }
         .service-detail-section li::before {
             content: '\f058'; /* Font Awesome check-circle icon */
             font-family: 'Font Awesome 6 Free';
             font-weight: 900;
             color: var(--secondary-color);
             position: absolute;
             left: 0;
             top: 1px;
         }
        /* Consistent CTA Style */
        .pediatric-cta {
            background-color: var(--primary-color-light);
            color: var(--primary-color-dark);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 40px;
            margin-bottom: 20px;
            border: 1px solid var(--primary-color);
        }
        .pediatric-cta strong {
             font-weight: 700;
        }
    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header pediatric-hero">
        <div class="page-header-overlay"></div>
        <div class="container position-relative">
            <h1 data-aos="fade-up">Pediatric Care</h1>
            <p class="lead-text" data-aos="fade-up" data-aos-delay="200">Gentle, Personalized Compounding for Your Child's Health</p>
        </div>
    </section>

    <!-- Main Content Section -->
    <section class="content-section">
        <div class="container">
            <div class="text-center" data-aos="fade-up">
                 <h2>Meeting the Unique Needs of <span class="highlight">Young Patients</span></h2>
                 <p class="section-intro">Administering medication to children can present unique challenges. Commercial medications may not always come in the right strength, dosage form, or flavor for a child. NP Labs specializes in pediatric compounding to create customized medications that are easier for children to take, ensuring they receive the treatment they need in a way that works best for them.</p>
                 <hr class="section-divider">
            </div>

            <!-- Flavor Masking -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="100">
                 <h3><i class="fas fa-ice-cream"></i> Flavor Masking</h3>
                 <p>Many children refuse medication due to unpleasant taste. We can mask bitter flavors by adding appealing, sugar-free options like bubblegum, grape, or cherry, making medicine time less stressful for both children and parents.</p>
                 <ul>
                    <li>Improved compliance with prescribed treatments.</li>
                    <li>Wide range of kid-friendly flavors available.</li>
                    <li>Sugar-free, dye-free options upon request.</li>
                 </ul>
            </div>

            <!-- Alternative Dosage Forms -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="200">
                 <h3><i class="fas fa-capsules"></i> Alternative Dosage Forms</h3>
                 <p>If a child has difficulty swallowing pills or capsules, we can often reformulate the medication into a more suitable format.</p>
                 <ul>
                    <li>Oral liquids and suspensions.</li>
                    <li>Chewable troches or gummy treats.</li>
                    <li>Lollipops for prolonged absorption.</li>
                    <li>Topical creams or gels.</li>
                    <li>Suppositories when oral administration isn't possible.</li>
                 </ul>
            </div>

            <!-- Custom Dosage Strengths -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="300">
                 <h3><i class="fas fa-balance-scale"></i> Custom Dosage Strengths</h3>
                 <p>Children's medication dosages are often based on weight, requiring precise amounts that might not be commercially available. We compound medications to the exact strength prescribed by the pediatrician, eliminating the need for caregivers to split or estimate doses.</p>
                 <ul>
                    <li>Accurate dosing tailored to individual needs.</li>
                    <li>Avoids potential errors from splitting tablets.</li>
                    <li>Ideal for gradual dosage adjustments.</li>
                 </ul>
            </div>

            <!-- Allergy-Friendly Formulations -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="400">
                 <h3><i class="fas fa-allergies"></i> Allergy-Friendly Formulations</h3>
                 <p>Commercial medications often contain fillers, dyes, preservatives, or binders that can cause allergic reactions in sensitive children. We can compound medications free from specific ingredients like gluten, lactose, dyes, or sugar.</p>
                 <ul>
                    <li>Avoidance of known allergens and irritants.</li>
                    <li>Suitable for children with dietary restrictions or sensitivities.</li>
                    <li>Consultation available to identify potential ingredient concerns.</li>
                 </ul>
            </div>

            <!-- CTA Section -->
            <div class="pediatric-cta text-center" data-aos="fade-up">
                <p><strong>Need a child-friendly medication solution? Contact NP Labs to learn how our pediatric compounding services can help your little one.</strong></p>
            </div>

        </div> <!-- End Container -->
    </section> <!-- End Content Section -->

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>
<script src="js/include-html.js" defer></script>

</body>
</html>

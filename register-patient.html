<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/register-patient.html">
    <title>Patient Registration | NP Labs Compounding Pharmacy</title>
    <meta name="description" content="Register as a patient with NP Labs compounding pharmacy in Athens. Access personalized compounded medications, manage your prescriptions online, and receive customized treatment solutions.">
    <meta name="keywords" content="patient registration, patient account, compounding pharmacy patient, personalized medication, prescription management, NP Labs patient portal">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Patient Registration | NP Labs Compounding Pharmacy">
    <meta property="og:description" content="Register as a patient with NP Labs. Access personalized compounded medications and manage your prescriptions online.">
    <meta property="og:image" content="https://www.nplabs.com/images/nplabs-patient-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/register-patient.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Patient Registration | NP Labs Compounding Pharmacy">
    <meta name="twitter:description" content="Register as a patient with NP Labs. Access personalized compounded medications and manage your prescriptions online.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/nplabs-patient-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="stylesheet" href="register.css">
    <link rel="stylesheet" href="css/form-improvements.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
</head>
<body>

    <div id="header-placeholder"></div> <!-- Header will be loaded here -->

    <main id="main-content" class="form-page">
        <section class="register-section">
            <div class="register-container">
                <!-- Left side - Branding -->
                <div class="register-brand-panel">
                    <div class="register-brand-content">
                        <div class="register-logo">
                            <img src="nplabslogo.svg" alt="NP Labs Logo">
                        </div>
                        <h1 class="register-brand-title">Patient Registration</h1>
                        <p class="register-brand-subtitle">Join thousands of patients who trust NP Labs for their personalized medicine needs</p>
                        <div class="register-benefits">
                            <div class="register-benefit">
                                <i class="fas fa-pills"></i>
                                <span>Personalized Medications</span>
                            </div>
                            <div class="register-benefit">
                                <i class="fas fa-user-shield"></i>
                                <span>Secure Patient Portal</span>
                            </div>
                            <div class="register-benefit">
                                <i class="fas fa-clipboard-check"></i>
                                <span>Prescription Management</span>
                            </div>
                        </div>
                    </div>
                    <div class="register-brand-overlay"></div>
                </div>

                <!-- Right side - Registration Form -->
                <div class="register-form-panel">
                    <div class="register-card">
                        <div class="register-header">
                            <h1>Create Patient Account</h1>
                            <p class="register-description">Fill in your details to get started with personalized medicine</p>
                        </div>

                        <!-- Progress Steps -->
                        <div class="progress-steps">
                            <div class="progress-step active">
                                <div class="step-number">1</div>
                                <div class="step-label">Personal</div>
                            </div>
                            <div class="progress-step">
                                <div class="step-number">2</div>
                                <div class="step-label">Contact</div>
                            </div>
                            <div class="progress-step">
                                <div class="step-number">3</div>
                                <div class="step-label">Security</div>
                            </div>
                        </div>

                        <form class="register-form" id="patient-registration-form" novalidate>
                            <fieldset>
                                <legend>Personal Information</legend>
                                <div class="form-group">
                                    <label for="firstName">First Name</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="firstName" name="firstName" placeholder="Enter your first name" required>
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <small id="firstName-error" class="form-error-message"></small>
                                </div>

                                <div class="form-group">
                                    <label for="lastName">Last Name</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="lastName" name="lastName" placeholder="Enter your last name" required>
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <small id="lastName-error" class="form-error-message"></small>
                                </div>

                                <div class="form-group">
                                    <label for="dob">Date of Birth</label>
                                    <div class="input-with-icon">
                                        <input type="date" id="dob" name="dob" required>
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <small id="dob-error" class="form-error-message"></small>
                                </div>
                            </fieldset>

                            <fieldset>
                                <legend>Contact Information</legend>
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <div class="input-with-icon">
                                        <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <small id="email-error" class="form-error-message"></small>
                                </div>

                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <div class="input-with-icon">
                                        <input type="tel" id="phone" name="phone" placeholder="Enter your phone number" required>
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <small id="phone-error" class="form-error-message"></small>
                                </div>
                            </fieldset>

                            <fieldset>
                                <legend>Account Security</legend>
                                <div class="form-group">
                                    <label for="password">Password</label>
                                    <div class="input-with-icon password-input-wrapper">
                                        <input type="password" id="password" name="password" placeholder="Create a strong password" required>
                                        <i class="fas fa-lock"></i>
                                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength">
                                        <div class="password-strength-bar"></div>
                                    </div>
                                    <div class="password-strength-text">Password strength: <span id="password-strength-value">None</span></div>
                                    <small id="password-error" class="form-error-message"></small>
                                </div>

                                <div class="form-group">
                                    <label for="confirmPassword">Confirm Password</label>
                                    <div class="input-with-icon password-input-wrapper">
                                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                                        <i class="fas fa-lock"></i>
                                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                    <small id="confirmPassword-error" class="form-error-message"></small>
                                </div>
                            </fieldset>

                            <div class="checkbox">
                                <input type="checkbox" id="terms" name="terms" required>
                                <label for="terms">I agree to the <a href="terms-of-use.html" target="_blank">Terms of Service</a> and <a href="privacy-policy.html" target="_blank">Privacy Policy</a></label>
                            </div>
                            <small id="terms-error" class="form-error-message"></small>

                            <button type="submit" class="register-button">
                                <span>Create Account</span>
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </form>

                        <p class="login-prompt">Already have an account? <a href="login.html">Sign In</a></p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div> <!-- Footer will be loaded here -->

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>
    <script src="scripts.js"></script>
    <script src="js/include-html.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('patient-registration-form');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const passwordStrengthBar = document.querySelector('.password-strength-bar');
            const passwordStrengthText = document.getElementById('password-strength-value');
            const togglePasswordBtns = document.querySelectorAll('.toggle-password');
            const progressSteps = document.querySelectorAll('.progress-step');
            const fieldsets = document.querySelectorAll('fieldset');

            // Toggle password visibility
            togglePasswordBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const input = this.closest('.password-input-wrapper').querySelector('input');
                    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                    input.setAttribute('type', type);

                    // Toggle icon
                    const icon = this.querySelector('i');
                    if (type === 'text') {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });

            // Password strength meter
            passwordInput.addEventListener('input', function() {
                const value = this.value;
                const strength = calculatePasswordStrength(value);

                // Update strength bar
                passwordStrengthBar.style.width = strength.percent + '%';
                passwordStrengthBar.style.backgroundColor = strength.color;
                passwordStrengthText.textContent = strength.label;
            });

            // Multi-step form navigation (simplified for demo)
            let currentStep = 0;

            // For demo purposes, clicking on a fieldset will activate its step
            fieldsets.forEach((fieldset, index) => {
                fieldset.addEventListener('click', function() {
                    if (index !== currentStep) {
                        updateProgressStep(index);
                    }
                });
            });

            function updateProgressStep(stepIndex) {
                // Update progress steps
                progressSteps.forEach((step, index) => {
                    if (index < stepIndex) {
                        step.classList.remove('active');
                        step.classList.add('completed');
                    } else if (index === stepIndex) {
                        step.classList.add('active');
                        step.classList.remove('completed');
                    } else {
                        step.classList.remove('active', 'completed');
                    }
                });

                currentStep = stepIndex;
            }

            // Form validation
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                let isValid = true;

                // Reset previous errors
                document.querySelectorAll('.form-error-message').forEach(el => el.textContent = '');
                document.querySelectorAll('input').forEach(el => el.classList.remove('is-invalid'));

                // Validate first name
                const firstName = document.getElementById('firstName');
                if (!firstName.value.trim()) {
                    firstName.classList.add('is-invalid');
                    document.getElementById('firstName-error').textContent = 'First name is required';
                    isValid = false;
                }

                // Validate last name
                const lastName = document.getElementById('lastName');
                if (!lastName.value.trim()) {
                    lastName.classList.add('is-invalid');
                    document.getElementById('lastName-error').textContent = 'Last name is required';
                    isValid = false;
                }

                // Validate date of birth
                const dob = document.getElementById('dob');
                if (!dob.value) {
                    dob.classList.add('is-invalid');
                    document.getElementById('dob-error').textContent = 'Date of birth is required';
                    isValid = false;
                }

                // Validate email
                const email = document.getElementById('email');
                if (!email.value.trim()) {
                    email.classList.add('is-invalid');
                    document.getElementById('email-error').textContent = 'Email address is required';
                    isValid = false;
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value.trim())) {
                    email.classList.add('is-invalid');
                    document.getElementById('email-error').textContent = 'Please enter a valid email address';
                    isValid = false;
                }

                // Validate phone
                const phone = document.getElementById('phone');
                if (!phone.value.trim()) {
                    phone.classList.add('is-invalid');
                    document.getElementById('phone-error').textContent = 'Phone number is required';
                    isValid = false;
                }

                // Validate password
                if (!passwordInput.value) {
                    passwordInput.classList.add('is-invalid');
                    document.getElementById('password-error').textContent = 'Password is required';
                    isValid = false;
                } else if (passwordInput.value.length < 8) {
                    passwordInput.classList.add('is-invalid');
                    document.getElementById('password-error').textContent = 'Password must be at least 8 characters';
                    isValid = false;
                }

                // Validate confirm password
                if (!confirmPasswordInput.value) {
                    confirmPasswordInput.classList.add('is-invalid');
                    document.getElementById('confirmPassword-error').textContent = 'Please confirm your password';
                    isValid = false;
                } else if (confirmPasswordInput.value !== passwordInput.value) {
                    confirmPasswordInput.classList.add('is-invalid');
                    document.getElementById('confirmPassword-error').textContent = 'Passwords do not match';
                    isValid = false;
                }

                // Validate terms
                const terms = document.getElementById('terms');
                if (!terms.checked) {
                    document.getElementById('terms-error').textContent = 'You must agree to the Terms of Service and Privacy Policy';
                    isValid = false;
                }

                if (!isValid) {
                    // Add shake animation to form
                    form.classList.add('shake');
                    setTimeout(() => {
                        form.classList.remove('shake');
                    }, 500);
                } else {
                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
                    submitBtn.disabled = true;

                    // Simulate form submission (remove in production)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        alert('Account created successfully! You would be redirected to login page.');
                        // window.location.href = 'login.html';
                    }, 2000);
                }
            });

            // Helper function to calculate password strength
            function calculatePasswordStrength(password) {
                if (!password) {
                    return { percent: 0, label: 'None', color: '#e0e0e0' };
                }

                let strength = 0;

                // Length check
                if (password.length >= 8) strength += 25;

                // Contains lowercase
                if (/[a-z]/.test(password)) strength += 25;

                // Contains uppercase
                if (/[A-Z]/.test(password)) strength += 25;

                // Contains number or special char
                if (/[0-9!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 25;

                // Determine label and color
                let label, color;
                if (strength <= 25) {
                    label = 'Weak';
                    color = '#ff4d4d';
                } else if (strength <= 50) {
                    label = 'Fair';
                    color = '#ffa64d';
                } else if (strength <= 75) {
                    label = 'Good';
                    color = '#ffff4d';
                } else {
                    label = 'Strong';
                    color = '#4dff4d';
                }

                return { percent: strength, label, color };
            }
        });
    </script>

    <style>
        /* Additional animations */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
        }
    </style>
</body>
</html>

/* Enhanced Registration Page Styles */

/* Main Registration Section */
.register-section {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.register-container {
    display: flex;
    flex-direction: column; /* Mobile first - stack vertically */
    width: 100%;
    max-width: 1200px;
    min-height: 600px;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 1rem; /* Smaller margin for mobile */
}

/* Responsive register container */
@media (min-width: 768px) {
    .register-container {
        flex-direction: row; /* Side by side on tablets and up */
        margin: 2rem;
    }
}

/* Left Panel - Branding */
.register-brand-panel {
    flex: 1;
    background-image: url('images/register-bg.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.register-brand-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

.register-brand-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 400px;
}

.register-logo {
    margin-bottom: 2rem;
}

.register-logo img {
    height: 60px;
    filter: brightness(0) invert(1);
}

.register-brand-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.register-brand-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.register-benefits {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: left;
}

.register-benefit {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.register-benefit:hover {
    transform: translateX(5px);
}

.register-benefit i {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Right Panel - Form */
.register-form-panel {
    flex: 1;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
}

.register-card {
    width: 100%;
    max-width: 500px;
    background-color: transparent;
    box-shadow: none;
    padding: 0;
    margin: 0;
}

.register-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.register-header h1 {
    font-size: 2.2rem;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.register-description {
    color: var(--dark-grey);
    font-size: 1.1rem;
}

/* Account Type Selection */
.register-options-container {
    margin-bottom: 2rem;
}

.register-options-heading {
    text-align: center;
    margin-bottom: 1.5rem;
}

.register-options-heading h3 {
    font-size: 1.4rem;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.register-options-heading p {
    color: var(--dark-grey);
    font-size: 1rem;
}

.register-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.register-option-card {
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-decoration: none;
    height: 100%;
    border: 2px solid transparent;
}

.register-option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-blue);
}

.option-card-header {
    padding: 1.5rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.option-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.patient-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.prescriber-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.option-icon i {
    font-size: 2.5rem;
    color: white;
}

.register-option-card:hover .option-icon {
    transform: scale(1.1) rotate(5deg);
}

.option-card-header h4 {
    font-size: 1.5rem;
    color: var(--dark-grey);
    margin: 0;
    font-weight: 700;
}

.option-card-body {
    padding: 0 1.5rem 1.5rem;
    flex-grow: 1;
}

.option-card-body p {
    color: var(--dark-grey);
    margin-bottom: 1.2rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.option-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.option-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: var(--dark-grey);
    font-size: 0.95rem;
}

.option-features li i {
    color: var(--secondary-teal);
    font-size: 1rem;
}

.option-card-footer {
    padding: 1.2rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: center;
    transition: all 0.3s ease;
}

.register-option-card:hover .option-card-footer {
    background-color: var(--primary-blue);
}

.btn-select {
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.register-option-card:hover .btn-select {
    color: white;
}

.btn-select i {
    transition: transform 0.3s ease;
}

.register-option-card:hover .btn-select i {
    transform: translateX(5px);
}

/* Options Footer */
.register-options-footer {
    margin-top: 1rem;
}

.options-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: var(--dark-grey);
    font-size: 0.9rem;
}

.options-divider::before,
.options-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
}

.options-divider span {
    padding: 0 1rem;
}

.contact-support {
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.contact-support p {
    margin-bottom: 0.5rem;
    color: var(--dark-grey);
    font-size: 0.95rem;
}

.support-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.support-link:hover {
    color: var(--secondary-teal);
}

.support-link i {
    font-size: 1.1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .register-options {
        grid-template-columns: 1fr;
    }
}

/* Registration Form Styling */
.register-form {
    text-align: left;
}

.register-form fieldset {
    border: 1px solid #e0e0e0;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.register-form fieldset:hover {
    border-color: var(--primary-blue);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.register-form legend {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-blue);
    padding: 0 10px;
}

.register-form .form-group {
    margin-bottom: 1.5rem;
}

.register-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 0.95rem;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-grey);
    font-size: 1.1rem;
}

.input-with-icon input,
.input-with-icon textarea {
    width: 100%;
    padding: 1.2rem 1rem 1.2rem 50px; /* Larger padding for mobile */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px; /* Prevent zoom on iOS */
    transition: all 0.3s ease;
    min-height: 44px; /* Minimum touch target */
    -webkit-appearance: none; /* Remove iOS styling */
    appearance: none;
}

/* Responsive input adjustments */
@media (min-width: 768px) {
    .input-with-icon input,
    .input-with-icon textarea {
        padding: 1rem 1rem 1rem 45px;
        font-size: 1rem;
    }
}

.input-with-icon textarea {
    padding-top: 1.5rem;
    min-height: 100px;
    resize: vertical;
}

.input-with-icon input:focus,
.input-with-icon textarea:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.1);
    outline: none;
}

.password-input-wrapper {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--dark-grey);
    cursor: pointer;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-blue);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
    height: 5px;
    background-color: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-text {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    text-align: right;
}

/* Checkbox Styling */
.checkbox {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-top: 0.2rem;
    accent-color: var(--primary-blue);
    cursor: pointer;
}

.checkbox label {
    margin: 0;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    line-height: 1.5;
}

.checkbox a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.checkbox a:hover {
    color: var(--secondary-teal);
    text-decoration: underline;
}

/* Register Button */
.register-button {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.register-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.register-button:hover::before {
    left: 100%;
}

.register-button:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.register-button i {
    transition: transform 0.3s ease;
}

.register-button:hover i {
    transform: translateX(3px);
}

/* Login Prompt */
.login-prompt {
    text-align: center;
    color: var(--dark-grey);
    font-size: 1rem;
    margin: 0;
}

.login-prompt a {
    color: var(--primary-blue);
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
}

.login-prompt a:hover {
    color: var(--secondary-teal);
    text-decoration: underline;
}

/* Form Error Messages */
.form-error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
}

input.is-invalid,
textarea.is-invalid {
    border-color: #dc3545 !important;
}

/* Progress Steps for Multi-step Forms */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #e0e0e0;
    z-index: 1;
}

.progress-step {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 30px;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: var(--dark-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.8rem;
    color: var(--dark-grey);
    text-align: center;
    white-space: nowrap;
    position: absolute;
    top: 35px;
    transform: translateX(-50%);
    left: 50%;
}

.progress-step.active .step-number {
    background-color: var(--primary-blue);
    color: white;
}

.progress-step.completed .step-number {
    background-color: var(--secondary-teal);
    color: white;
}

.progress-step.completed .step-number::after {
    content: '✓';
}

/* Responsive Styles */
@media (max-width: 992px) {
    .register-container {
        flex-direction: column;
        max-width: 600px;
        margin: 1.5rem;
    }

    .register-brand-panel {
        padding: 2.5rem 1.5rem;
    }

    .register-form-panel {
        padding: 2.5rem 1.5rem;
    }

    .register-brand-title {
        font-size: 2rem;
    }

    .register-benefits {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem 2rem;
    }

    .register-benefit {
        width: auto;
    }
}

@media (max-width: 768px) {
    .register-container {
        margin: 1rem;
        min-height: auto;
    }

    .register-brand-panel {
        padding: 2rem 1rem;
    }

    .register-form-panel {
        padding: 2rem 1rem;
    }

    .register-header h1 {
        font-size: 1.8rem;
    }

    .register-description {
        font-size: 1rem;
    }

    .register-benefits {
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .register-benefit {
        flex-direction: column;
        gap: 0.5rem;
    }

    .register-form fieldset {
        padding: 1.2rem;
    }
}

@media (max-width: 480px) {
    .register-container {
        margin: 0;
        border-radius: 0;
        box-shadow: none;
    }

    .register-brand-panel {
        display: none;
    }

    .register-form-panel {
        padding: 1.5rem 1rem;
    }

    .register-button {
        padding: 0.9rem;
    }

    .register-form fieldset {
        padding: 1rem;
    }
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    }
}

.register-header,
.register-options-heading,
.register-options-footer,
.register-form fieldset,
.register-button,
.login-prompt {
    animation: fadeIn 0.6s ease forwards;
}

.register-options-heading {
    animation-delay: 0.1s;
}

.register-option-card:nth-child(1) {
    animation: scaleIn 0.6s ease forwards;
    animation-delay: 0.2s;
}

.register-option-card:nth-child(2) {
    animation: scaleIn 0.6s ease forwards;
    animation-delay: 0.3s;
}

.register-option-card:hover {
    animation: pulse 2s infinite;
}

.option-features li {
    animation: slideInRight 0.5s ease forwards;
    opacity: 0;
}

.option-features li:nth-child(1) {
    animation-delay: 0.4s;
}

.option-features li:nth-child(2) {
    animation-delay: 0.5s;
}

.option-features li:nth-child(3) {
    animation-delay: 0.6s;
}

.register-options-footer {
    animation-delay: 0.7s;
}

.login-prompt {
    animation-delay: 0.8s;
}

.register-form fieldset:nth-child(1) {
    animation-delay: 0.2s;
}

.register-form fieldset:nth-child(2) {
    animation-delay: 0.3s;
}

.register-form fieldset:nth-child(3) {
    animation-delay: 0.4s;
}

.register-form fieldset:nth-child(4) {
    animation-delay: 0.5s;
}

.register-button {
    animation-delay: 0.6s;
}

.register-benefit:nth-child(1) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.2s;
}

.register-benefit:nth-child(2) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.3s;
}

.register-benefit:nth-child(3) {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.4s;
}

/* Shake animation for form errors */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

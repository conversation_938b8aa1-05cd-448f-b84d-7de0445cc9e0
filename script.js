/**
 * NP Labs Frontend Script v4 (Reverted Body Lock)
 *
 * Includes:
 * - Password Visibility Toggle
 * - Registration Page Form Choice Logic
 * - Mobile Navigation Toggle (Using class toggle, no body lock)
 * - Basic Password Strength Indicator
 * - Form Submission Handling (Loading State, Basic Validation Feedback)
 * - Scroll Animations (Intersection Observer)
 */

document.addEventListener('DOMContentLoaded', function() {

    /**
     * Password Visibility Toggle
     */
    function setupPasswordToggles() {
        const passwordToggles = document.querySelectorAll('.password-toggle');
        passwordToggles.forEach(toggle => {
            if (toggle.dataset.listenerAttached) return;
            toggle.addEventListener('click', function() {
                const passwordInput = this.closest('.password-group').querySelector('input[type="password"], input[type="text"]');
                if (passwordInput) {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    this.classList.toggle('fa-eye');
                    this.classList.toggle('fa-eye-slash');
                    this.setAttribute('aria-label', type === 'password' ? 'Show password' : 'Hide password');
                }
            });
            toggle.dataset.listenerAttached = 'true';
        });
    }
    setupPasswordToggles();

    /**
     * Registration Page: Show/Hide Forms based on Choice
     */
    function setupRegistrationChoice() {
        const registrationChoiceArea = document.getElementById('registration-choice-area');
        const formWrapper = document.getElementById('form-wrapper');
        if (!registrationChoiceArea || !formWrapper) return;

        const choiceButtons = registrationChoiceArea.querySelectorAll('.btn-choice');
        const backButtons = formWrapper.querySelectorAll('.back-to-choice-btn');
        const forms = formWrapper.querySelectorAll('.registration-form');

        const showRegistrationForm = (formId) => {
            const formToShow = document.getElementById(formId);
            if (formToShow) {
                registrationChoiceArea.style.display = 'none';
                formWrapper.style.display = 'block';
                forms.forEach(form => form.style.display = 'none');
                formToShow.style.display = 'block';
                const firstInput = formToShow.querySelector('input:not([type="hidden"]), select, textarea');
                if (firstInput) firstInput.focus();
            }
        };

        const showRegistrationChoices = () => {
            registrationChoiceArea.style.display = 'block';
            formWrapper.style.display = 'none';
            forms.forEach(form => form.style.display = 'none');
            if (choiceButtons.length > 0) choiceButtons[0].focus();
        };

        choiceButtons.forEach(button => {
            button.addEventListener('click', () => showRegistrationForm(button.dataset.form));
        });
        backButtons.forEach(button => {
            button.addEventListener('click', showRegistrationChoices);
        });
    }
    setupRegistrationChoice();

    /**
     * Mobile Navigation Toggle - Complete rewrite
     */
    function setupMobileNav() {
        // Get elements
        const toggleButton = document.getElementById('mobile-menu-toggle');
        const mainMenu = document.getElementById('main-menu');
        
        // Check if elements exist
        if (!toggleButton || !mainMenu) {
            console.log("Mobile menu elements not found on this page");
            return;
        }
        
        // Toggle menu function
        function toggleMenu() {
            mainMenu.classList.toggle('active');
            toggleButton.setAttribute('aria-expanded', 
                toggleButton.getAttribute('aria-expanded') === 'true' ? 'false' : 'true'
            );
            
            // Toggle icon
            const icon = toggleButton.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times');
            }
        }
        
        // Add event listener
        toggleButton.addEventListener('click', toggleMenu);
        
        // Set initial ARIA state
        toggleButton.setAttribute('aria-expanded', 'false');
        toggleButton.setAttribute('aria-label', 'Toggle navigation menu');
        
        // Close menu when clicking on links
        const menuLinks = mainMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    mainMenu.classList.remove('active');
                    toggleButton.setAttribute('aria-expanded', 'false');
                    
                    // Reset icon
                    const icon = toggleButton.querySelector('i');
                    if (icon) {
                        icon.classList.add('fa-bars');
                        icon.classList.remove('fa-times');
                    }
                }
            });
        });
        
        // Close menu on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mainMenu.classList.contains('active')) {
                mainMenu.classList.remove('active');
                toggleButton.setAttribute('aria-expanded', 'false');
                
                // Reset icon
                const icon = toggleButton.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            }
        });
    }
    setupMobileNav();

    /**
     * Dropdown functionality
     */
    function setupDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown');

        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    const menu = this.querySelector('.dropdown-menu');
                    menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
                }
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                dropdowns.forEach(dropdown => {
                    if (window.innerWidth > 768) {
                        dropdown.querySelector('.dropdown-menu').style.display = 'none';
                    }
                });
            }
        });
    }
    setupDropdowns();

    /**
     * Basic Password Strength Indicator
     */
    function setupPasswordStrength() {
        const passwordInputs = document.querySelectorAll('input[type="password"][name="password"]');
        passwordInputs.forEach(input => {
            const strengthMeter = input.closest('.password-group')?.querySelector('.password-strength-meter');
            if (strengthMeter) {
                input.addEventListener('input', function() {
                    const password = this.value;
                    let strengthClass = '';
                    const hasUpperCase = /[A-Z]/.test(password);
                    const hasLowerCase = /[a-z]/.test(password);
                    const hasNumbers = /[0-9]/.test(password);
                    const hasNonalphas = /[^A-Za-z0-9]/.test(password);
                    let score = 0;
                    if (password.length >= 8) score++;
                    if (password.length >= 12) score++;
                    if (hasUpperCase && hasLowerCase) score++;
                    if (hasNumbers) score++;
                    if (hasNonalphas) score++;

                    if (password.length === 0) { strengthClass = ''; }
                    else if (score <= 2) { strengthClass = 'weak'; }
                    else if (score <= 4) { strengthClass = 'medium'; }
                    else { strengthClass = 'strong'; }
                    strengthMeter.className = 'password-strength-meter ' + strengthClass;
                });
            }
        });
    }
    setupPasswordStrength();

    /**
     * Form Submission Handling (Loading State, Basic Validation Feedback)
     */
    function setupFormSubmission() {
        const formsToIntercept = document.querySelectorAll('form[novalidate]');
        formsToIntercept.forEach(form => {
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                const submitButton = form.querySelector('.form-submit-btn, button[type="submit"]');
                const formMessage = form.querySelector('.form-message');
                let originalButtonText = '';
                if (formMessage) { formMessage.textContent = ''; formMessage.className = 'form-message'; }
                if (!form.checkValidity()) {
                    const firstInvalid = form.querySelector(':invalid');
                    if (firstInvalid) {
                        firstInvalid.focus();
                        if (formMessage) { formMessage.textContent = 'Please correct the errors above.'; formMessage.classList.add('error'); }
                    }
                    return;
                }
                if (submitButton) { originalButtonText = submitButton.textContent; submitButton.disabled = true; submitButton.innerHTML = 'Submitting... <i class="fas fa-spinner fa-spin"></i>'; }

                // --- Replace setTimeout with fetch/AJAX ---
                console.log(`Simulating submission for form: ${form.id || 'form'}`);
                setTimeout(() => {
                    const isSuccess = Math.random() > 0.3;
                    if (submitButton) { submitButton.disabled = false; submitButton.innerHTML = originalButtonText; }
                    if (formMessage) {
                        if (isSuccess) {
                            formMessage.textContent = 'Success! (Demo)'; formMessage.classList.add('success'); form.reset();
                            form.querySelector('.password-strength-meter')?.setAttribute('class', 'password-strength-meter');
                        } else {
                            formMessage.textContent = 'Error occurred. (Demo)'; formMessage.classList.add('error');
                        }
                    }
                }, 1500);
            });
        });
    }
    setupFormSubmission();

    /**
     * Scroll Animations using Intersection Observer
     */
    function setupScrollAnimations() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        if (animatedElements.length === 0) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    // Optionally remove observer after animation
                    // observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }
    setupScrollAnimations();

    /**
     * Hero Section Enhancement
     */
    function enhanceHeroSection() {
        const heroSection = document.querySelector('.hero-section-new');
        if (!heroSection) return;
        
        // Add subtle zoom effect to hero background
        const heroImg = heroSection.querySelector('.hero-bg-img');
        if (heroImg) {
            setTimeout(() => {
                heroImg.classList.add('hero-bg-zoom');
            }, 300);
        }
        
        // Add staggered animation to hero content elements
        const heroContent = heroSection.querySelector('.hero-content');
        if (heroContent) {
            const elements = heroContent.children;
            Array.from(elements).forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = `opacity 0.5s ease, transform 0.5s ease`;
                element.style.transitionDelay = `${index * 0.15}s`;
                
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 100);
            });
        }
    }
    enhanceHeroSection();
    
    /**
     * Stats Counter Animation
     */
    function setupStatsCounter() {
        const statItems = document.querySelectorAll('.stat-item h3');
        if (!statItems.length) return;
        
        // Create Intersection Observer to trigger counting when stats are visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statElement = entry.target;
                    const targetValue = parseInt(statElement.textContent.replace(/\D/g, ''));
                    const duration = 2000; // 2 seconds
                    const startTime = performance.now();
                    const suffix = statElement.textContent.includes('+') ? '+' : '';
                    
                    // Start counting animation
                    function updateCount(currentTime) {
                        const elapsedTime = currentTime - startTime;
                        const progress = Math.min(elapsedTime / duration, 1);
                        
                        // Easing function for smoother animation
                        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                        const currentCount = Math.floor(easeOutQuart * targetValue);
                        
                        // Format with commas for thousands
                        const formattedCount = currentCount.toLocaleString();
                        statElement.textContent = formattedCount + suffix;
                        
                        if (progress < 1) {
                            requestAnimationFrame(updateCount);
                        }
                    }
                    
                    requestAnimationFrame(updateCount);
                    observer.unobserve(statElement); // Only animate once
                }
            });
        }, { threshold: 0.5 });
        
        // Observe all stat elements
        statItems.forEach(item => {
            observer.observe(item);
        });
    }
    setupStatsCounter();

    // Animation on scroll
    // Function to check if an element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Function to handle scroll animation
    function handleScrollAnimation() {
        const elements = document.querySelectorAll('.animate-on-scroll');
        
        elements.forEach(element => {
            if (isInViewport(element)) {
                element.classList.add('animated');
            }
        });
    }

    // Initial check on page load
    handleScrollAnimation();

    // Check on scroll
    window.addEventListener('scroll', handleScrollAnimation);

    // Check if partner logo images are loading correctly
    const partnerLogos = document.querySelectorAll('.partner-logo');
    
    partnerLogos.forEach(function(logo) {
        // Add event listeners to check if images load or fail
        logo.addEventListener('load', function() {
            console.log('Image loaded successfully:', logo.src);
        });
        
        logo.addEventListener('error', function() {
            console.error('Image failed to load:', logo.src);
            // Add a fallback text to indicate the image failed to load
            const container = logo.closest('.partner-logo-container');
            if (container) {
                const fallbackText = document.createElement('div');
                fallbackText.className = 'fallback-logo-text';
                fallbackText.textContent = logo.alt.replace(' Logo', '');
                container.appendChild(fallbackText);
                logo.style.display = 'none';
            }
        });
    });

}); // End DOMContentLoaded
// Function to load the header
function loadHeader() {
    fetch('_header.html')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(data => {
            const headerPlaceholder = document.getElementById('header-placeholder');
            if (headerPlaceholder) {
                headerPlaceholder.innerHTML = data;
                initializeNavigation();
            } else {
                console.error('Header placeholder element not found.');
            }
        })
        .catch(error => {
            console.error('Error loading header:', error);
        });
}

// Function to initialize mobile menu and navigation
function initializeNavigation() {
    const mobileNavToggle = document.getElementById('mobile-nav-toggle');
    const mobileNavClose = document.getElementById('mobile-nav-close');
    const mainNav = document.getElementById('main-nav');
    const megaMenuItems = mainNav ? mainNav.querySelectorAll('.nav-item.has-mega-menu') : [];

    // Mobile Nav Toggle
    if (mobileNavToggle && mainNav) {
        mobileNavToggle.addEventListener('click', () => {
            mainNav.classList.add('active');
            document.body.classList.add('no-scroll'); // Prevent body scroll
            mobileNavToggle.setAttribute('aria-expanded', 'true');
        });
    }

    // Mobile Nav Close
    if (mobileNavClose && mainNav) {
        mobileNavClose.addEventListener('click', () => {
            closeMobileNav(mainNav, mobileNavToggle); // Use helper function
        });
    }

    // Handle click outside for mobile nav
    document.addEventListener('click', (event) => {
        if (mainNav && mainNav.classList.contains('active') && 
            !mainNav.contains(event.target) && 
            !mobileNavToggle.contains(event.target)) {
            closeMobileNav(mainNav, mobileNavToggle); // Use helper function
        }
    });

    // Mobile Mega Menu Toggle
    megaMenuItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        if (link) {
            link.addEventListener('click', (event) => {
                // Only toggle if in mobile view (nav panel is active)
                if (mainNav && mainNav.classList.contains('active')) {
                    event.preventDefault(); // Prevent default link behavior only on mobile toggle
                    
                    // Close other open mega menus
                    megaMenuItems.forEach(otherItem => {
                        if (otherItem !== item && otherItem.classList.contains('active')) {
                            otherItem.classList.remove('active');
                        }
                    });
                    
                    // Toggle the current one
                    item.classList.toggle('active');
                }
                // On desktop, the default link behavior (href="#") should proceed if needed
            });
        }
    });
}

// Helper function to close mobile navigation
function closeMobileNav(mainNav, mobileNavToggle) {
    if (mainNav) {
        mainNav.classList.remove('active');
        document.body.classList.remove('no-scroll');
        if (mobileNavToggle) {
            mobileNavToggle.setAttribute('aria-expanded', 'false');
        }
        // Close any open mobile submenus/mega menus
        mainNav.querySelectorAll('.nav-item.has-mega-menu.active').forEach(item => {
            item.classList.remove('active');
        });
    }
}

// Function to load the footer
function loadFooter() {
    fetch('_footer.html')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(data => {
            const footerPlaceholder = document.getElementById('footer-placeholder');
            if (footerPlaceholder) {
                footerPlaceholder.innerHTML = data;
            } else {
                console.error('Footer placeholder element not found.');
            }
        })
        .catch(error => {
            console.error('Error loading footer:', error);
        });
}

// Add event listener to run loadHeader and loadFooter when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    loadHeader();
    loadFooter();
});

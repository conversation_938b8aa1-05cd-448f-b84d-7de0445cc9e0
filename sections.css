/* START OF FILE sections.css */

/* ==========================================
   Homepage Sections
========================================== */

/* Hero Section */
.hero-section-new {
    position: relative;
    /* height: 85vh; */ /* Temporarily comment out vh */
    height: 650px; /* Use fixed pixel height for testing */
    /* min-height: 650px; */ /* Temporarily comment out min-height */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-white);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-bg-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    z-index: 0;
}

@media (max-width: 768px) {
    .hero-section-new {
        height: auto; /* Allow height to adjust to content */
        min-height: 500px; /* Minimum height */
        padding: 80px 0 60px; /* Increased top padding to account for header */
        text-align: center;
    }

    .hero-bg-img {
        height: 100%; /* Cover the entire section */
        object-fit: cover;
        object-position: center 20%; /* Adjust vertical position if needed */
    }

    /* Adjust hero content for mobile */
    .hero-content {
        max-width: 100%; /* Full width on mobile */
        padding: 25px 20px; /* Reduced padding */
        margin: 0 15px; /* Add some margin on the sides */
    }

    /* Reduce heading size */
    .hero-content h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
        line-height: 1.3;
    }

    /* Adjust paragraph text */
    .hero-content p {
        font-size: 1rem;
        margin-bottom: 1.8rem;
        line-height: 1.5;
    }

    /* Adjust badge size */
    .hero-badge {
        font-size: 0.9rem;
        padding: 6px 12px;
        margin-bottom: 1rem;
    }

    /* Center and adjust CTA buttons */
    .hero-cta-group {
        justify-content: center;
    }

    .hero-cta {
        padding: 12px 20px;
        font-size: 1rem;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .hero-section-new {
        padding: 70px 0 50px;
    }

    .hero-content {
        padding: 20px 15px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    /* Stack buttons vertically on very small screens */
    .hero-cta-group {
        flex-direction: column;
        gap: 10px;
    }

    .hero-cta {
        width: 100%;
        padding: 12px 15px;
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.2) 0%, rgba(0, 168, 150, 0.2) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 650px;
    padding: 35px;
    animation: fadeInUp 1s ease-out;
    background-color: rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    backdrop-filter: blur(3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3.2rem;
    margin-bottom: 1.5rem;
    color: white;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
    text-align: center;
}

.hero-highlight {
    color: var(--secondary-teal);
    font-weight: 900;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.hero-content p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    line-height: 1.7;
    font-weight: 500;
}

.hero-cta-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.hero-cta {
    padding: 14px 28px;
    font-weight: 700;
    border-radius: 50px;
    transition: all var(--transition-speed);
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    /* min-width: 180px; */
}

.hero-cta.primary {
    background-color: var(--secondary-teal);
    color: white;
    border: 2px solid var(--secondary-teal);
    box-shadow: 0 4px 15px rgba(0, 168, 150, 0.3);
}

.hero-cta.primary:hover {
    background-color: var(--secondary-teal-dark);
    border-color: var(--secondary-teal-dark);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 168, 150, 0.4);
}

.hero-cta.secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.hero-cta.secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Personalized Medicine Section */
.personalized-medicine {
    background-color: white;
    text-align: center;
}

.personalized-medicine h2 {
    line-height: 1.2;
    margin-bottom: 1rem;
}

.personalized-medicine p {
    max-width: 800px;
    margin: 0 auto 3rem;
    font-size: 1.2rem;
}

/* Note: .benefits-grid, .benefit-item moved to components.css if deemed reusable, otherwise keep here */
/* Assuming .benefits-grid used uniquely here for now */
.personalized-medicine .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 2rem;
}

.personalized-medicine .benefit-item {
    background-color: var(--light-grey);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-speed);
    text-align: center;
}

.personalized-medicine .benefit-item:hover {
    transform: translateY(-5px);
}

.personalized-medicine .benefit-item i {
    font-size: 2.5rem;
    color: var(--primary-blue);
    margin-bottom: 20px;
}

.personalized-medicine .benefit-item h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.personalized-medicine .benefit-item p {
    margin-bottom: 0;
    font-size: 1rem;
}

/* Trust Section */
.trust-section {
    background-color: var(--light-grey);
    text-align: center;
    padding: 80px 0;
}

.trust-section p {
    max-width: 800px;
    margin: 0 auto 3rem;
    font-size: 1.2rem;
    line-height: 1.8;
}

/* Note: .stats-grid, .stat-item moved to components.css if deemed reusable, otherwise keep here */
/* Assuming .stats-grid used uniquely here for now */
.trust-section .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
    margin-bottom: 3rem;
}

.trust-section .stat-item {
    background-color: white;
    padding: 40px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    position: relative;
    overflow: hidden;
}

.trust-section .stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-teal));
}

.trust-section .stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.trust-section .stat-item i {
    font-size: 2.5rem;
    color: var(--secondary-teal);
    margin-bottom: 20px;
    display: inline-block;
    transition: transform 0.3s ease;
}

.trust-section .stat-item:hover i {
    transform: scale(1.2);
}

.trust-section .stat-item h3 {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 10px;
    font-weight: 900;
    line-height: 1;
}

.trust-section .stat-item p {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-grey);
}

/* Accreditations Placeholder/Section (Homepage Specific) */
.accreditations-placeholder { /* Keeping this name if it's specifically a placeholder structure */
    background-color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 2rem;
}

.accreditations-placeholder h4 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.placeholder-text {
    color: var(--dark-grey);
    font-style: italic;
    opacity: 0.7;
}

/* Accreditations and Partners Section (Homepage Specific) */
.accreditations-section {
    background-color: white;
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 3rem;
    text-align: center;
}

.accreditations-section h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.accreditations-section p {
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Certifications Container (Homepage Specific) */
.certifications-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.certification-item {
    background-color: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    flex: 1;
    min-width: 220px;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.certification-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.certification-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.1) 0%, rgba(0, 168, 150, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.certification-icon i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-teal) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.certification-content {
    text-align: center;
}

.certification-item h5 {
    margin-bottom: 0.75rem;
    color: var(--primary-blue);
    font-size: 1.25rem;
    font-weight: 700;
}

.certification-item p {
    font-size: 0.95rem;
    color: #555;
    line-height: 1.5;
}
/* Note: .certification-item moved to components.css if deemed reusable */

/* Partners Section (Homepage Specific) */
.partners-section { /* Styles specific to the partner section within accreditations on homepage */
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    background-image: radial-gradient(var(--medium-grey) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0;
    padding: 40px 30px;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.partners-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 0;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.partners-section h3, /* Added h3 to lift it above overlay */
.partners-section h4,
.partners-section p,
.partners-grid {
    position: relative;
    z-index: 1;
}

.partners-section h4 {
    color: var(--secondary-teal);
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

/* Note: .partners-grid, .partner-item moved to components.css if deemed reusable */
/* Assuming .partners-grid structure used uniquely here for now */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
    margin-top: 2rem;
}

/* Specific override for homepage partners grid to show 5 across */
.homepage-partners-grid {
    grid-template-columns: repeat(5, 1fr) !important; /* Force 5 columns for desktop/notebook */
}

.partner-item { /* Partner item in the accreditations section */
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.partner-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.partner-logo-container {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.partner-logo {
    max-width: 100%;
    max-height: 80px;
    object-fit: contain;
}

/* Responsive adjustments for accreditations/partners section */
@media (max-width: 992px) {
    .partners-grid { /* Adjust for smaller screens but not yet mobile */
        grid-template-columns: repeat(3, 1fr); /* 3 columns for smaller desktops/laptops */
    }

    .homepage-partners-grid {
        grid-template-columns: repeat(3, 1fr) !important; /* 3 columns for smaller desktops/laptops */
    }
}

@media (max-width: 768px) {
    .accreditations-section {
        padding: 30px 20px;
    }

    .partners-grid { /* Specific to this section's grid */
        grid-template-columns: repeat(2, 1fr); /* 2 columns for tablets */
    }

    .homepage-partners-grid {
        grid-template-columns: repeat(2, 1fr) !important; /* 2 columns for tablets */
    }

    .certifications-container { /* Specific to this section's grid */
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .partners-grid { /* Single column for small mobile devices */
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .homepage-partners-grid {
        grid-template-columns: 1fr !important; /* Single column for small mobile devices */
    }

}

/* Facility Section */
.facility-section {
    background-color: white;
}

.compounding-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.compounding-image {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    height: 100%;
}

.compounding-image picture {
    width: 100%;
    height: 100%;
    display: block;
}

.compounding-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.5s ease;
}

.compounding-image:hover img {
    transform: scale(1.05);
}

.compounding-text h3 {
    font-size: 2rem;
    margin-bottom: 20px;
}

.compounding-text h4 {
    font-weight: 400;
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--dark-grey);
}

/* Portal Benefits Section */
.portal-benefits-section {
    background-color: var(--light-grey);
    text-align: center;
}

/* Note: .benefits-grid, .benefit-card moved to components.css if deemed reusable */
/* Assuming benefits-grid structure used uniquely here */
.portal-benefits-section .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Or specific columns like 4 */
    gap: 30px;
    margin-bottom: 2rem;
}

.portal-benefits-section .benefit-card {
    background-color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-speed);
    text-align: center;
}

.portal-benefits-section .benefit-card:hover {
    transform: translateY(-5px);
}

.portal-benefits-section .benefit-icon {
    font-size: 2.5rem;
    color: var(--primary-blue);
    margin-bottom: 20px;
}

.portal-benefits-section .benefit-card h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.portal-benefits-section .benefit-card p {
    margin-bottom: 0;
    font-size: 1rem;
}

/* Testimonials Section */
.testimonials-section {
    background-color: white;
    text-align: center;
}

.testimonials-section p { /* The intro paragraph */
    max-width: 800px;
    margin: 0 auto 3rem;
    font-size: 1.2rem;
}

/* Note: .testimonial-placeholder-grid, .testimonial-item moved to components.css if deemed reusable */
/* Assuming testimonial structure used uniquely here */
.testimonials-section .testimonial-placeholder-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 2rem;
}

.testimonials-section .testimonial-item {
    background-color: var(--light-grey);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    position: relative;
    text-align: left;
}

.testimonials-section .testimonial-item p { /* Testimonial quote */
    font-style: italic;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.testimonials-section .testimonial-author {
    display: block;
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.testimonials-section .testimonial-role {
    display: block;
    color: var(--dark-grey);
    font-size: 0.9rem;
    margin-top: 5px;
}


/* ==========================================
   Specific Page Styles
========================================== */

/* Create Patient Account Form (Likely register.html) */
.create-patient-form {
    max-width: 600px;
    margin: 40px auto;
    padding: 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.create-patient-form h2 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    margin-bottom: 10px;
    text-align: center;
}

.create-patient-form p {
    text-align: center;
    color: var(--dark-grey);
    margin-bottom: 25px;
}

.create-patient-form .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-blue);
    text-decoration: none;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.create-patient-form .back-link i {
    margin-right: 5px;
}

.create-patient-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.create-patient-form .form-group {
    margin-bottom: 15px;
}

.create-patient-form label {
    display: block;
    margin-bottom: 5px;
    color: var(--dark-grey);
    font-weight: 500;
}

.create-patient-form input,
.create-patient-form select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-grey);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.create-patient-form input:focus,
.create-patient-form select:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 80, 158, 0.1);
}

.create-patient-form .password-field {
    position: relative;
}

.create-patient-form .password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--dark-grey);
    cursor: pointer;
    padding: 5px;
}

.create-patient-form .register-button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
    transition: background-color 0.3s ease;
}

.create-patient-form .register-button:hover {
    background-color: var(--primary-blue-dark);
}

@media (max-width: 768px) {
    .create-patient-form .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .create-patient-form {
        padding: 20px;
        margin: 20px;
    }
}

/* Register Page Section (register.html) */
.register-section {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120px 20px 40px;
    background-color: var(--light-grey);
}

.register-card {
    background: white;
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    text-align: center;
}

.register-card h1 {
    color: var(--primary-blue);
    font-size: clamp(1.5rem, 4vw, 1.8rem);
    margin-bottom: 10px;
    line-height: 1.2;
}

.register-description { /* Used in both register card and form section */
    color: var(--dark-grey);
    margin-bottom: 30px;
    font-size: clamp(0.9rem, 3vw, 1rem);
    line-height: 1.5;
}

.register-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
    padding: 0 10px;
}

.register-option-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: clamp(12px, 3vw, 15px);
    background-color: var(--primary-blue);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-size: clamp(0.9rem, 3vw, 1.1rem);
    font-weight: 500;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
    width: 100%;
}

.register-option-btn:hover {
    background-color: var(--primary-blue-dark);
}

.register-option-btn i {
    font-size: clamp(1rem, 3vw, 1.2rem);
}

/* Shared login prompt style */
.login-prompt {
    color: var(--dark-grey);
    margin: 0;
    font-size: clamp(0.85rem, 2.5vw, 0.95rem);
    text-align: center; /* Ensure centered */
    margin-top: 25px; /* Add consistent margin */
}

.login-prompt a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500; /* Added weight */
}

.login-prompt a:hover {
    text-decoration: underline;
}

@media (max-width: 480px) {
    .register-section {
        padding: 100px 15px 30px;
    }

    .register-card {
        padding: 30px 20px;
    }

    .register-options {
        padding: 0;
    }
}

/* Registration Form Styles (likely register.html) */
.register-form {
    text-align: left;
    margin-top: 30px; /* Appears within register-card or similar */
}

.register-form fieldset {
    border: 1px solid var(--medium-grey);
    padding: 20px 25px;
    margin-bottom: 25px;
    border-radius: var(--border-radius);
}

.register-form legend {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-blue);
    padding: 0 10px;
    margin-left: 10px;
}

/* Style for screen-reader only legend */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.register-form .agreement-fieldset {
    border: none;
    padding: 0;
    margin-bottom: 20px;
}

.register-form .form-group {
    margin-bottom: 20px;
}

.register-form label {
    display: block;
    margin-bottom: 8px;
    color: var(--dark-grey);
    font-weight: 500;
    font-size: 0.95rem;
}

.register-form input[type="text"],
.register-form input[type="email"],
.register-form input[type="tel"],
.register-form input[type="date"],
.register-form input[type="password"],
.register-form textarea { /* Added textarea */
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--medium-grey); /* Use variable */
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease; /* Added box-shadow transition */
    box-sizing: border-box; /* Ensure padding included in width */
}

.register-form textarea {
    resize: vertical;
    min-height: 100px;
}

.register-form input:focus,
.register-form textarea:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 80, 158, 0.15); /* Added focus shadow */
}

.register-form .password-input {
    position: relative;
}

.register-form .toggle-password { /* Also used in login */
    position: absolute;
    right: 10px; /* Adjusted from 15px */
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--dark-grey);
    cursor: pointer;
    padding: 5px; /* Added padding */
    font-size: 1rem; /* Ensure consistent size */
}

.register-form .checkbox {
    display: flex;
    align-items: center; /* Better alignment */
    gap: 10px;
    margin-top: 10px;
}

.register-form .checkbox input[type="checkbox"] {
    width: auto;
    margin-top: -1px;
    flex-shrink: 0; /* Prevent shrinking */
}

.register-form .checkbox label {
    margin-bottom: 0;
    font-weight: 400;
    font-size: 0.9rem;
}

.register-form .checkbox label a {
    color: var(--secondary-teal); /* Changed color */
    text-decoration: underline;
}

.register-form button[type="submit"], /* Specific button styling */
.register-form .register-button { /* Covering both possibilities */
    width: 100%;
    padding: 12px 20px;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    box-shadow: var(--box-shadow);
}

.register-form button[type="submit"]:hover,
.register-form .register-button:hover {
    background-color: var(--primary-blue-dark);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

@media (max-width: 480px) {
    .register-form {
        margin-top: 20px;
    }

    .register-form .form-group {
        margin-bottom: 15px;
    }

    .register-form input[type="text"],
    .register-form input[type="email"],
    .register-form input[type="tel"],
    .register-form input[type="date"],
    .register-form input[type="password"],
    .register-form textarea {
        padding: 10px 12px;
        font-size: 0.95rem;
    }
}


/* --- Login Page Styles --- */
.login-section {
    padding: 4rem 0;
    background-color: #f8f9fa; /* Use variable if defined */
    min-height: calc(100vh - 160px); /* Adjust based on header/footer height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background-color: #ffffff;
    padding: 2.5rem;
    border-radius: 8px; /* Consider var(--border-radius) */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); /* Consider var(--box-shadow) */
    max-width: 450px;
    width: 100%;
    margin: 0 auto;
    text-align: center;
}

.login-card h2 {
    margin-bottom: 0.75rem;
    color: var(--primary-blue); /* Use variable */
    font-weight: 700;
    font-size: 1.8rem;
}

.login-description {
    margin-bottom: 2rem;
    color: #6c757d; /* Consider var(--dark-grey) or similar */
    font-size: 1rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.login-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057; /* Consider var(--dark-grey) */
    font-size: 0.9rem;
}

.login-form input[type="email"],
.login-form input[type="password"] {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da; /* Consider var(--medium-grey) */
    border-radius: 5px; /* Consider var(--border-radius) */
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.login-form input:focus {
    border-color: var(--primary-blue-light); /* Use variable */
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 80, 158, 0.25); /* Soft shadow */
}

/* Password Input Specifics (Login) */
.password-group .password-input-wrapper { /* Should be shared with register? */
    position: relative;
}

.password-input-wrapper input[type="password"] {
    padding-right: 3rem; /* Make space for toggle */
}

/* Toggle password style is shared, keep in components.css? Moved here for page context */
.toggle-password {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 3rem;
    background: transparent;
    border: none;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.toggle-password:hover {
    color: #343a40;
}

.toggle-password i.fa-eye-slash::before { /* Specific icon state */
    content: "\f070";
}

/* Form Options (Remember Me / Forgot Password) - Login */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.remember-me { /* Can be component? Moved here for now */
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: var(--primary-blue); /* Style checkbox */
}

.remember-me label {
    margin-bottom: 0;
    font-weight: 400; /* Lighter weight */
    color: #495057;
}

.forgot-password a {
    color: var(--primary-blue);
    text-decoration: none;
}

.forgot-password a:hover {
    text-decoration: underline;
}

/* Login Button */
.login-button {
    width: 100%;
    padding: 0.9rem;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease, transform 0.1s ease;
    /* Inherits .btn styles, add specifics */
    background-color: var(--primary-blue);
    color: white;
    border: 2px solid var(--primary-blue);
}

.login-button:hover {
    background-color: var(--primary-blue-dark); /* Use variable */
    border-color: var(--primary-blue-dark);
    transform: translateY(-1px);
}

/* Divider */
.login-divider {
    margin: 1.5rem 0;
    text-align: center;
    position: relative;
    color: #adb5bd;
    font-size: 0.9rem;
    display: flex; /* Use flex for line alignment */
    align-items: center;
}

.login-divider::before,
.login-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #ced4da; /* Consider var(--medium-grey) */
}

.login-divider span {
    background-color: #fff;
    padding: 0 10px;
    position: relative;
    z-index: 1;
    order: 0; /* Ensure span is between lines */
}
.login-divider::before { order: -1; margin-right: 10px;} /* Add spacing */
.login-divider::after { order: 1; margin-left: 10px;} /* Add spacing */


/* Social Login Buttons (Placeholder Styles - Login) */
.social-login {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-social {
    padding: 0.75rem;
    border-radius: 5px; /* Consider var(--border-radius) */
    border: 1px solid #ced4da; /* Consider var(--medium-grey) */
    background-color: #fff;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-decoration: none; /* Ensure it acts like a button */
}

.btn-social:hover {
    background-color: #f1f3f5; /* Consider var(--light-grey) */
}

.btn-social i {
    font-size: 1.2rem;
}

.btn-google i { color: #DB4437; }
.btn-facebook i { color: #4267B2; }

/* Registration Prompt (Login Page) */
.register-prompt { /* Style specifically for the prompt on the login page */
    margin-top: 2rem;
    font-size: 0.95rem;
    color: #6c757d;
}

.register-prompt a {
    color: var(--primary-blue);
    font-weight: 600;
    text-decoration: none;
}

.register-prompt a:hover {
    text-decoration: underline;
}

/* Responsive adjustments for Login Page*/
@media (max-width: 576px) {
    .login-card {
        padding: 1.5rem;
        box-shadow: none;
        border-radius: 0;
    }

    .login-section {
        padding: 2rem 0;
    }

    .login-card h2 {
        font-size: 1.6rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .forgot-password {
        align-self: flex-end;
    }

    .login-button {
        padding: 0.8rem;
        font-size: 1rem;
    }
}

/* --- About Page Styles --- */
.page-header { /* General page header, move to components? Keeping here for context */
    background-color: var(--primary-blue);
    color: var(--light-white);
    padding: 0rem 0; /* Adjusted padding */
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header.about-hero { /* Specific variant for About page */
    padding: 0rem 0; /* Override padding for all pages */
}
.page-header.team-hero { /* Specific variant for Team page */
    padding: 0rem 0; /* Override padding */
}
.page-header-overlay { /* Overlay specific to page headers */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 80, 158, 0.85) 0%, rgba(0, 168, 150, 0.85) 100%);
    z-index: 1;
}

.page-header .container { /* Ensure content is above overlay */
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
    color: var(--light-white); /* Ensure white */
}

.page-header p { /* Subtitle */
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.about-section { /* Main container for about content */
    padding: 5rem 0;
}

.about-intro { /* Intro text block */
    text-align: center;
    max-width: 800px;
    margin: 0 auto 4rem;
}

.about-intro h2 {
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    font-size: 2.2rem;
}

.about-grid { /* Two-column layout */
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
}

.about-content { /* Text content within the grid */
    font-size: 1.1rem;
    line-height: 1.6;
}

.about-content p {
    margin-bottom: 1.5rem;
}

.specialty-list { /* List of specialties */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    list-style: none;
    padding: 0;
}

.specialty-list li {
    position: relative;
    padding-left: 1.5rem;
    line-height: 1.4;
}

.specialty-list li::before {
    content: '•';
    color: var(--secondary-teal);
    position: absolute;
    left: 0;
    font-size: 1.2em;
}

.specialty-grid { /* Alternative grid display for specialties */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.specialty-item { /* Individual item in the specialty grid */
    background-color: var(--light-grey);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.specialty-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    background-color: var(--primary-blue);
    color: white;
}

.specialty-item i {
    margin-right: 10px;
    color: var(--primary-blue);
    font-size: 1.2rem;
}

.specialty-item:hover i {
    color: white;
}


.about-image { /* Container for the image in the grid */
    position: relative;
}

.rounded-image { /* Specific image styling */
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.image-frame { /* Decorative frame/container for image */
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.experience-badge { /* Badge overlay on image */
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: var(--primary-blue);
    color: white;
    padding: 1rem;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.experience-badge .years {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.7rem;
    margin-top: 5px;
}


/* Certification Section (About Page Specific) */
.certification-section {
    text-align: center;
    padding: 4rem 0;
    background-color: var(--light-grey);
    border-radius: 15px; /* Rounded corners for the section */
    margin: 4rem 0;
}

.certification-content { /* Container for badges within this section */
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 2rem;
    flex-wrap: wrap; /* Allow wrapping */
}

/* Note: .certification-badge styles likely in components.css, add overrides if needed */
.certification-section .certification-badge {
    text-align: center;
    max-width: 300px;
    /* Assuming base styles are in components, add section specific margin/padding if needed */
    background: none; /* Example override: remove bg */
    box-shadow: none; /* Example override: remove shadow */
}

.certification-section .certification-badge i {
    font-size: 3rem;
    color: var(--secondary-teal);
    margin-bottom: 1rem;
}

/* Capabilities Section (About Page Specific) */
.capabilities-section {
    margin: 4rem 0;
}

.capabilities-intro {
    max-width: 800px;
    margin: 0 auto 2rem;
    color: var(--dark-grey);
    font-size: 1.1rem;
}

/* New Prescription Cards Styling */
.prescription-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
    width: 90%; /* Make the entire grid 90% of its container width */
    margin-left: auto;
    margin-right: auto;
}

.prescription-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.prescription-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-blue);
}

.prescription-card-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: white;
    padding: 1.35rem; /* Slightly reduced padding */
    text-align: center;
    position: relative;
}

.prescription-icon {
    width: 72px; /* Reduced from 80px */
    height: 72px; /* Reduced from 80px */
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.9rem; /* Slightly reduced margin */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.prescription-card:hover .prescription-icon {
    transform: scale(1.1) rotate(5deg);
}

.prescription-icon i {
    font-size: 1.8rem; /* Reduced from 2rem */
    color: var(--primary-blue);
}

.prescription-card-header h4 {
    font-size: 1.2rem; /* Reduced from 1.3rem */
    margin: 0;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.prescription-card-body {
    padding: 1.35rem; /* Reduced from 1.5rem */
    flex-grow: 1;
}

.prescription-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.prescription-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.prescription-features li:last-child {
    border-bottom: none;
}

.prescription-features li i {
    color: var(--secondary-teal);
    margin-right: 10px;
    font-size: 0.9rem;
}

.prescription-card-footer {
    padding: 1.25rem; /* Reduced from 1.5rem */
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center;
}

.btn-learn-more {
    color: var(--primary-blue);
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-learn-more:hover {
    color: var(--secondary-teal);
    gap: 12px;
}

/* Responsive adjustments for prescription cards */
@media (max-width: 992px) {
    .prescription-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .prescription-cards {
        grid-template-columns: 1fr;
    }
}

/* Legacy capability styles for backward compatibility - Mobile First */
.capabilities-grid {
    display: grid;
    grid-template-columns: 1fr; /* Single column for mobile */
    gap: 1.5rem; /* Smaller gap for mobile */
    margin-top: 1.5rem; /* Smaller margin for mobile */
}

/* Responsive capabilities grid */
@media (min-width: 576px) {
    .capabilities-grid {
        gap: 1.75rem;
        margin-top: 1.75rem;
    }
}

@media (min-width: 768px) {
    .capabilities-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns for tablets */
        gap: 2rem;
        margin-top: 2rem;
    }
}

@media (min-width: 992px) {
    .capabilities-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Original layout for desktop */
        gap: 2rem;
        margin-top: 2rem;
    }
}

.capability-item {
    text-align: center;
    padding: 2rem;
    background-color: white;
    border-radius: 15px; /* Rounded corners */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.capability-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.capability-icon { /* Icon container */
    width: 70px;
    height: 70px;
    background-color: var(--light-grey);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.capability-item:hover .capability-icon {
    background-color: var(--primary-blue);
    transform: scale(1.1);
}

.capability-icon i {
    font-size: 1.8rem; /* Reduced size slightly */
    color: var(--primary-blue);
    transition: all 0.3s ease;
}

.capability-item:hover .capability-icon i {
    color: white;
}


/* Expert Support Section (About Page Specific) */
.expert-support {
    text-align: center;
    max-width: 800px;
    margin: 4rem auto;
    padding: 2rem;
    background-color: var(--light-white); /* White BG */
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.support-content { /* Container for icon + text */
    display: flex;
    align-items: center;
    background-color: var(--light-grey); /* Light grey BG */
    padding: 2rem;
    border-radius: 10px;
    margin-top: 3rem; /* Spacing from expert-support title */
}

.support-icon {
    flex: 0 0 80px;
    height: 80px;
    background-color: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
}

.support-icon i {
    font-size: 2rem;
    color: white;
}

.support-text {
    flex: 1;
    text-align: left; /* Align text left */
}

.support-text h3 {
    margin-top: 0;
}

/* Partners Section (About Page Specific) */
.partners-section { /* Styles specific to the About page partners section */
    background-color: #f9f9f9; /* Use variable if available */
    padding: 4rem 0;
    margin-top: 3rem; /* Spacing from previous section */
}

.partners-intro { /* Intro text for this section */
    color: var(--dark-grey) !important; /* Ensure color */
    margin-bottom: 2.5rem;
    font-size: 1.1rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    text-align: center; /* Center align */
}

.partners-grid { /* Grid layout specific to this section */
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4 columns */
    gap: 2rem;
    max-width: 1200px; /* Max width */
    margin: 0 auto; /* Center */
}

/* Ensure homepage partners grid maintains 5 columns */
.homepage-partners-grid {
    grid-template-columns: repeat(5, 1fr) !important;
}
/* Note: .partner-item styles likely in components.css, add overrides if needed */
.partners-section .partner-item {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.partners-section .partner-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.partners-section .partner-logo-container {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.partners-section .partner-logo {
    max-width: 80%;
    max-height: 80px;
    object-fit: contain;
}

.partners-section .partner-item h5 {
    color: var(--dark-grey);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.partners-section .partner-item p {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Responsive Styles for About Page Sections */
@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-image {
        order: -1; /* Image appears first on mobile */
    }

    .support-content {
        flex-direction: column;
        text-align: center;
    }

    .support-icon {
        margin-right: 0;
        margin-bottom: 1.5rem;
    }

    .partners-grid { /* Partners section on About */
        grid-template-columns: repeat(2, 1fr);
    }

    /* Maintain homepage partners grid at 3 columns */
    .homepage-partners-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 3rem 0;
    }
    .page-header h1 {
        font-size: 2.5rem;
    }
    .specialty-list {
        grid-template-columns: 1fr;
    }
    .specialty-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    .certification-content {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }
    .capabilities-grid {
        grid-template-columns: 1fr;
        gap: 1rem; /* Even smaller gap for mobile */
    }
    .capability-item {
        padding: 1rem;
    }
    .capability-icon {
        width: 60px;
        height: 60px;
    }
    .experience-badge {
        width: 80px;
        height: 80px;
    }
    .experience-badge .years {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .partners-grid { /* Partners section on About */
        grid-template-columns: 1fr;
    }

    /* Maintain homepage partners grid at 1 column for smallest screens */
    .homepage-partners-grid {
        grid-template-columns: 1fr !important;
    }
}

/* Call To Action Section (Generic/Reusable? Moved here for now) */
.cta-section {
    background-color: var(--primary-blue);
    color: white;
    padding: 4rem 0;
    margin-top: 4rem; /* Spacing */
}

.cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h3 {
    color: white;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Overriding button styles for this section */
.cta-buttons .btn-primary {
    background-color: white;
    color: var(--primary-blue);
    border-color: white;
}

.cta-buttons .btn-primary:hover {
    background-color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.9);
}

.cta-buttons .btn-secondary { /* Outline style on dark bg */
    color: white;
    border-color: white;
    background-color: transparent; /* Ensure transparent */
}

.cta-buttons .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
}


/* --- Privacy Policy / Terms of Use Page Styles --- */
.privacy-policy-page,
.terms-of-use-page {
    padding: 3rem 1rem;
    background-color: #f9f9f9; /* Use variable */
}

/* Specific headers for these pages */
.privacy-policy-page .page-header h1,
.terms-of-use-page .page-header h1 {
    text-align: center !important;
    margin-top: 1rem !important;
    margin-bottom: 2rem !important;
    color: var(--primary-blue) !important; /* Use variable */
    font-size: 2.2rem !important;
    font-weight: 600 !important;
    padding-bottom: 0.5rem !important;
    border: none !important;
    /* If using a generic page-header, these might be overrides */
}

.privacy-content-container,
.terms-content-container {
    max-width: 800px; /* Unified max-width */
    margin: 0 auto;
    background-color: #fff;
    padding: 2rem 2.5rem;
    border-radius: 8px; /* Use variable */
    box-shadow: 0 2px 10px rgba(0,0,0,0.05); /* Use variable */
    line-height: 1.7;
}

.privacy-content-container h1, /* Title within the content box */
.terms-content-container h1 {
    font-size: 2.2rem;
    color: var(--primary-blue); /* Use variable */
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee; /* Use variable */
}

.privacy-content-container h2,
.terms-content-container h2 {
    font-size: 1.6rem;
    color: var(--primary-blue); /* Use variable */
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee; /* Use variable */
}

.privacy-content-container h3,
.terms-content-container h3 {
    font-size: 1.3rem;
    color: var(--secondary-teal); /* Use variable */
    margin-top: 2rem;
    margin-bottom: 0.8rem;
}

.privacy-content-container p,
.terms-content-container p,
.terms-content p { /* Combined selectors */
    margin-bottom: 1.2rem;
    color: #444; /* Use variable */
}

.privacy-content-container ul,
.privacy-content-container ol,
.terms-content-container ul,
.terms-content-container ol,
.terms-content ul, /* Combined selectors */
.terms-content ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.privacy-content-container li,
.terms-content-container li,
.terms-content li { /* Combined selectors */
    margin-bottom: 0.8rem;
}

.privacy-content-container strong,
.terms-content-container strong,
.terms-content strong { /* Combined selectors */
    color: #333; /* Use variable */
}

.privacy-content-container a,
.terms-content-container a,
.terms-content a { /* Combined selectors */
    color: var(--primary-blue); /* Use variable */
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.privacy-content-container a:hover,
.terms-content-container a:hover,
.terms-content a:hover { /* Combined selectors */
    color: var(--primary-blue-dark); /* Use variable */
    text-decoration: underline;
}

/* Responsive Adjustments for Policy/Terms */
@media (max-width: 768px) {
    .privacy-policy-page,
    .terms-of-use-page {
        padding: 2rem 0.5rem;
    }
    .privacy-content-container,
    .terms-content-container {
        padding: 1.5rem;
    }
    .privacy-content-container h1,
    .terms-content-container h1 {
        font-size: 1.8rem;
    }
    .privacy-content-container h2,
    .terms-content-container h2 {
        font-size: 1.4rem;
    }
    .privacy-content-container h3,
    .terms-content-container h3 {
        font-size: 1.2rem;
    }
    .terms-content h2 { /* From original */
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .privacy-content-container,
    .terms-content-container {
        padding: 1rem;
        border-radius: 4px; /* Use variable */
    }
    .privacy-content-container h1,
    .terms-content-container h1 {
        font-size: 1.6rem;
    }
     .privacy-content-container h2,
     .terms-content-container h2 {
        font-size: 1.3rem;
    }
     .privacy-content-container h3,
     .terms-content-container h3 {
        font-size: 1.1rem;
    }
}


/* ==========================================
   Animation Styles (Definitions & Base)
========================================== */

/* Keyframes */
/* (Included fadeInUp earlier with Hero section) */


/* Base Scroll Animation Class */
.animate-on-scroll {
    opacity: 0; /* Start hidden */
    transform: translateY(20px); /* Start slightly lower */
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0); /* Animate to original position */
}

/* Add submenu background styling for desktop */
@media (min-width: 678px) {
    .nav-item.has-mega-menu {
        position: relative;
    }

    .mega-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: #ffffff;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-radius: 4px;
        z-index: 1000;
        min-width: 250px;
    }

    .nav-item.has-mega-menu:hover .mega-menu {
        display: block;
    }

    .mega-menu-content {
        padding: 15px 0;
    }

    .mega-menu-column h4 {
        padding: 0 20px 10px;
        margin: 0;
        color: var(--text-color);
        font-size: 1rem;
        border-bottom: 1px solid #eee;
    }

    .mega-menu-links {
        list-style: none;
        padding: 10px 0;
        margin: 0;
    }

    .mega-menu-links li {
        margin: 0;
        padding: 0;
        display: block;
        width: 100%;
    }

    .mega-menu-links a {
        display: block;
        padding: 8px 20px;
        color: var(--text-color);
        text-decoration: none;
        transition: background-color 0.2s ease;
        width: 100%;
        white-space: nowrap;
    }

    .mega-menu-links a:hover {
        background-color: #f5f5f5;
    }

    /* Remove any background from nav items */
    .nav-item > a,
    .nav-link {
        background: none;
    }

    .nav-item > a:hover {
        background: none;
    }
}

/* Utility styles potentially used within sections */
.lead-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--dark-grey);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.section-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem auto;
    width: 100%;
    max-width: 200px;
    position: relative;
}

.section-divider::before,
.section-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--primary-blue);
    opacity: 0.3;
}

.section-divider span {
    padding: 0 15px;
    color: var(--primary-blue);
    font-size: 1.2rem;
}

/* Add submenu background styling for desktop */
@media (min-width: 678px) {
    .mega-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 200px;
        background-color: #ffffff;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-radius: 4px;
        padding: 15px 0;
        z-index: 1000;
    }

    .mega-menu-content {
        width: 100%;
        background-color: #ffffff;
        padding: 0;
    }

    .mega-menu-column {
        width: 100%;
        background-color: #ffffff;
        padding: 0;
    }

    .mega-menu-column ul {
        list-style: none;
        padding: 0;
        margin: 0;
        background-color: #ffffff;
    }

    .mega-menu-column li {
        background-color: #ffffff;
    }

    .mega-menu-column a {
        display: block;
        padding: 8px 20px;
        color: var(--text-color);
        transition: background-color 0.2s ease;
        background-color: #ffffff;
    }

    .mega-menu-column a:hover {
        background-color: #f5f5f5;
    }

    .nav-item {
        position: relative;
    }
}

/* END OF FILE sections.css */
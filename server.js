/**
 * Standalone Node.js Server for NP Labs Blog Admin
 * 
 * This server provides the blog admin API for generic Unix hosting environments
 * like SiteGround, DigitalOcean, or any VPS with Node.js support.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const multer = require('multer');
const sharp = require('sharp');

const app = express();
const PORT = process.env.PORT || 3000;

// Configuration
const CONFIG = {
    adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    blogPath: path.join(__dirname, 'blog'),
    postsPath: path.join(__dirname, 'blog', 'posts'),
    imagesPath: path.join(__dirname, 'blog', 'assets', 'images'),
    uploadsPath: path.join(__dirname, 'uploads'),
    maxFileSize: 5 * 1024 * 1024 // 5MB
};

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(__dirname));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage,
    limits: {
        fileSize: CONFIG.maxFileSize
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only JPG, PNG, and WebP are allowed.'));
        }
    }
});

// Simple JWT implementation
const JWT = {
    sign(payload, secret) {
        const header = { alg: 'HS256', typ: 'JWT' };
        const now = Math.floor(Date.now() / 1000);
        const exp = now + (24 * 60 * 60); // 24 hours
        
        const jwtPayload = { ...payload, iat: now, exp };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
        const encodedPayload = Buffer.from(JSON.stringify(jwtPayload)).toString('base64url');
        
        const signature = crypto
            .createHmac('sha256', secret)
            .update(`${encodedHeader}.${encodedPayload}`)
            .digest('base64url');
        
        return `${encodedHeader}.${encodedPayload}.${signature}`;
    },
    
    verify(token, secret) {
        try {
            const [header, payload, signature] = token.split('.');
            
            const expectedSignature = crypto
                .createHmac('sha256', secret)
                .update(`${header}.${payload}`)
                .digest('base64url');
            
            if (signature !== expectedSignature) {
                throw new Error('Invalid signature');
            }
            
            const decodedPayload = JSON.parse(Buffer.from(payload, 'base64url').toString());
            
            if (decodedPayload.exp < Math.floor(Date.now() / 1000)) {
                throw new Error('Token expired');
            }
            
            return decodedPayload;
        } catch (error) {
            throw new Error('Invalid token');
        }
    }
};

// Authentication middleware
function requireAuth(req, res, next) {
    try {
        const token = req.headers.authorization;
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        
        const bearerToken = token.startsWith('Bearer ') ? token.slice(7) : token;
        const decoded = JWT.verify(bearerToken, CONFIG.jwtSecret);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }
}

// Utility functions
function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
    } catch (error) {
        await fs.mkdir(dirPath, { recursive: true });
    }
}

// API Routes

// Authentication
app.post('/api/admin/auth/login', async (req, res) => {
    try {
        const { password } = req.body;
        
        if (password === CONFIG.adminPassword) {
            const token = JWT.sign({ role: 'admin' }, CONFIG.jwtSecret);
            res.json({ success: true, token });
        } else {
            res.status(401).json({ success: false, message: 'Invalid password' });
        }
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Get blog statistics
app.get('/api/admin/stats', requireAuth, async (req, res) => {
    try {
        const indexPath = path.join(CONFIG.blogPath, 'index.html');
        const indexContent = await fs.readFile(indexPath, 'utf8');
        
        const postMatches = indexContent.match(/class="post-card"/g) || [];
        const featuredMatches = indexContent.match(/class="featured-post-card"/g) || [];
        
        // Count images
        let imageCount = 0;
        try {
            const imageFiles = await fs.readdir(CONFIG.imagesPath);
            imageCount = imageFiles.filter(file => 
                file.match(/\.(jpg|jpeg|png|webp)$/i)
            ).length;
        } catch (error) {
            console.warn('Could not count images:', error.message);
        }
        
        const stats = {
            totalPosts: postMatches.length + featuredMatches.length,
            featuredPosts: featuredMatches.length,
            totalImages: imageCount,
            categories: {
                health: 4,
                wellness: 3,
                medicine: 5,
                research: 3
            }
        };
        
        res.json(stats);
    } catch (error) {
        console.error('Stats error:', error);
        res.status(500).json({ error: 'Failed to get blog stats' });
    }
});

// Get posts list
app.get('/api/admin/posts', requireAuth, async (req, res) => {
    try {
        const postsDir = await fs.readdir(CONFIG.postsPath);
        const posts = [];
        
        for (const file of postsDir) {
            if (file.endsWith('.html') && !file.startsWith('_')) {
                const postPath = path.join(CONFIG.postsPath, file);
                const content = await fs.readFile(postPath, 'utf8');
                const stats = await fs.stat(postPath);
                
                // Extract title from HTML
                const titleMatch = content.match(/<title>(.*?)<\/title>/);
                const title = titleMatch ? titleMatch[1].split(' | ')[0] : 'Untitled';
                
                // Extract category
                const categoryMatch = content.match(/<span class="post-category">(.*?)<\/span>/);
                const category = categoryMatch ? categoryMatch[1] : 'Uncategorized';
                
                posts.push({
                    slug: file.replace('.html', ''),
                    title,
                    category,
                    filename: file,
                    lastModified: stats.mtime,
                    size: stats.size
                });
            }
        }
        
        // Sort by last modified (newest first)
        posts.sort((a, b) => b.lastModified - a.lastModified);
        
        res.json(posts);
    } catch (error) {
        console.error('Posts list error:', error);
        res.status(500).json({ error: 'Failed to get posts list' });
    }
});

// Create new post
app.post('/api/admin/posts', requireAuth, async (req, res) => {
    try {
        const postData = req.body;
        
        // Validate required fields
        if (!postData.title || !postData.content || !postData.category) {
            return res.status(400).json({ error: 'Title, content, and category are required' });
        }
        
        const slug = postData.slug || generateSlug(postData.title);
        const filename = `${slug}.html`;
        const postPath = path.join(CONFIG.postsPath, filename);
        
        // Check if post already exists
        try {
            await fs.access(postPath);
            return res.status(409).json({ error: 'Post with this slug already exists' });
        } catch (error) {
            // File doesn't exist, which is what we want
        }
        
        // Generate post HTML (simplified template)
        const postHTML = generatePostHTML(postData, slug);
        
        // Write post file
        await fs.writeFile(postPath, postHTML);
        
        // Log success
        console.log(`Created new post: ${slug}`);
        
        res.status(201).json({ 
            slug, 
            filename, 
            success: true,
            message: 'Post created successfully'
        });
        
    } catch (error) {
        console.error('Create post error:', error);
        res.status(500).json({ error: 'Failed to create post' });
    }
});

// Update existing post
app.put('/api/admin/posts/:slug', requireAuth, async (req, res) => {
    try {
        const { slug } = req.params;
        const postData = req.body;
        
        const filename = `${slug}.html`;
        const postPath = path.join(CONFIG.postsPath, filename);
        
        // Check if post exists
        try {
            await fs.access(postPath);
        } catch (error) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        // Generate updated HTML
        const postHTML = generatePostHTML(postData, slug);
        
        // Write updated file
        await fs.writeFile(postPath, postHTML);
        
        console.log(`Updated post: ${slug}`);
        
        res.json({ 
            slug, 
            filename, 
            success: true,
            message: 'Post updated successfully'
        });
        
    } catch (error) {
        console.error('Update post error:', error);
        res.status(500).json({ error: 'Failed to update post' });
    }
});

// Delete post
app.delete('/api/admin/posts/:slug', requireAuth, async (req, res) => {
    try {
        const { slug } = req.params;
        const filename = `${slug}.html`;
        const postPath = path.join(CONFIG.postsPath, filename);
        
        // Delete post file
        await fs.unlink(postPath);
        
        console.log(`Deleted post: ${slug}`);
        
        res.json({ 
            success: true,
            message: 'Post deleted successfully'
        });
        
    } catch (error) {
        console.error('Delete post error:', error);
        if (error.code === 'ENOENT') {
            res.status(404).json({ error: 'Post not found' });
        } else {
            res.status(500).json({ error: 'Failed to delete post' });
        }
    }
});

// Upload image
app.post('/api/admin/upload', requireAuth, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }
        
        // Ensure images directory exists
        await ensureDirectoryExists(CONFIG.imagesPath);
        
        // Generate unique filename
        const timestamp = Date.now();
        const originalName = req.file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
        const filename = `${timestamp}_${originalName}`;
        const filepath = path.join(CONFIG.imagesPath, filename);
        
        // Optimize image with Sharp
        await sharp(req.file.buffer)
            .resize(1200, 630, { 
                fit: 'cover',
                withoutEnlargement: true 
            })
            .jpeg({ quality: 85 })
            .toFile(filepath);
        
        const imageUrl = `/blog/assets/images/${filename}`;
        
        console.log(`Uploaded image: ${filename}`);
        
        res.json({
            success: true,
            filename,
            url: imageUrl,
            size: req.file.size
        });
        
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ error: 'Failed to upload image' });
    }
});

// Generate post HTML template
function generatePostHTML(postData, slug) {
    const categoryNames = {
        health: 'Health',
        wellness: 'Wellness',
        medicine: 'Personalized Medicine',
        research: 'Research'
    };
    
    const categoryName = categoryNames[postData.category] || postData.category;
    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/blog/posts/${slug}.html">
    <title>${postData.title} | NP Labs Blog</title>
    <meta name="description" content="${postData.metaDescription || postData.title}">
    <meta name="keywords" content="${postData.metaKeywords || 'health, wellness, compounding pharmacy'}">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="${postData.title}">
    <meta property="og:description" content="${postData.metaDescription || postData.title}">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://www.nplabs.com/blog/posts/${slug}.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../core.css">
    <link rel="stylesheet" href="../../components.css">
    <link rel="stylesheet" href="../../sections.css">
    <link rel="stylesheet" href="../../footer.css">
    <link rel="stylesheet" href="../../css/blog.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div id="header-placeholder"></div>
    
    <main id="main-content">
        <!-- Blog Post Header -->
        <section class="blog-post-header">
            <div class="container">
                <h1 class="blog-post-title">${postData.title}</h1>
                <div class="blog-post-meta">
                    <span class="post-category">${categoryName}</span>
                    <span class="post-date">${currentDate}</span>
                    <span class="post-author">By NP Labs Team</span>
                </div>
            </div>
        </section>
        
        <!-- Blog Post Content -->
        <article class="blog-post-content">
            ${postData.content}
            
            ${postData.tags && postData.tags.length > 0 ? `
            <div class="blog-post-tags">
                <span class="tag-label">Tags:</span>
                ${postData.tags.map(tag => `<a href="#" class="tag">${tag}</a>`).join('')}
            </div>
            ` : ''}
        </article>
    </main>
    
    <div id="footer-placeholder"></div>
    
    <!-- Scripts -->
    <script src="../../js/navigation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function loadHTML(elementId, filePath) {
                fetch(filePath)
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById(elementId).innerHTML = data;
                        if (elementId === 'header-placeholder' && typeof initializeNavigation === 'function') {
                            initializeNavigation();
                        }
                    });
            }
            
            loadHTML('header-placeholder', '_header.html');
            loadHTML('footer-placeholder', '_footer.html');
        });
    </script>
</body>
</html>`;
}

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 5MB.' });
        }
    }
    
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log(`NP Labs Blog Admin Server running on port ${PORT}`);
    console.log(`Admin interface: http://localhost:${PORT}/admin/`);
    console.log(`Blog: http://localhost:${PORT}/blog/`);
});

module.exports = app;

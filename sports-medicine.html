<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/sports-medicine.html">
    <title>Sports Medicine Compounding | Athletic Performance Solutions | NP Labs</title>
    <meta name="description" content="NP Labs provides customized sports medicine compounding services for athletes, including pain relief formulations, inflammation control, recovery support, and personalized nutritional supplements.">
    <meta name="keywords" content="sports medicine compounding, athletic performance, pain relief for athletes, anti-inflammatory compounds, recovery formulations, sports nutrition, compounded medications for athletes">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Sports Medicine Compounding | Athletic Performance Solutions | NP Labs">
    <meta property="og:description" content="NP Labs provides customized sports medicine compounding services for athletes, including pain relief formulations, inflammation control, recovery support, and personalized nutritional supplements.">
    <meta property="og:image" content="https://www.nplabs.com/images/sports-medicine-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/sports-medicine.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Sports Medicine Compounding | Athletic Performance Solutions | NP Labs">
    <meta name="twitter:description" content="NP Labs provides customized sports medicine compounding services for athletes, including pain relief formulations, inflammation control, recovery support, and personalized nutritional supplements.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/sports-medicine-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Page-specific styles - Adopted from veterinary-compounding.html */
        .sports-medicine-hero {
            background: url('images/sports-medicine-hero-placeholder.webp') no-repeat center center/cover; /* Placeholder */
            padding: 0; /* Set padding to 0 */
            text-align: center;
            position: relative;
        }
        .sports-medicine-hero .page-header-overlay {
            background-color: rgba(0, 123, 255, 0.6); /* Adjusted overlay color for sports med (primary blue) */
        }
        .sports-medicine-hero h1,
        .sports-medicine-hero p.lead-text {
            color: #ffffff !important;
        }
        .content-section {
            padding: 60px 0;
        }
        /* Consistent Service Detail Section Styling */
        .service-detail-section {
            margin-bottom: 40px;
            padding: 25px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 5px solid var(--primary-color); /* Using primary color for sports med */
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        }
        .service-detail-section h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
        }
         .service-detail-section h3 i {
             margin-right: 10px;
        }
         .service-detail-section ul {
             list-style: none;
             padding-left: 0;
             margin-top: 15px;
        }
          .service-detail-section li {
             margin-bottom: 10px;
             padding-left: 25px;
             position: relative;
             line-height: 1.6;
         }
         .service-detail-section li::before {
             content: '\f058'; /* Font Awesome check-circle icon */
             font-family: 'Font Awesome 6 Free';
             font-weight: 900;
             color: var(--primary-color);
             position: absolute;
             left: 0;
             top: 1px;
         }
        /* Style for content image */
        .content-image {
            display: block;
            margin: 30px auto;
            max-width: 75%; /* Adjusted width */
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        /* Consistent CTA Style */
        .sports-medicine-cta {
            background-color: var(--primary-color-light);
            color: var(--primary-color-dark);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 40px;
            margin-bottom: 20px;
            border: 1px solid var(--primary-color);
        }
         .sports-medicine-cta strong {
             font-weight: 700;
        }
    </style>
</head>
<body>

<div id="header-placeholder"></div>

<main id="main-content">
    <!-- Hero Section -->
    <section class="page-header sports-medicine-hero">
        <div class="page-header-overlay"></div>
        <div class="container position-relative">
            <h1 data-aos="fade-up">Compounded Sports Medicine</h1>
            <p class="lead-text" data-aos="fade-up" data-aos-delay="200">Personalized Solutions for Peak Performance & Recovery</p>
        </div>
    </section>

    <!-- Main Content Section -->
    <section class="content-section">
        <div class="container">
            <div class="text-center" data-aos="fade-up">
                 <h2>Supporting Athletes with <span class="highlight">Customized Formulations</span></h2>
                 <p class="section-subtitle">Compounding pharmacies play a vital role in sports medicine by creating customized medications to meet the specific needs of athletes, aiding in performance, recovery, and injury management.</p>
                 <!-- Updated Image -->
                 <img src="images/Sports-Medicine.jpg" alt="Sports medicine concept with athlete" class="content-image" data-aos="fade-up" data-aos-delay="50">
            </div>

            <!-- Custom Doses Section -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="100">
                 <h3><i class="fas fa-ruler-combined"></i> Custom Sports Medicine Doses</h3>
                 <p>Every athlete is unique. We compound medications to the exact strength and dosage form required by the individual, ensuring optimal efficacy and minimizing potential side effects. This is particularly useful when standard commercial dosages are not suitable.</p>
            </div>

            <!-- Topical Pain/Anti-Inflammatories Section -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="200">
                 <h3><i class="fas fa-paint-roller"></i> Topical Pain Relievers & Anti-inflammatories</h3>
                 <p>Applying medication directly to the site of pain or injury can reduce systemic absorption and side effects. We create custom topical formulations such as:</p>
                 <ul>
                     <li>Gels, creams, or sprays containing analgesics (pain relievers) and/or anti-inflammatory agents (NSAIDs, corticosteroids).</li>
                     <li>Formulations designed for enhanced absorption through the skin to target affected tissues directly.</li>
                     <li>Combinations of multiple active ingredients in a single topical preparation for convenience.</li>
                 </ul>
            </div>

            <!-- Combined Doses Section -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="300">
                 <h3><i class="fas fa-layer-group"></i> Combined Sports Medicine Doses</h3>
                 <p>Athletes may need multiple medications. We can simplify regimens by combining compatible medications into a single dosage form, like a capsule or topical cream, improving adherence and convenience.</p>
            </div>

            <!-- Antifungal Medications Section -->
            <div class="service-detail-section" data-aos="fade-up" data-aos-delay="400">
                 <h3><i class="fas fa-bacteria"></i> Compounded Antifungal Medications</h3>
                 <p>Conditions like athlete's foot require effective treatment. We can compound antifungal medications in various bases (creams, powders, solutions) and strengths, sometimes combining them with other agents to manage symptoms like itching or inflammation.</p>
            </div>

            <!-- CTA Section -->
            <div class="sports-medicine-cta text-center" data-aos="fade-up">
                <p><strong>Maximize your potential. Ask your physician or trainer about how personalized compounded medications from NP Labs can support your athletic goals and recovery.</strong></p>
                <a href="contact.html" class="btn btn-primary">Discuss Your Needs</a>
            </div>

        </div> <!-- End Container -->
    </section> <!-- End Content Section -->

</main>

<div id="footer-placeholder"></div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
    });
</script>
<script src="js/include-html.js" defer></script>

</body>
</html>

/* Terms of Use Page Styles */
.terms-hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-teal) 100%);
    padding: 0rem 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.terms-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('assets/pattern-bg.png') repeat;
    opacity: 0.1;
    z-index: 1;
}

.terms-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.terms-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
    line-height: 1.2;
}

.terms-hero .lead-text {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.terms-hero .breadcrumb {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0 0 1rem;
    font-size: 0.95rem;
}

.terms-hero .breadcrumb li {
    display: flex;
    align-items: center;
}

.terms-hero .breadcrumb li:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.terms-hero .breadcrumb a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-hero .breadcrumb a:hover {
    color: white;
}

.terms-hero .breadcrumb .current {
    color: rgba(255, 255, 255, 0.7);
}

.terms-of-use-page {
    background-color: #f8f9fa;
    padding: 3rem 0;
    margin-top: 0;
}

.terms-content-container {
    max-width: 900px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 3rem 2rem;
}

.terms-content-container .page-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.terms-content-container .page-header:after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background-color: var(--secondary-teal);
    margin: 1.5rem auto 0;
    border-radius: 2px;
}

.terms-content-container h1 {
    color: var(--primary-blue);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.terms-content {
    color: #495057;
    font-size: 1.1rem;
    line-height: 1.8;
}

.terms-content h2 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    margin: 3rem 0 1.5rem;
    text-align: left;
    position: relative;
    padding-bottom: 0.5rem;
}

.terms-content h2:after {
    content: '';
    display: block;
    width: 40px;
    height: 3px;
    background-color: var(--secondary-teal);
    margin-top: 0.5rem;
    border-radius: 2px;
}

.terms-content p {
    margin-bottom: 1.5rem;
    color: #495057;
}

.terms-content ul {
    list-style: none;
    padding-left: 0;
    margin: 1.5rem 0;
}

.terms-content li {
    margin-bottom: 1rem;
    position: relative;
    color: inherit;
}

.terms-content li::before {
    content: '';
}

.terms-content strong {
    color: var(--primary-blue);
    font-weight: 600;
}

.terms-content a {
    text-decoration: none;
    color: inherit;
}

body {
    padding: 1rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 992px) {
    .terms-content-container {
        margin: 0 1rem;
    }

    .terms-content-container h1 {
        font-size: 2.2rem;
    }

    .terms-content h2 {
        font-size: 1.6rem;
    }
}

@media (max-width: 768px) {
    .terms-of-use-page {
        padding: 1rem 0;
    }

    .terms-content-container {
        padding: 2rem 1.5rem;
        margin: 0 1rem;
    }

    .terms-content-container h1 {
        font-size: 2rem;
    }

    .terms-content {
        font-size: 1rem;
    }

    .terms-content h2 {
        font-size: 1.4rem;
        margin: 2rem 0 1rem;
    }
}

@media (max-width: 576px) {
    .terms-content-container {
        padding: 1.5rem 1rem;
        margin: 0 0.5rem;
    }

    .terms-content-container h1 {
        font-size: 1.8rem;
    }
}

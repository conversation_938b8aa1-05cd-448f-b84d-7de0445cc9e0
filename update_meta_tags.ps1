# PowerShell script to update meta tags in HTML files
# This script reads from a CSV file with meta tag information and updates the HTML files

# Define the path to the CSV file with meta tag information
$csvPath = "meta_tags_data.csv"

# Check if the CSV file exists
if (-not (Test-Path $csvPath)) {
    Write-Host "Error: Meta tags data file not found at $csvPath"
    Write-Host "Please create a CSV file with the following columns:"
    Write-Host "FilePath,Title,Description,Keywords,CanonicalURL,OGTitle,OGDescription,OGImage,TwitterTitle,TwitterDescription,TwitterImage"
    exit
}

# Read the CSV file
$metaData = Import-Csv -Path $csvPath

# Process each row in the CSV
foreach ($page in $metaData) {
    $filePath = $page.FilePath
    
    # Check if the file exists
    if (-not (Test-Path $filePath)) {
        Write-Host "Warning: File not found: $filePath"
        continue
    }
    
    # Read the HTML content
    $content = Get-Content -Path $filePath -Raw
    
    # Update or add meta tags
    
    # 1. Update title tag
    if ($page.Title -and $page.Title -ne "") {
        if ($content -match '<title>(.*?)</title>') {
            $content = $content -replace '<title>(.*?)</title>', "<title>$($page.Title)</title>"
        } else {
            # If no title tag exists, add it after the head tag
            $content = $content -replace '<head>', "<head>`n    <title>$($page.Title)</title>"
        }
    }
    
    # 2. Update or add description meta tag
    if ($page.Description -and $page.Description -ne "") {
        if ($content -match '<meta name="description" content="(.*?)"') {
            $content = $content -replace '<meta name="description" content="(.*?)"', "<meta name=`"description`" content=`"$($page.Description)`""
        } else {
            # If no description meta tag exists, add it after the title tag
            $content = $content -replace '</title>', "</title>`n    <meta name=`"description`" content=`"$($page.Description)`">"
        }
    }
    
    # 3. Add keywords meta tag if it doesn't exist
    if ($page.Keywords -and $page.Keywords -ne "") {
        if (-not ($content -match '<meta name="keywords"')) {
            $content = $content -replace '</title>', "</title>`n    <meta name=`"keywords`" content=`"$($page.Keywords)`">"
        }
    }
    
    # 4. Add canonical URL tag if it doesn't exist
    if ($page.CanonicalURL -and $page.CanonicalURL -ne "") {
        if (-not ($content -match '<link rel="canonical"')) {
            $content = $content -replace '<meta name="viewport"', "<link rel=`"canonical`" href=`"$($page.CanonicalURL)`">`n    <meta name=`"viewport`""
        }
    }
    
    # 5. Add Open Graph tags if they don't exist
    if ($page.OGTitle -and $page.OGTitle -ne "") {
        if (-not ($content -match '<meta property="og:title"')) {
            $ogTags = "`n    <!-- Open Graph Tags -->`n"
            $ogTags += "    <meta property=`"og:title`" content=`"$($page.OGTitle)`">`n"
            
            if ($page.OGDescription -and $page.OGDescription -ne "") {
                $ogTags += "    <meta property=`"og:description`" content=`"$($page.OGDescription)`">`n"
            }
            
            if ($page.OGImage -and $page.OGImage -ne "") {
                $ogTags += "    <meta property=`"og:image`" content=`"$($page.OGImage)`">`n"
            }
            
            if ($page.CanonicalURL -and $page.CanonicalURL -ne "") {
                $ogTags += "    <meta property=`"og:url`" content=`"$($page.CanonicalURL)`">`n"
            }
            
            $ogTags += "    <meta property=`"og:type`" content=`"website`">`n"
            $ogTags += "    <meta property=`"og:site_name`" content=`"NP Labs`">"
            
            # Add OG tags before the first stylesheet link
            $content = $content -replace '(<link rel="stylesheet"|<link rel="preconnect"|<link href=)', "$ogTags`n    $1"
        }
    }
    
    # 6. Add Twitter Card tags if they don't exist
    if ($page.TwitterTitle -and $page.TwitterTitle -ne "") {
        if (-not ($content -match '<meta name="twitter:card"')) {
            $twitterTags = "`n    <!-- Twitter Card Tags -->`n"
            $twitterTags += "    <meta name=`"twitter:card`" content=`"summary_large_image`">`n"
            $twitterTags += "    <meta name=`"twitter:title`" content=`"$($page.TwitterTitle)`">`n"
            
            if ($page.TwitterDescription -and $page.TwitterDescription -ne "") {
                $twitterTags += "    <meta name=`"twitter:description`" content=`"$($page.TwitterDescription)`">`n"
            }
            
            if ($page.TwitterImage -and $page.TwitterImage -ne "") {
                $twitterTags += "    <meta name=`"twitter:image`" content=`"$($page.TwitterImage)`">`n"
            }
            
            # Add Twitter Card tags before the first stylesheet link or after OG tags
            if ($content -match '<meta property="og:site_name"') {
                $content = $content -replace '<meta property="og:site_name" content="NP Labs">', "<meta property=`"og:site_name`" content=`"NP Labs`">$twitterTags"
            } else {
                $content = $content -replace '(<link rel="stylesheet"|<link rel="preconnect"|<link href=)', "$twitterTags`n    $1"
            }
        }
    }
    
    # Save the updated content back to the file
    Set-Content -Path $filePath -Value $content
    
    Write-Host "Updated meta tags for: $filePath"
}

Write-Host "Meta tag update complete!"

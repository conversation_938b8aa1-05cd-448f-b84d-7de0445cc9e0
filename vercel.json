{"version": 2, "name": "nplabs-blog-admin", "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/admin/(.*)", "dest": "/api/admin/$1"}, {"src": "/admin", "dest": "/admin/index.html"}, {"src": "/admin/(.*)", "dest": "/admin/$1"}, {"src": "/blog", "dest": "/blog/index.html"}, {"src": "/blog/(.*)", "dest": "/blog/$1"}, {"src": "/(.*)\\.html", "dest": "/$1", "status": 301}, {"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.quilljs.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.quilljs.com https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob:; connect-src 'self';"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}]}], "env": {"ADMIN_PASSWORD": "@admin-password", "JWT_SECRET": "@jwt-secret"}, "functions": {"api/**/*.js": {"maxDuration": 30}}}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.nplabs.com/where-we-ship.html">
    <title>Shipping & Delivery Information | NP Labs Compounding Pharmacy</title>
    <meta name="description" content="Learn about NP Labs' shipping coverage and delivery services for compounded medications. Find out if we ship to your location in Greece, the EU, and internationally.">
    <meta name="keywords" content="compounding pharmacy shipping, medication delivery, international shipping, EU medication shipping, compounded medication delivery, pharmacy delivery services, NP Labs shipping">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Shipping & Delivery Information | NP Labs Compounding Pharmacy">
    <meta property="og:description" content="Learn about NP Labs' shipping coverage and delivery services. Find out if we ship to your location.">
    <meta property="og:image" content="https://www.nplabs.com/images/nplabs-shipping-og.jpg">
    <meta property="og:url" content="https://www.nplabs.com/where-we-ship.html">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="NP Labs">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Shipping & Delivery Information | NP Labs Compounding Pharmacy">
    <meta name="twitter:description" content="Learn about NP Labs' shipping coverage and delivery services. Find out if we ship to your location.">
    <meta name="twitter:image" content="https://www.nplabs.com/images/nplabs-shipping-twitter.jpg">

    <!-- External Libraries First -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">
    <!-- Custom Stylesheets -->
    <link rel="stylesheet" href="core.css">
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="sections.css">
    <link rel="stylesheet" href="footer.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- AOS Library for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Specific styles for Where We Ship page */
        .page-header.shipping-hero {
            background: linear-gradient(rgba(0, 80, 158, 0.6), rgba(0, 80, 158, 0.6)), url('images/weship.png') no-repeat center center/cover;
            background-color: #00509e; /* Fallback color */
        }

        .page-header.shipping-hero h1 {
            color: white; /* Ensure hero title is white */
        }

        .shipping-intro-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Responsive grid */
            gap: 30px;
            align-items: center;
            margin-bottom: 40px; /* Add space before next section */
        }

        .shipping-intro-image img {
            width: 100%;
            max-width: 450px; /* Control max size */
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: block;
            margin: 0 auto;
        }

        .countries-section {
            background-color: #f9f9f9;
            padding: 60px 0;
            margin-bottom: 60px;
        }

        .countries-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* Responsive grid for countries */
            gap: 20px;
            list-style: none;
            padding-left: 0;
        }

        .countries-grid li {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.07);
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .countries-grid li:hover {
            transform: translateY(-3px);
            background-color: var(--secondary-color-light);
            color: var(--primary-color);
        }

        .shipping-options {
            text-align: center;
            margin-bottom: 60px;
            padding: 30px;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .shipping-options h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

    </style>
</head>
<body>

    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Hero Section -->
        <section class="page-header shipping-hero text-white text-center">
            <div class="page-header-overlay"></div>
            <div class="container">
                <h1 data-aos="fade-up">Where We Ship</h1>
                <p data-aos="fade-up" data-aos-delay="200">Delivering Personalized Medicine Across Europe and Beyond</p>
            </div>
        </section>

        <!-- Intro Section -->
        <section class="content-section">
            <div class="container">
                <div class="shipping-intro-grid">
                    <div class="shipping-intro-content" data-aos="fade-right">
                        <h2>Global Reach, Local Care</h2>
                        <p>NP Labs is proud to be one of the first in Europe to prepare and manufacture listed hormones, holding exclusive rights across the continent. We are committed to making personalized medicine accessible, exporting our high-quality products worldwide.</p>
                    </div>
                    <div class="shipping-intro-image" data-aos="fade-left">
                        <img src="images/weship.png" alt="NP Labs Shipping Global Reach">
                    </div>
                </div>
            </div>
        </section>

        <!-- Countries Section -->
        <section class="countries-section">
            <div class="container">
                <h2 class="text-center mb-5" data-aos="fade-up">Some of the Countries We Currently Serve</h2>
                <ul class="countries-grid" data-aos="fade-up">
                    <!-- List extracted from content -->
                    <li>UK</li>
                    <li>Greece</li>
                    <li>Sweden</li>
                    <li>Czech Republic</li>
                    <li>Hungary</li>
                    <li>Italy</li>
                    <li>France</li>
                    <li>Spain</li>
                    <li>Norway</li>
                    <li>Cyprus</li>
                    <li>Portugal</li>
                    <li>Turkey</li>
                    <li>Egypt</li>
                    <li>Russia</li>
                    <li>Ukraine</li>
                    <li>UAE</li>
                    <li>Mexico</li>
                    <li>Belize</li>
                    <li>USA</li>
                    <li>Australia</li>
                    <li>New Zealand</li>
                </ul>
            </div>
        </section>

        <!-- Shipping Options Section -->
        <section class="content-section">
            <div class="container">
                <div class="shipping-options" data-aos="fade-up">
                    <h3>Reliable Shipping Options</h3>
                    <p>We offer several shipping options tailored to your needs. You can rest assured that your medications will arrive safely and on time, wherever you are.</p>
                    <p>For specific shipping details or inquiries, please <a href="contact.html">contact us</a>.</p>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
        });
    </script>
    <script src="scripts.js"></script>
    <script src="js/include-html.js" defer></script>

</body>
</html>
